package com.sgs.customerbiz.biz.customertrf.service;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.sgs.customerbiz.biz.customertrf.action.CustomerTrfImportActionExtPt;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabContactDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/7 10:39
 */
@Service
@Slf4j
@AllArgsConstructor
public class CustomerTrfDomainService {

    @Resource
    private ExtensionExecutor extensionExecutor;

    private static Map<Integer, String> REFSYSTEMID_IMPORT_SCENARIO = new HashMap<>();

    static {
        REFSYSTEMID_IMPORT_SCENARIO.put(2, "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.F21.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.JO_ANN.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.Walmart.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.Walmart_Group.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.Target.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.DollarTree.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.Veyer.getRefSystemId(), "GENERAL");
        REFSYSTEMID_IMPORT_SCENARIO.put(RefSystemIdEnum.BigLots.getRefSystemId(), "GENERAL");
//        REFSYSTEMID_IMPORT_SCENARIO.put(7, "GENERAL");
//        REFSYSTEMID_IMPORT_SCENARIO.put(10016, "GENERAL");
//        REFSYSTEMID_IMPORT_SCENARIO.put(10017, "GENERAL");
    }

    /**
     * 根据TrfNo 向指定系统refSystemId pull CustomerTrf(JSON)
     *
     * @param
     * @param
     * @return
     */
    public String getCustomerTrf(TrfImportReq importReq) {
//        Assert.notBlank(trfNo, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "TrfNo is required");
//        Assert.notNull(refSystemId, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId is required");

        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.CUSTOMERTRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(importReq.getTrfNo(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "TrfNo is required");
        ErrorAssert.notNull(importReq.getRefSystemId(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId is required");

        //TODO 增加业务校验
        /**
         * 1、refSystemId 有效性校验
         * 2、refSystemId 的 importAPI Config
         */

        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, BizScenario.DEFAULT_USE_CASE, String.valueOf(importReq.getRefSystemId()));
        return extensionExecutor.execute(
                CustomerTrfImportActionExtPt.class,
                bizScenario,
                customerTrfImportActionExtPt -> customerTrfImportActionExtPt.getCustomerTrf(importReq));
    }

    public TrfDTO convert(String customerTrfJson, Integer refSystemId, Integer systemId, String labCode, String buCode, String trfTemplateId,String templateType, String formId, String gridId, TrfLabContactDTO labContact) {
//        Assert.notBlank(customerTrfJson, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "customerTrfJson is required");
//        Assert.notNull(refSystemId, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId is required");
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.CUSTOMERTRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(customerTrfJson, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "customerTrfJson is required");
        ErrorAssert.notNull(refSystemId, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId is required");

        String scenario = getImportScenario(refSystemId);

        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, BizScenario.DEFAULT_USE_CASE, scenario);
        return extensionExecutor.execute(
                CustomerTrfImportActionExtPt.class,
                bizScenario,
                customerTrfImportActionExtPt -> customerTrfImportActionExtPt.convert(customerTrfJson, refSystemId, systemId, labCode, buCode, trfTemplateId,templateType, formId, gridId, labContact));
    }

    /**
     * 临时方案：20240112
     * 解决时间：20240121 （本周调整的DFF 方案，因为风险和测试时间等考虑，本周只上线Lows、TIC、Sgsmart，其他客户走原来的逻辑；下周目标全部统一，不再区分ImportMode ）
     *
     * @param refSystemId
     * @return
     */
    private String getImportScenario(Integer refSystemId) {
        String importTrfMode = REFSYSTEMID_IMPORT_SCENARIO.get(refSystemId);

        return Func.isNotEmpty(importTrfMode) ? importTrfMode : String.valueOf(refSystemId);
    }
}
