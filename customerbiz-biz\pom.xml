<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sgs-customerbiz</artifactId>
        <groupId>com.sgs.customerbiz</groupId>
        <version>1.0.22</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${customerbiz.version}</version>
    <artifactId>customerbiz-biz</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.customerbiz</groupId>
            <artifactId>customerbiz-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.customerbiz</groupId>
            <artifactId>customerbiz-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.customerbiz</groupId>
            <artifactId>customerbiz-dfv-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.customerbiz</groupId>
            <artifactId>customerbiz-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>
<!--        todo 这里要判断一下加入的影响-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
