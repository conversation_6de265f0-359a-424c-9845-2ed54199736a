package com.sgs.customerbiz.biz.service.preview;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingKey;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SgsTestLine implements TestMappingKey {

    private TrfTestLineDTO testLine;
    private TestLineMappingInfoV2DTO mapping;

    @Override
    public Integer getTestLineId() {
        return mapping.getTestLineId();
    }

    @Override
    public Integer getPpNo() {
        return mapping.getPpNo();
    }

    @Override
    public Integer getCitationId() {
        return mapping.getCitationId();
    }

    @Override
    public Integer getCitationType() {
        return mapping.getCitationType();
    }

    public boolean hasMapping() {
        return mapping != null;
    }

    public static SgsTestLine copyUsingJson(SgsTestLine sgsTestLine) {
        return JSON.parseObject(JSON.toJSONString(sgsTestLine), SgsTestLine.class);
    }
}
