package com.sgs.customerbiz.web;

import com.sgs.customerbiz.validation.props.ValidationProps;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.KafkaMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.Profile;

/**
 *
 */
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class
        , KafkaAutoConfiguration.class
        , KafkaMetricsAutoConfiguration.class
        , RedissonAutoConfiguration.class
        , RedisAutoConfiguration.class
        , RedisRepositoriesAutoConfiguration.class,
        SecurityAutoConfiguration.class
})

@ImportResource("classpath:applicationContext.xml")
@EnableAspectJAutoProxy
@EnableConfigurationProperties({
        ValidationProps.class
})
@ComponentScan(basePackages = {"com.sgs.tools", "com.sgs.customerbiz"})
public class CustomerBizApplication {
    public static void main(String[] args) {
        SpringApplication.run(CustomerBizApplication.class, args);
    }
}
