package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InspectorioTestLineInfoMapper {
    int countByExample(InspectorioTestLineInfoExample example);

    int deleteByExample(InspectorioTestLineInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(InspectorioTestLineInfoPO record);

    int insertSelective(InspectorioTestLineInfoPO record);

    List<InspectorioTestLineInfoPO> selectByExample(InspectorioTestLineInfoExample example);

    InspectorioTestLineInfoPO selectByPrimaryKey(Integer id);

    List<InspectorioTestLineInfoPO> page(@Param("offset") int offset, @Param("limit") int limit);

    int updateByExampleSelective(@Param("record") InspectorioTestLineInfoPO record, @Param("example") InspectorioTestLineInfoExample example);

    int updateByExample(@Param("record") InspectorioTestLineInfoPO record, @Param("example") InspectorioTestLineInfoExample example);

    int updateByPrimaryKeySelective(InspectorioTestLineInfoPO record);

    int updateByPrimaryKey(InspectorioTestLineInfoPO record);

    int batchInsert(List<InspectorioTestLineInfoPO> list);

    int batchUpdate(List<InspectorioTestLineInfoPO> list);
}