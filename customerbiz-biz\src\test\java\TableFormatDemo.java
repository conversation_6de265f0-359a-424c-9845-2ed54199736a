/**
 * 演示表格输出格式
 */
public class TableFormatDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 邮件内容示例 ===");
        System.out.println();
        
        // 模拟Package表格
        StringBuilder packageTable = new StringBuilder();
        packageTable.append("Package update list:\n\n");
        packageTable.append("Package Id\tPackage Name\tUpdate\n");
        packageTable.append("PKG001\tTest Package 1\tUpdated\n");
        packageTable.append("PKG002\tTest Package 2\tDelete\n");
        packageTable.append("PKG003\tTest Package 3\tNew\n");
        
        // 模拟Property表格
        StringBuilder propertyTable = new StringBuilder();
        propertyTable.append("Property update list:\n\n");
        propertyTable.append("Property Id\tProperty Name\tUpdate\n");
        propertyTable.append("PROP001\tTest Property 1\tUpdated\n");
        propertyTable.append("PROP002\tTest Property 2\tDelete\n");
        propertyTable.append("PROP003\tTest Property 3\tNew\n");
        
        // 输出完整的邮件内容
        System.out.println("Dear Team,");
        System.out.println();
        System.out.println("Inspectorio sync completed successfully.");
        System.out.println();
        System.out.println(packageTable.toString());
        System.out.println();
        System.out.println(propertyTable.toString());
        System.out.println();
        System.out.println("Best regards,");
        System.out.println("System");
        
        System.out.println();
        System.out.println("=== 空数据示例 ===");
        
        // 模拟空数据表格
        StringBuilder emptyPackageTable = new StringBuilder();
        emptyPackageTable.append("Package update list:\n\n");
        emptyPackageTable.append("Package Id\tPackage Name\tUpdate\n");
        emptyPackageTable.append(" \t \t \n");
        
        StringBuilder emptyPropertyTable = new StringBuilder();
        emptyPropertyTable.append("Property update list:\n\n");
        emptyPropertyTable.append("Property Id\tProperty Name\tUpdate\n");
        emptyPropertyTable.append(" \t \t \n");
        
        System.out.println(emptyPackageTable.toString());
        System.out.println();
        System.out.println(emptyPropertyTable.toString());
    }
}
