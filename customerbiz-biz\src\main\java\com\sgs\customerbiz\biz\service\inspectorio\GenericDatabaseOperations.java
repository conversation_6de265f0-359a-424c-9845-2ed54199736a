package com.sgs.customerbiz.biz.service.inspectorio;

import java.util.List;

/**
 * 泛型数据库操作封装类
 * @param <T> PO对象类型
 */
public class GenericDatabaseOperations<T> {
    public final List<T> toInsert;
    public final List<T> toUpdate;
    public final List<T> toDelete;
    
    public GenericDatabaseOperations(List<T> toInsert, List<T> toUpdate, List<T> toDelete) {
        this.toInsert = toInsert;
        this.toUpdate = toUpdate;
        this.toDelete = toDelete;
    }
} 