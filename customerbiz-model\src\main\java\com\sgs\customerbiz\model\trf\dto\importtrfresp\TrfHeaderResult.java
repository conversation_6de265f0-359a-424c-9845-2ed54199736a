package com.sgs.customerbiz.model.trf.dto.importtrfresp;

import com.sgs.customerbiz.model.trf.dto.TrfOtherDTO;
import com.sgs.customerbiz.model.trf.dto.TrfServiceRequirementDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/1 13:59
 */
@Data
public class TrfHeaderResult implements Serializable {

    private Integer refSystemId;

    private String trfNo;

    private Integer serviceType;

    /**
     * 样品Level，1 客户提供Component  2、SGS 拆样
     */
    private Integer sampleLevel;
    /**
     * TRF 开单方式：TRF开单、单样品开单
     * 目前TIC在用
     */
    private Integer trfReportLevel;
    // add 230620
    private List<String> parcelNoList;
    // add 230620
    private Date trfSubmissionDate;

    private Date trfExpectDueDate;
    // add 230620
    private Integer selfTestFlag;
    // add 230620
    private TrfOtherDTO others;

    private TrfLabResult lab;

    /**
     * json
     */
    private Object extFields;

    private Object customerTrfContent;

}
