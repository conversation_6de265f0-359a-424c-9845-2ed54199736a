package com.sgs.customerbiz.core.errorcode.enums;

public enum ErrorTypeEnum {
    REQUESTNULL("01", "请求为空"),
    REQUIREDMISSING("02", "缺少必需字段"),
    DATANOTFOUND("03", "数据不存在"),
    DATACONFLICT("04", "数据冲突"),
    STATUSERROR("05", "状态错误"),
    REVISE_STATUS_ERROR("05100", "状态错误"),
    CONFIGERROR("06", "配置错误"),
    LOGICERROR("07", "逻辑错误"),
    EXTERNALCALLFAILED("08", "调用失败"),
    PARAMETERERROR("09", "参数错误"),
    //数据不一致
    DATANOTMATCH("10", "数据不一致"),
    SYSTEMERROR("11","系统错误"),
    DUPLICATEDATA("12","数据重复"), 
    CONFIG_ERROR("13","配置错误"),
    INVALID_STATUS("14","状态错误");


    private final String code;
    private final String description;

    ErrorTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ErrorTypeEnum fromCode(String code) {
        for (ErrorTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
