package com.sgs.customerbiz.model.trf.dto;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdMethodLimitDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportLimitDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultLanguageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TrfTestResultDTO implements Serializable {

    private String rootOrderNo;

    // add 20230529
    private String orderNo;

    private String realOrderNo;

    private String reportNo;
    private String testMatrixId;

    private String subReportNo;

    private Integer testResultSeq;


    private String testResultFullName;

    private TrfTestResultNameRelDTO testResultFullNameRel;

    private TrfTestResultValueDTO testResult;

    public String getTestResultFullName() {
        if (Func.isBlank(testResultFullName) && Func.isNotEmpty(testResult) && Func.isNotBlank(testResult.getTestResultFullName())) {
            return testResult.getTestResultFullName();
        }
        return testResultFullName;
    }

    private String limitValueFullName;

    private TrfLimitValueFullNameRelDTO limitValueFullNameRel;

    private RdReportLimitDTO reportLimit;

    private RdMethodLimitDTO methodLimit;

    private List<RdTestResultLanguageDTO> languageList;

    //since SCI-1748
    private String testResultInstanceId;

}
