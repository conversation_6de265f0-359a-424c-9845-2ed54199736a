package com.sgs.customerbiz.biz.event.handler;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.ImmutableList;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.dto.DataSyncExtra;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.dto.TaskObjectRelDTO;
import com.sgs.customerbiz.biz.enums.EventNotifyRuleEnum;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.customerbiz.biz.event.handler.converter.DataConvertStrategy;
import com.sgs.customerbiz.biz.event.handler.converter.impl.DefaultConvertStrategy;
import com.sgs.customerbiz.biz.event.handler.predicate.DependOnUnPendingActionPredicater;
import com.sgs.customerbiz.biz.service.aftersplit.DataAfterSplitService;
import com.sgs.customerbiz.biz.service.condition.base.CustomerNotifyTriggerCondition;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.datacollector.DataCollectService;
import com.sgs.customerbiz.biz.service.datacollector.DataCollectorContextHolder;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertService;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.biz.service.task.impl.handler.TaskFactory;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.TaskCondition;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.ILayerMessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.MessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.message.IgnoreAttrNameMap;
import com.sgs.customerbiz.biz.utils.*;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.SpringUtil;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfLogMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfLogExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfLogPO;
import com.sgs.customerbiz.domain.domainevent.*;
import com.sgs.customerbiz.domain.domainevent.message.MessageTaskCompletedEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfParkEventDomainService;
import com.sgs.customerbiz.domain.dto.TrfStatusControlConfig;
import com.sgs.customerbiz.domain.enums.ChangeTypeEnum;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.EmailClient;
import com.sgs.customerbiz.integration.UserClient;
import com.sgs.customerbiz.integration.dto.EMailRequest;
import com.sgs.customerbiz.integration.dto.EmpInfoReq;
import com.sgs.customerbiz.integration.dto.EmpInfoRsp;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfQuotationDTO;
import com.sgs.customerbiz.model.trf.enums.PendingCtrlEnum;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TaskParamObjectTypeEnum;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.biz.utils.TrfEventCodeMapping.*;

/**
 * <AUTHOR> walley.wang
 * @version : V1.0.0
 * @date : 2023/6/30 9:37
 * @desc : 消息通知型处理器，按模板化的流程执行
 */
@Slf4j
@Component("messageNotifyHandler")
public class MessageNotifyHandler implements DataSyncHandler {

    @Resource
    private DataConvertor<String, String, JSON> jsonDataConvertor;

    @Resource
    private DataConvertor<ObjectEvent, EventSubscribeDTO, String> ilayerDataConvertorAdaptor;

    @Resource
    private DataCollectService dataCollectService;

    @Resource
    private DataPreConvertService dataPreConvertService;

    @Resource
    private TaskService taskService;

    @Resource
    protected EventSubscribeService eventSubscribeService;

    @Resource
    protected TrfDomainService trfDomainService;

    @Resource
    protected TrfParkEventDomainService trfParkEventDomainService;

    @Resource
    private EmailClient emailClient;

    @Resource
    private TrfLogMapper trfLogMapper;

    @Resource
    private UserClient userClient;

    @Resource
    protected TrfInfoExtMapper trfInfoExtMapper;

    @Resource
    private List<CustomerNotifyTriggerCondition> notifyTriggerConditions;

    @Resource
    private ConfigClient configClient;

    @Resource(name = "messageDependOnUnPendingActionPredicater")
    private DependOnUnPendingActionPredicater messageDependOnUnPendingActionPredicater;

    @Autowired
    private IdService idService;

    @Resource
    private List<DataConvertStrategy> convertStrategies;

    @Autowired
    private DataAfterSplitService dataAfterSplitService;


    @Override
    public void handle(EventSubscribeDTO subscribe, ObjectEvent trfEvent, DataSyncExtra extraData) {
        try {
            HandleContextHolder.setTaskId(trfEvent.getEventId(), subscribe.getApiId());
            extraData.setRandomSequence(HandleContextHolder.getTaskId(trfEvent.getEventId(), subscribe.getApiId()));
            HandleContextHolder.setDataSyncExtra(extraData);

            if (!isAllConditionsMatched(trfEvent, subscribe)) {
                log.info("The event did not match the processing criteria. TrfNo={}, EventId={}, EventName={}",
                        trfEvent.getTrfNo(),
                        trfEvent.getEventId(),
                        trfEvent.getClass().getSimpleName());
                return;
            }

            if (trfEvent instanceof TrfEvent) {
                if (isMarkedPending(subscribe, trfEvent)) {
                    return;
                }
            }

            if (subscribe.getApiId() == null) {
                log.warn("subscriber systemId:{} ,RefSystemId : {}, API Id not configured.", subscribe.getSubscriber(), subscribe.getRefSystemId());
                return;
            }

            // TODO 临时处理，与文军确认后决定如何修改，先不阻碍验证
            if (Objects.equals(subscribe.getSubscriber(), trfEvent.getSystemId()) && !(trfEvent instanceof TrfNewEvent)) {
                return;
            }

            // 根据systemId + apiId 查找api信息
            SystemApiDTO systemApi = configClient.getSystemApiInfo(subscribe.getSubscriber(), subscribe.getApiId());
            if (systemApi == null) {
                log.warn("systemId:{} ,api:{} not existed.", subscribe.getSubscriber(), subscribe.getApiId());
                return;
            }
            // 1. 根据TRF收集数据
            CollectedData collectedData = collect(subscribe, trfEvent, extraData);
            //2.数据收集完整后，预处理扩展
            /**
             * 2.1 Sgsmart.去除无效的TestLine （无效类型、无效状态）
             * 2.2 AF-Yili.处理TestResultValue.merge
             * 2.3 优衣库.LabCode\TestLine.MappingToCustomer
             */
            collectedData = preHandler(subscribe, trfEvent, collectedData);

            if (trfEvent instanceof TrfCompletedEvent||trfEvent instanceof TrfOrderCompletedEvent || trfEvent instanceof TrfDeliveryReportEvent || trfEvent instanceof TrfClosedEvent) {
                trfDomainService.markDeliveryReport(subscribe.getId(),subscribe.getSubscriber(), subscribe.getApiId(), DataCollectorContextHolder.getDeliveryReportNos());
            }

            if(systemApi.splitByReport()) {
                convertThenInvokeByReport(subscribe, trfEvent, collectedData, systemApi);
            } else {
                convertThenInvoke(subscribe, trfEvent, collectedData, systemApi);
            }

        } finally {
            HandleContextHolder.removeTaskId(trfEvent.getEventId(), subscribe.getApiId());
            HandleContextHolder.removeDataSyncExtra();
            DataCollectorContextHolder.clearReportNos();
        }
    }

    private void convertThenInvoke(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData, SystemApiDTO systemApi) {
        List<String> deliveryReportNos = DataCollectorContextHolder.getDeliveryReportNos();
        // 判断订阅条件中的condition是否成立
        boolean flag = SubscriberConditionUtils.subscriberConditionEval(subscribe, collectedData);
        if (!flag) {
            log.warn("subscriber systemId:{} ,RefSystemId : {}, API Id : {} Condition Fail.", subscribe.getSubscriber(), subscribe.getRefSystemId(), subscribe.getApiId());
            trfDomainService.conditionDeliveryReport(deliveryReportNos, subscribe.getApiId());
            return;
        }
        // 3. 标准数据转换为客户定制数据
        Object convertedData = convert(subscribe, trfEvent, collectedData, systemApi);

        String extId = "";
        if (trfEvent instanceof TrfActionEvent) {
            String extIdByAction = trfEvent.getExtId();
            extId = Func.isEmpty(extIdByAction) ? trfEvent.getTrfNo() : extIdByAction;
        } else {
            extId = trfEvent.getTrfNo();
        }
        // 4. 分发数据
        Long taskId = HandleContextHolder.getTaskId(trfEvent.getEventId(), subscribe.getApiId());
        invoke(taskId, subscribe, trfEvent, convertedData, systemApi, extId ,deliveryReportNos);
    }

    private void convertThenInvokeByReport(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData, SystemApiDTO systemApi) {
        List<CollectedData> collectedDataList = CollectDataUtil.splitByReport(collectedData);
        for(CollectedData collectedDataBySplit : collectedDataList) {
            dataAfterSplitService.afterSplit(subscribe, trfEvent, collectedDataBySplit);
            String reportNo = CollectDataUtil.fetchFirstReportNo(collectedDataBySplit);
            // 判断订阅条件中的condition是否成立
            boolean flag = SubscriberConditionUtils.subscriberConditionEval(subscribe, collectedData);
            if (!flag) {
                log.warn("subscriber systemId:{} ,RefSystemId : {}, API Id : {} Condition Fail.", subscribe.getSubscriber(), subscribe.getRefSystemId(), subscribe.getApiId());
                trfDomainService.conditionDeliveryReport(ImmutableList.of(reportNo), subscribe.getApiId());
                continue;
            }
            // 3. 标准数据转换为客户定制数据
            DataSyncExtra extraDataSplit = (DataSyncExtra)collectedDataBySplit.getExtraData();
            // todo taskId 是不是有依赖关系的问题
            Long taskId = IdUtil.snowflakeId();
            extraDataSplit.setRandomSequence(taskId);
            Object convertedData = convert(subscribe, trfEvent, collectedDataBySplit, systemApi);
            // 4. 分发数据
            invoke(taskId, subscribe, trfEvent, convertedData, systemApi,reportNo ,ImmutableList.of(reportNo));
        }
    }

    private String getPendingDecision(Integer refSystemId, String buCode) {
        TrfStatusControlConfig cfg = getPendingDecisionConfig(refSystemId, buCode, false);
        if (cfg == null) {
            cfg = getPendingDecisionConfig(0, "", true);
        }
        return cfg.getPendingDecision();
    }

    public TrfStatusControlConfig getPendingDecisionConfig(Integer refSystemId, String buCode, boolean thrEx) {
        String configValue = configClient.getConfig(buCode, refSystemId, Constants.CONFIG_STATUS_CONTROL);
        if (Func.isEmpty(configValue)) {
            if (thrEx) {
                log.error("TrfStatusControl config was not found. identityId={}, buCode={}", refSystemId, buCode);
                throw new BizException("There are currently no configuration items. Please update the relevant configuration items！");
            } else {
                return null;
            }
        }

        TrfStatusControlConfig trfStatusControlConfig =
                JSON.parseObject(configValue, TrfStatusControlConfig.class);
        if (trfStatusControlConfig.getActionStatusMapping() == null ||
                trfStatusControlConfig.getPendingDecision() == null) {
            if (thrEx) {
                log.error("TrfStatusControl config item was not found. identityId={}, buCode={}, itemKey=pendingDecision", refSystemId, buCode);
                throw new BizException("There are currently no `pendingDecision` items. Please update the relevant configuration！");
            } else {
                return null;
            }
        }
        return trfStatusControlConfig;
    }

    protected boolean isMarkedPending(EventSubscribeDTO subscribe, ObjectEvent trfEvent) {
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
        Assert.notNull(trfInfoPO, StrUtil.format("The trf {} not found!", trfEvent.getTrfNo()));
        if (shouldPark(subscribe, trfEvent, trfInfoPO)) {
            log.info("TrfEvent was suspended due to Trf marked as Pending. TrfNo:[{}],RefSystemId:[{}],TrfEvent:[{}]",
                    trfInfoPO.getTrfNo(),
                    trfEvent.getRefSystemId(),
                    trfEvent.getClass().getSimpleName());
            // 记录事件记录
            // TODO DTO convert to PO
            trfParkEventDomainService.save(
                    trfEvent.getTrfNo(), trfEvent.getSystemId(), trfEvent.getRefSystemId(),
                    trfEvent.getEventId(), TrfEventCodeMapping.getCode(trfEvent), trfEvent.getTriggerBy().name(),
                    trfEvent.getTimestamp(), trfEvent.getProductLineCode(), trfEvent.getSourceId());
            return true;
        }
        return false;
    }

    /**
     * 非(TrfPendingEvent或TrfUnPendingEvent)且Trf当前被标记为pending且客户配置为pending时block主流程
     *
     * @param subscribe
     * @param trfEvent
     * @param trfInfoPO
     * @return 满足返回true，不满足范湖false
     */
    private boolean shouldPark(EventSubscribeDTO subscribe, ObjectEvent trfEvent, TrfInfoPO trfInfoPO) {
        String pendingCtrl = getPendingDecision(subscribe.getRefSystemId(), trfEvent.getProductLineCode());
        return !(trfEvent instanceof TrfPendingEvent || trfEvent instanceof TrfUnPendingEvent) &&
                Objects.equals(trfInfoPO.getPendingFlag(), PendingFlagEnum.Pending.getType()) &&
                PendingCtrlEnum.Hold.getCtrl().equalsIgnoreCase(pendingCtrl);
    }


    protected CollectedData collect(EventSubscribeDTO subscribe, ObjectEvent trfEvent, Object extraData) {
        return dataCollectService.collect(
                trfEvent.getRefSystemId(),
                trfEvent.getTrfNo(),
                subscribe.getNotifyData(),
                extraData);
    }

    protected CollectedData preHandler(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        return dataPreConvertService.preHandler(subscribe, trfEvent, collectedData);
    }

    protected Object convert(EventSubscribeDTO subscribe, ObjectEvent trfEvent, 
            CollectedData collectedData, SystemApiDTO systemApi) {
        
        // 1. 过滤出非默认策略并按Order排序
        Optional<DataConvertStrategy> matchedStrategy = convertStrategies.stream()
                .filter(strategy -> !(strategy instanceof DefaultConvertStrategy))
                .filter(strategy -> strategy.supports(trfEvent, subscribe))
                .findFirst();

        // 2. 如果找到匹配的非默认策略，执行转换
        if (matchedStrategy.isPresent()) {
            return matchedStrategy.get().convert(trfEvent, subscribe, collectedData, systemApi);
        }
        
        // 3. 如果没有匹配的策略,使用默认转换策略
        log.warn("No matching convert strategy found for event: {}, using default strategy",
            trfEvent.getClass().getSimpleName());

        return convertStrategies.stream()
                .filter(DefaultConvertStrategy.class::isInstance)
                .findFirst()
                .map(s -> s.convert(trfEvent, subscribe, collectedData, systemApi))
                .orElseThrow(() -> new IllegalStateException("Default convert strategy not found"));
    }

    protected void invoke(Long taskId, EventSubscribeDTO subscribe, ObjectEvent trfEvent, Object convertedData, SystemApiDTO systemApi, String extId, List<String> deliveryReportNos) {
        TaskInfoDTO messageTask;

        //todo 适配SHEIN:改成通用的message
        if (subscribe.getSubscriber() == RefSystemIdEnum.Shein.getRefSystemId()) {
            ILayerMessageTaskParameters iLayerMessage = ILayerMessageTaskParameters.buildMessage(convertedData.toString(), systemApi, trfEvent);
            messageTask = TaskFactory.createDefaultILayerMessageTask(taskId, iLayerMessage);
        } else {
//            //todo 目前通知customer使用ilayer的透传接口，所以需要对请求参数包装成ilayer的格式,后续不使用ilayer了删除
            MessageTaskParameters notifyMessage = MessageTaskParameters.build(convertedData, systemApi, trfEvent);
            messageTask = TaskFactory.createDefaultMessageTask(taskId, notifyMessage);
        }

        messageTask.setSubscriberId(subscribe.getId());
        messageTask.setApiId(subscribe.getApiId());
        messageTask.setIdentityId(subscribe.getSubscriber());
        messageTask.setGroupKey(String.valueOf(subscribe.getEventCode()));
        messageTask.setExtId(extId);
        messageTask.setRefSystemId(subscribe.getRefSystemId());

        // 如果需要顺序发送,查找前置taskId
        TaskCondition taskCondition = this.getTaskConditionIfMatched(subscribe, trfEvent);
        if (taskCondition != null) {
            messageTask.setDependCondition(JSON.toJSONString(taskCondition, SerializerFeature.DisableCircularReferenceDetect));
        }

        Map<Integer, Long> trfEventTaskIdMap = trfEvent.getTaskIdMap();
        if (Func.isEmpty(trfEventTaskIdMap)) {
            trfEventTaskIdMap = new HashMap<>();
        }
        // 前置taskId
        messageTask.setDependTask(trfEventTaskIdMap.get(subscribe.getSubscriber() * 100 + (subscribe.getPriority() + 1)));
        if(systemApi.splitByReport()) {
            messageTask.setId(idService.nextId());
        } else {
            messageTask.setId(trfEventTaskIdMap.get(subscribe.getSubscriber() * 100 + subscribe.getPriority()));
        }


        /**
         * TODO 待完善，
         * //更新delivery Object 的状态为发送中
         */
        if (trfEvent instanceof TrfCompletedEvent|| trfEvent instanceof TrfOrderCompletedEvent || trfEvent instanceof TrfDeliveryReportEvent || trfEvent instanceof TrfClosedEvent) {
            fillTaskParamForReportNo(messageTask, deliveryReportNos);
        }

        if (trfEvent instanceof TrfDeliveryQuotationEvent) {
            fillTaskParamForQuotationNo(messageTask, trfEvent);
        }

        if (taskService.checkSubmitTask(messageTask, IgnoreAttrNameMap.getIgnoreAttrNames(systemApi.getId()))) {
            taskService.submitTask(messageTask);
        } else {
            trfDomainService.repeatedDeliveryReport(deliveryReportNos, subscribe.getApiId());
        }
    }

    protected TaskCondition getTaskConditionIfMatched(EventSubscribeDTO eventSubscribe, ObjectEvent trfEvent) {
        // 不要求顺序发送
        if (!EventNotifyRuleEnum.SERIAL_ON_NOTIFY.match(eventSubscribe.getNotifyRule())) {
            return null;
        }
        // 当前code如果不在需要顺序发送的事件列表中，返回空
        int matchIndex = ArrayUtil.matchIndex(v -> v.equals(eventSubscribe.getEventCode()), SERIAL_EVENT_CODES);
        boolean isSerialEvent = matchIndex > 0;
        if (!isSerialEvent) {
            return null;
        }

        // 判断客户订阅的上一个事件是不是在顺序发送列表中
        List<EventSubscribeDTO> subscribeList = eventSubscribeService.findBySubsciberAndRefSystemId(eventSubscribe.getSubscriber(), eventSubscribe.getRefSystemId());

        Integer groupKey = subscribeList.stream().map(EventSubscribeDTO::getEventCode)
                .filter(code -> (code < eventSubscribe.getEventCode())).sorted(Comparator.comparingInt(Integer::intValue).reversed())
                .filter(code -> ArrayUtil.matchIndex(v -> v.equals(code), SERIAL_EVENT_CODES) >= 0)
                .findFirst().orElse(null);

        // subscriber + RefSystemId + eventCode 维度下，存在相同 EventSubscribe记录，则需要考虑priority
        List<EventSubscribeDTO> groupSubscribeList = subscribeList.stream().filter(eventSubscribeDTO -> (Objects.equals(eventSubscribeDTO.getEventCode(), eventSubscribe.getEventCode()))).collect(Collectors.toList());
        if (groupSubscribeList.size() > 1) {
            for (EventSubscribeDTO e : groupSubscribeList) {
                if (eventSubscribe.getPriority() < e.getPriority()) {
                    groupKey = e.getEventCode();
                }
            }
        }

        if (groupKey == null) {
            boolean result = messageDependOnUnPendingActionPredicater.predicate(
                    HandleContextHolder.getTrfEventSubscribeList(),
                    HandleContextHolder.getTrfEvent(), trfEvent);
            if (result) {
                return TaskCondition.newBuilder()
                        .identityIdEquals(eventSubscribe.getSubscriber())
                        .extIdEquals(trfEvent.getTrfNo())
                        .groupKeyEquals(TrfEventCodeMapping.getCode(HandleContextHolder.getTrfEvent()).toString())
                        .statusIn(Collections.singletonList(TaskStatusEnum.SUCCESS.getCode())).build();
            }

            return null;
        }

        // 查询trf的source是否为orderToTrf并且依赖的EventCode是10则跳过
        TrfInfoPO trf = trfDomainService.selectByTrfNo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
        if (Objects.equals(trf.getSource(), TrfSourceType.Order2TRF.getSourceType()) && Objects.equals(groupKey, TRF_TO_ORDER_EVENT)) {
            return null;
        }

        return TaskCondition.newBuilder()
                .identityIdEquals(eventSubscribe.getSubscriber())
                .extIdEquals(trfEvent.getTrfNo())
                .groupKeyEquals(groupKey.toString())
                .statusIn(Collections.singletonList(TaskStatusEnum.SUCCESS.getCode())).build();
    }

    private static final Integer[] SERIAL_EVENT_CODES = {
            TRF_TO_ORDER_EVENT,
            TRF_TO_QUOTATION_EVENT,
            TRF_CONFIRMED_EVENT,
            TRF_TESTING_EVENT,
            TRF_REPORTING_EVENT,
            TRF_COMPLETED_EVENT,
            TRF_CLOSED_EVENT
    };

    private void fillTaskParamForReportNo(TaskInfoDTO taskInfoDTO, List<String> deliveryReportNos) {

        if (CollectionUtil.isEmpty(deliveryReportNos)) {
            return;
        }

        taskInfoDTO.setTaskObjectRelList(new ArrayList<>());

        deliveryReportNos.forEach(deliveryReportNo -> {
            TaskObjectRelDTO taskParamDTO = new TaskObjectRelDTO();
            taskParamDTO.setObjectType(TaskParamObjectTypeEnum.Report.getCode());
            taskParamDTO.setObjectNo(deliveryReportNo);

            taskInfoDTO.getTaskObjectRelList().add(taskParamDTO);
        });

    }

    private void fillTaskParamForQuotationNo(TaskInfoDTO taskInfoDTO, ObjectEvent trfEvent) {
        if (!(trfEvent instanceof TrfDeliveryQuotationEvent)) {
            return;
        }

        TrfDeliveryQuotationEvent trfDeliveryQuotationEvent = (TrfDeliveryQuotationEvent) trfEvent;
        TrfFullDTO trfFullDTO = (TrfFullDTO) trfEvent.getPayload();

        List<TrfQuotationDTO> quotationList = trfFullDTO.getQuotationList();

        if (CollectionUtil.isEmpty(quotationList)) {
            return;
        }

        if (CollectionUtil.isEmpty(taskInfoDTO.getTaskObjectRelList())) {
            taskInfoDTO.setTaskObjectRelList(new ArrayList<>());
        }

        List<String> quotationNoList = new ArrayList<>();
        quotationList.stream().forEach(quotationDTO -> {
            TaskObjectRelDTO taskParamDTO = new TaskObjectRelDTO();
            taskParamDTO.setObjectType(TaskParamObjectTypeEnum.Quotation.getCode());
            taskParamDTO.setObjectNo(quotationDTO.getQuotationNo() + "-" + quotationDTO.getQuotationVersionId());

            taskInfoDTO.getTaskObjectRelList().add(taskParamDTO);

            quotationNoList.add(taskParamDTO.getObjectNo());
        });

        TrfHeaderDOV2 trfHeaderDOV2 = trfDomainService.selectSimple(RefSystemIdAdapter.recover(taskInfoDTO.getIdentityId()), taskInfoDTO.getExtId());
        trfDomainService.markDeliveryQuotation(trfHeaderDOV2.getTrfId(), quotationNoList);
    }

    private void notifyByEmail(MessageTaskCompletedEvent event, TaskInfoPO taskInfo) {
        ILayerMessageTaskParameters taskParameters = JSON.parseObject(taskInfo.getTaskParameters(), ILayerMessageTaskParameters.class);
        if (taskParameters.getRawMessage() == null) {
            log.warn("TaskParameters was not found. TrfNo:{}, TaskId:{}", taskInfo.getExtId(), event.getTaskId());
            return;
        }

        Integer refSystemId = taskParameters.getRawMessage().getRefSystemId();
        TrfInfoPO trfInfo = trfDomainService.selectByTrfNo(refSystemId, taskInfo.getExtId());
        if (trfInfo == null) {
            log.warn("TrfInfo was not found. TrfNo:{}, RefSystemId:{}", taskInfo.getExtId(), refSystemId);
            return;
        }

        CustomerTrfInfoRsp trfExtInfo = trfInfoExtMapper.getTrfInfo(refSystemId, taskInfo.getExtId());
        if (trfExtInfo == null) {
            log.warn("TrfExtInfo was not found. TrfNo:{}, RefSystemId:{}", taskInfo.getExtId(), refSystemId);
            return;
        }

        TrfLogExample example = new TrfLogExample();
        example.createCriteria().andTrfNoEqualTo(trfInfo.getTrfNo()).
                andRefSystemIdEqualTo(trfInfo.getRefSystemId()).andChangeTypeEqualTo(ChangeTypeEnum.CANCEL.getCode());
        example.setOrderByClause("id desc");
        List<TrfLogPO> trfLogPOList = trfLogMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(trfLogPOList)) {
            log.warn("Trf cancellation log was not found. TrfNo:{}, TrfExtId:{}", taskInfo.getExtId(), trfExtInfo.getId());
            return;
        }

        EmpInfoReq empInfoReq = new EmpInfoReq();
        empInfoReq.setLabCode(trfExtInfo.getLabCode());
        empInfoReq.setRegionAccount(trfLogPOList.get(0).getCreatedBy());
        CustomResult<EmpInfoRsp> customResult = userClient.getEmpInfo(empInfoReq);
        if (customResult == null || !customResult.isSuccess()) {
            log.warn("Employee info was not found. LabCode:{}, RegionAccount:{}", trfExtInfo.getLabCode(), trfLogPOList.get(0).getCreatedBy());
            return;
        }

        // from系统id
        Integer srcSysId = event.getSystemId();
        // from系统名称
        String srcSysName = srcSysId.intValue() == refSystemId ?
                RefSystemIdEnum.getRefSystemId(srcSysId).getName() :
                SgsSystem.findCode(srcSysId).getCode();
        // to系统名称
        String tgtSysName =
                srcSysId.intValue() == refSystemId ?
                        SgsSystem.findCode(trfInfo.getSystemId()).getCode() :
                        RefSystemIdEnum.getRefSystemId(trfInfo.getRefSystemId()).getName();

        EMailRequest eMailRequest = EMailRequest.builder()
                .mailTo(Collections.singleton(customResult.getData().getEmail()))
                .mailCc(getCcByProfile())
                .mailSubject(String.format(EMAIL_TITLE_TEMPLATE, taskInfo.getExtId(), srcSysName, tgtSysName))
                .mailText(String.format(EMAIL_CONTENT_TEMPLATE, taskInfo.getExtId(), srcSysName, tgtSysName, event.getMessage()))
                .build();
        TempSendEmailExecutor.asyncExecute(() -> emailClient.sendEmail(eMailRequest));
    }

    private final static String EMAIL_TITLE_TEMPLATE = "TRF No：%s，%s节点同步%s失败";
    private final static String EMAIL_CONTENT_TEMPLATE = "TRF No：%s，%s节点同步%s失败,，原因为:%s";

    // TODO 后续扩展支持更多处理动作种类、方式
    // error handle支持通知等各种形式动作
    // 通知方式目前支持email，未来还可以扩展其他方式sms、teams、飞书等
    @Override
    public void onError(ObjectEvent event, TaskInfoPO taskInfoPo, Set<String> errorHandles) {
        /**
         * task失败情况下，如果是delivery 事件，需要重置deliveryFlag
         */
//        List<TaskObjectRelPO> taskObjectRelPOList = taskService.queryTaskObjectRelByTaskIdAndObjectType(taskInfoPo.getTaskId(), TaskParamObjectTypeEnum.Report.getCode());
//        if (CollectionUtil.isNotEmpty(taskObjectRelPOList)) {
//            TrfHeaderDOV2 trfHeaderDOV2 = trfDomainService.selectSimple(taskInfoPo.getIdentityId(), taskInfoPo.getExtId());
//            List<String> reportNoList = taskObjectRelPOList.stream().map(TaskObjectRelPO :: getObjectNo).collect(Collectors.toList());
//            log.info("Failed DeliveryReport . reportNos : {}", JsonUtil.toJson(reportNoList));
//            trfDomainService.failedDeliveryReport(trfHeaderDOV2.getTrfId(), reportNoList);
//        }

        for (String errorHandle : errorHandles) {
            if ("email".equals(errorHandle)) {
                notifyByEmail((MessageTaskCompletedEvent) event, taskInfoPo);
            }
        }
    }

    @Override
    public void onSuccess(ObjectEvent event, TaskInfoPO taskInfo, HashSet<String> strings) {
//        List<TaskObjectRelPO> taskObjectRelPOList = taskService.queryTaskObjectRelByTaskIdAndObjectType(taskInfo.getTaskId(), TaskParamObjectTypeEnum.Report.getCode());
//        if (CollectionUtil.isNotEmpty(taskObjectRelPOList)) {
//            TrfHeaderDOV2 trfHeaderDOV2 = trfDomainService.selectSimple(taskInfo.getIdentityId(), taskInfo.getExtId());
//            List<String> reportNoList = taskObjectRelPOList.stream().map(TaskObjectRelPO :: getObjectNo).collect(Collectors.toList());
//            log.info("Completed DeliveryReport . reportNos : {}", JsonUtil.toJson(reportNoList));
//            trfDomainService.completedDeliveryReport(trfHeaderDOV2.getTrfId(), reportNoList);
//        }
    }

    static Set<String> DEFAULT_CC = new HashSet<>(Arrays.asList("<EMAIL>", "<EMAIL>"));
    static Map<String, Set<String>> CC_CONFIG_MAP = new HashMap<>();

    static {
        CC_CONFIG_MAP.put("test", new HashSet<>(Arrays.asList("<EMAIL>", "<EMAIL>")));
        CC_CONFIG_MAP.put("uat", new HashSet<>(Arrays.asList("<EMAIL>", "<EMAIL>")));
        CC_CONFIG_MAP.put("prod", new HashSet<>(Arrays.asList(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        )));
    }

    private Set<String> getCcByProfile() {
        try {
            Set<String> cc = CC_CONFIG_MAP.get(SpringUtil.getApplicationContext().getEnvironment().getActiveProfiles()[0]);
            if (CollectionUtils.isEmpty(cc)) {
                return DEFAULT_CC;
            }
            return cc;
        } catch (Exception e) {
            return DEFAULT_CC;
        }
    }

    private boolean isAllConditionsMatched(ObjectEvent event, EventSubscribeDTO subscribe) {
        for (CustomerNotifyTriggerCondition condition : notifyTriggerConditions) {
            if (!condition.validate(event, subscribe)) {
                log.warn("HandleTriggerCondition validate failure. event:{} ,condition:{} ,trfNo:{} ,eventId:{}",
                        event.getClass().getSimpleName(), condition.getClass().getName(), event.getTrfNo(), event.getEventId());
                return false;
            }
        }
        log.info("trfNo:{} pass the event handle pre-check.eventId:{}", event.getTrfNo(), event.getEventId());
        return true;
    }

}
