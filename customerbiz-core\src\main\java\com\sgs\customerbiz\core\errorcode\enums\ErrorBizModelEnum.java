package com.sgs.customerbiz.core.errorcode.enums;

public enum ErrorBizModelEnum {
    //枚举值换成字符串
    SCITRFBIZSERVICE("01", "SciTrfBizService"),
    CUSTOMERTRFDOMAIN("02", "CustomerTrfDomainService"),
    ABSTRACTCUSTOMERTRFIMPORT("03", "AbstractCustomerTrfImportActionExtPt"),
    TODO_LIST_SERVICE("04", "TodoListService"),
    TRFDOMAIN("05", "TrfDomainService"),
    TRFORDERDOMAIN("06", "TrfOrderDomainService"),
    TRFREPORTDOMAIN("07", "TrfReportDomainService"),
    SGSMAARTORDER("08", "SgsMartOrderToTrfActionExtPt"),
    SYNCSERVICETYPE("09", "SciTrfBizService (同步相关)"),
    NORMALSYNCVALIDATOR("10", "NormalSyncActionValidatorExtPt"),
    CLOSEDSYNCVALIDATOR("11", "ClosedSyncActionValidatorExtPt"),
    REPORTDATASERVICE("12", "ReportDataService"),
    DAFAULTIMPORTTRFACTION("13", "DefaultImportAction"),
    VALIDATOR_DFV("14", "DFVHelper"),
    VALIDATION_CUSTOMER("15", "CustomerValidator"),
    ORDERTOTRFACTION("16","OrderToTRFAction"),
    GLOBALEXCEPTIONASPECT("17","GlobaExceptionAspect"),
    SYNCACTION("18","SyncAction"),
    DELIVERYACTION("19","DeliveyAction"),
    VALIDATORBIZSERVICE("20","validateBizService"),
    IMPORTPROCESS("21","importProcess"),
    ;



    private final String code;
    private final String description;

    ErrorBizModelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ErrorBizModelEnum fromCode(String code) {
        for (ErrorBizModelEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
