package com.sgs.customerbiz.validation.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DffUtils {

    public static String replaceDffLabelCode(String input, Map<String, String> map) {
        return replaceDffLabelCode(input, "labelCode", map);
    }

    public static String replaceDffLabelCode(String input, String orderKeyword, Map<String, String> map) {
        if(CollectionUtils.isEmpty(map)) {
            throw new BizException(ResponseCode.FAIL, "dff code mapping not found!");
        }
        if(Objects.isNull(input)) {
            return input;
        }
        if(input.startsWith("SAMPLEATTRLIST")) {
            String[] split = input.split(":");
            String labelCode = split[1];
            if(!map.containsKey(labelCode)) {
                throw new BizException(ResponseCode.FAIL, "dff code mapping '" +labelCode+ "' not found!");
            }
            return "SAMPLEATTRLIST:" + map.get(labelCode);
        }
        // 检查输入字符串是否包含"orderList"
        if (input.contains(orderKeyword)) {
            // 创建正则表达式以查找labelCode='xxx'的模式
            Pattern pattern = Pattern.compile("labelCode='(.*?)'");
            Matcher matcher = pattern.matcher(input);
            StringBuffer result = new StringBuffer();

            // 遍历所有匹配项
            while (matcher.find()) {
                String code = matcher.group(1); // 提取xxx
                if(!map.containsKey(code)) {
                    throw new BizException(ResponseCode.FAIL, "dff code mapping '" +code+ "' not found!");
                }
                String replacement = map.get(code); // 从映射中查找替换值，如果找不到则保持不变
                // 替换找到的匹配项
                matcher.appendReplacement(result, "labelCode='" + replacement + "'");
            }
            // 添加字符串的最后一部分
            matcher.appendTail(result);
            return result.toString();
        }
        return input; // 如果不包含"orderList"，直接返回原字符串
    }

    public static void main(String[] args) {
        Map<String, String> map = ImmutableMap.of(
                "RefCode1", "RefCode2"
        );
        System.out.println(replaceDffLabelCode("$.orderList[0].productList[0].productAttrList[labelCode='RefCode1'].value", map));
    }

    public static Object normLabelCodeAndLabelValue(Object data) {
        if(Objects.isNull(data)) {
            return null;
        }
        JSONObject root = JSON.parseObject(JSON.toJSONString(data));
        if(!root.containsKey("orderList")) {
            return root;
        }
        JSONArray orderList = root.getJSONArray("orderList");
        if(CollectionUtils.isEmpty(orderList)) {
            return root;
        }
        JSONObject order = orderList.getJSONObject(0);

        if(order.containsKey("productList") && !CollectionUtils.isEmpty(order.getJSONArray("productList"))) {
            JSONArray productList = order.getJSONArray("productList");
            for (int index = 0; index < productList.size(); index++) {
                JSONObject product = productList.getJSONObject(index);
                if(product.containsKey("productAttrList") && !CollectionUtils.isEmpty(product.getJSONArray("productAttrList"))) {
                    JSONArray productAttrList = product.getJSONArray("productAttrList");
                    for (int i = 0; i < productAttrList.size(); i++) {
                        JSONObject attr = productAttrList.getJSONObject(i);
                        if (attr.containsKey("labelCode")) {
                            attr.put("labelCode", capitalizeFirstLetter(attr.getString("labelCode")));
                        }
                        if (attr.containsKey("labelValue") && !attr.containsKey("value")) {
                            attr.put("value", attr.get("labelValue"));
                        }
                    }
                }
            }

        }
        if(order.containsKey("sampleList") && !CollectionUtils.isEmpty(order.getJSONArray("sampleList"))) {
            JSONArray sampleList = order.getJSONArray("sampleList");
            for (int index = 0; index < sampleList.size(); index++) {
                JSONObject sample = sampleList.getJSONObject(index);
                if(sample.containsKey("sampleAttrList") && !CollectionUtils.isEmpty(sample.getJSONArray("sampleAttrList"))) {
                    JSONArray productAttrList = sample.getJSONArray("sampleAttrList");
                    for (int i = 0; i < productAttrList.size(); i++) {
                        JSONObject attr = productAttrList.getJSONObject(i);
                        if (attr.containsKey("labelCode")) {
                            attr.put("labelCode", capitalizeFirstLetter(attr.getString("labelCode")));
                        }
                        if (attr.containsKey("labelValue") && !attr.containsKey("value")) {
                            attr.put("value", attr.get("labelValue"));
                        }
                    }
                }
            }

        }
        return root;
    }

    public static String capitalizeFirstLetter(String str) {
        if (str != null && !str.isEmpty()) {
            return StrUtil.upperFirst(str);
        }
        return str;
    }

}
