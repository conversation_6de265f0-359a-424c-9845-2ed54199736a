package com.sgs.customerbiz.biz.service.aftersplit;

import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;

public interface DataAfterSplitHandler {

    boolean acceptSubscriber(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData);

    void processData(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData);

}
