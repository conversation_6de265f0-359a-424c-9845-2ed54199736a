package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class IfContainsFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3, Object arg4) {
        if(Objects.isNull(arg1) || Objects.isNull(arg2)) {
            return null;
        }
        if(arg1 instanceof List) {
            return ((List<?>) arg1).contains(arg2) ? arg3 : arg4;
        }
        return arg1.toString().contains(arg2.toString()) ? arg3 : arg4;
    }

    @Override
    public String getName() {
        return "ifContains";
    }

    @Override
    public String desc() {
        return "ifContains";
    }
}
