package com.sgs.customerbiz.domain.domainservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.core.common.KafkaProducer;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.constants.TopicConstants;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.core.util.*;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfOrderRelationshipExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfTodoInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.SearchTrfInfoDTO;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.CustomerTrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfReportLevelMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.config.TrfUpdatedConfig;
import com.sgs.customerbiz.domain.constants.TrfConstants;
import com.sgs.customerbiz.domain.convertor.TrfInfoPOConvertor;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.domain.dto.TrfActionStatusRule;
import com.sgs.customerbiz.domain.dto.TrfStatusCalculationRequest;
import com.sgs.customerbiz.domain.dto.TrfStatusControlConfig;
import com.sgs.customerbiz.domain.enums.ChangeTypeEnum;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.domain.enums.TrfOrderOpTypeEnum;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.domain.helper.TrfReportDeliveryInfoObject;
import com.sgs.customerbiz.domain.message.Payload;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.domain.strategy.TrfStatusStrategy;
import com.sgs.customerbiz.domain.strategy.TrfStatusStrategyFactory;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.enums.SearchType;
import com.sgs.customerbiz.facade.model.trf.req.BoundTrfInfoSearchReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfReportDTO;
import com.sgs.customerbiz.model.trf.dto.req.CustomerConfirmReportTrf;
import com.sgs.customerbiz.model.trf.dto.req.GetCustomerConfigReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfUnbindReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerConfirmReportFlag;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.customerbiz.model.trf.enums.*;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.grus.kafka.client.MessageReq;
import com.sgs.otsnotes.facade.model.enums.ActiveIndicatorEnum;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.dto.order.DataDictionary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TrfDomainService {

    public static final String TRF_VERSION_KEY = "version";
    public static final int TRF_VERSION_1692 = 1692;
    @Resource
    private IdService idService;

    @Resource
    private TrfInfoMapper trfInfoMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TrfCustomerDomainService trfCustomerDomainService;

    @Resource
    private TrfReportLevelMapper trfReportLevelMapper;

    @Resource
    private TrfServiceRequirementDomainService trfServiceRequirementDomainService;

    @Resource
    private TrfAttachmentDomainService trfAttachmentDomainService;


    @Resource
    private TrfProductDomainService trfProductDomainService;

    @Resource
    private TrfTestSampleDomainService trfTestSampleDomainService;

    @Resource
    private TrfTestItemDomainService trfTestItemDomainService;

    @Resource
    private TrfOrderDomainService trfOrderDomainService;

    @Resource
    private TrfReportDomainService trfReportDomainService;

    @Resource
    private TrfQuotationDomainService trfQuotationDomainService;

    @Resource
    private TrfLabDomainService trfLabDomainService;

    @Autowired
    private TrfLogDomainService trfLogDomainService;

    @Resource
    private TrfOrderRelationshipExtMapper orderRelationshipExtMapper;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Resource
    private TrfTodoInfoExtMapper trfTodoInfoExtMapper;

    @Autowired
    private ReportDataService reportDataService;

    @Resource
    private TrfCareLabelDomainService trfCareLabelDomainService;
    @Resource
    ConfigClient configClient;

    @Autowired
    private CustomerTrfInfoMapper customerTrfInfoMapper;

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private TrfInfoExtMapper trfInfoExtMapper;

    @Autowired
    private TrfStatusStrategyFactory strategyFactory;

    private final KafkaProducer kafkaProducer;

    private final ObjectMapper objectMapper;

    private final TrfUpdatedConfig trfUpdatedConfig;

    public TrfDomainService(KafkaProducer kafkaProducer, ObjectMapper objectMapper, TrfUpdatedConfig trfUpdatedConfig) {
        this.kafkaProducer = kafkaProducer;
        this.objectMapper = objectMapper;
        this.trfUpdatedConfig = trfUpdatedConfig;
    }

    public TrfHeaderDOV2 selectSimple(Integer refSystemId, String trfNo) {
        Assert.notNull(refSystemId);
        Assert.notNull(trfNo);


        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
        criteria.andRefSystemIdEqualTo(refSystemId);
        criteria.andTrfNoEqualTo(trfNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoList = trfInfoMapper.selectByExample(trfInfoExample);
        TrfInfoPO trfInfoPO = CollUtil.get(trfInfoList, 0);
        if (Objects.isNull(trfInfoPO)) {
            return null;
        }
        List<TrfAttachmentPO> trfAttachment = trfAttachmentDomainService.getTrfAttachment(trfInfoPO.getId());
//        List<TrfLogPO> logPOs = trfLogDomainService.selectTrfLog(trfNo, refSystemId);
        TrfServiceRequirementDOV2 trfServiceRequirementPOS = trfServiceRequirementDomainService.selectByTrfId(trfInfoPO.getId());
        TrfHeaderDOV2 trfHeaderDOV2 = convertTrfHeader(trfInfoPO, trfServiceRequirementPOS, trfAttachment);
        return trfHeaderDOV2;
    }

    private TrfHeaderDOV2 convertTrfHeader(TrfInfoPO trfInfoPO, TrfServiceRequirementDOV2 trfServiceRequirementPOS, List<TrfAttachmentPO> trfAttachment) {
        TrfHeaderDOV2 trfHeaderDOV2 = new TrfHeaderDOV2();
        BeanUtils.copyProperties(trfInfoPO, trfHeaderDOV2);
        trfHeaderDOV2.setTrfId(trfInfoPO.getId());
        trfHeaderDOV2.setTrfStatus(trfInfoPO.getStatus());

        if (Func.isNotEmpty(trfAttachment)) {
            List<TrfAttachmentDOV2> collect = trfAttachment.stream().map(l -> {
                TrfAttachmentDOV2 trfAttachmentDOV2 = new TrfAttachmentDOV2();
                trfAttachmentDOV2.setId(l.getId());
                trfAttachmentDOV2.setTrfId(l.getTrfId());
                trfAttachmentDOV2.setLanguageId(l.getLanguageId());
                trfAttachmentDOV2.setFileType(l.getFileType());
                trfAttachmentDOV2.setFileName(l.getFileName());
                trfAttachmentDOV2.setFilePath(l.getFilePath());
                trfAttachmentDOV2.setCloudId(l.getCloudId());
                trfAttachmentDOV2.setFileSize(l.getFileSize());
                return trfAttachmentDOV2;
            }).collect(Collectors.toList());
            trfHeaderDOV2.setAttachmentList(collect);
        }

        if (StrUtil.isNotEmpty(trfInfoPO.getLabCode())) {
            TrfLabDOV2 lab = new TrfLabDOV2();
            lab.setLabId(trfInfoPO.getLabId());
            lab.setLabCode(trfInfoPO.getLabCode());
            lab.setBuId(trfInfoPO.getBuId());
            lab.setBuCode(trfInfoPO.getBuCode());
            lab.setLocationCode(LabUtil.parseLocationCode(lab.getLabCode()));

            TrfLabPO labPO = trfLabDomainService.selectByTrfId(trfInfoPO.getId());
            if (Func.isNotEmpty(labPO)) {
                TrfLabContactDOV2 labContact = new TrfLabContactDOV2();
                labContact.setContactName(labPO.getContactName());
                labContact.setContactEmail(labPO.getEmail());
                labContact.setContactTelephone(labPO.getTelephone());
                lab.setLabContact(labContact);
            }
            trfHeaderDOV2.setLab(lab);
        }
        TrfOtherDOV2 trfOtherDOV2 = null;
//        if (Func.isNotEmpty(logPOs)) {
        trfOtherDOV2 = new TrfOtherDOV2();
        trfHeaderDOV2.setOthers(trfOtherDOV2);
        //.andChangeTypeEqualTo(ChangeTypeEnum.CANCEL.getCode()).andToStatusEqualTo(TrfStatusEnum.Canceled.getStatus())
//            for (TrfLogPO logPO : logPOs) {
//                if (Objects.equals(logPO.getChangeType(), ChangeTypeEnum.CANCEL.getCode())) {
        trfOtherDOV2.setCancelType(trfInfoPO.getCancelType());
        trfOtherDOV2.setCancelRemark(trfInfoPO.getCancelRemark());
//                } else if (Objects.equals(logPO.getChangeType(), ChangeTypeEnum.REMOVE.getCode())) {
        trfOtherDOV2.setRemoveType(trfInfoPO.getRemoveType());
        trfOtherDOV2.setRemoveRemark(trfInfoPO.getRemoveRemark());
//                }
//            }
//        }
//        if (Func.isNotEmpty(trfInfoPO.getCancelType()) || Func.isNotEmpty(trfInfoPO.getRemoveType())) {
//            trfOtherDOV2 = new TrfOtherDOV2();
//            trfHeaderDOV2.setOthers(trfOtherDOV2);
//            trfOtherDOV2.setTrfRemark(trfInfoPO.getTrfRemark());
//            trfOtherDOV2.setCancelType(trfInfoPO.getCancelType());
//            trfOtherDOV2.setCancelRemark(trfInfoPO.getCancelRemark());
//            trfOtherDOV2.setRemoveType(trfInfoPO.getRemoveType());
//            trfOtherDOV2.setRemoveRemark(trfInfoPO.getRemoveRemark());
//        }
        if ((Func.isNotEmpty(trfInfoPO.getPendingFlag())) && PendingFlagEnum.check(trfInfoPO.getPendingFlag(), PendingFlagEnum.Pending)) {
            if (Func.isEmpty(trfOtherDOV2)) {
                trfOtherDOV2 = new TrfOtherDOV2();
                trfHeaderDOV2.setOthers(trfOtherDOV2);
            }
            TrfPendingDOV2 pendingDOV2 = new TrfPendingDOV2();
            pendingDOV2.setPendingFlag(trfInfoPO.getPendingFlag());
            pendingDOV2.setPendingType(trfInfoPO.getPendingType());
            pendingDOV2.setPendingRemark(trfInfoPO.getPendingRemark());
            trfOtherDOV2.setPending(pendingDOV2);
        }
        if (Func.isNotEmpty(trfServiceRequirementPOS)) {
            trfHeaderDOV2.setServiceRequirement(trfServiceRequirementPOS);
        }
        return trfHeaderDOV2;
    }

    /**
     * @param trfNo
     * @return
     */
    public List<TrfInfoPO> selectActiveAndNotCancelByTrfNos(List<String> trfNo) {
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        if (CollectionUtils.isEmpty(trfNo)) {
            return Collections.emptyList();
        }

        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
        criteria.andTrfNoIn(trfNo);
        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        criteria.andStatusNotEqualTo(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Cancelled.getStatus());
        return trfInfoMapper.selectByExample(trfInfoExample);
    }

    /**
     * @param trfNo
     * @return
     */
    public List<TrfInfoPO> selectByTrfNos(List<String> trfNo) {
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        if (CollectionUtils.isEmpty(trfNo)) {
            return Collections.emptyList();
        }

        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
        criteria.andTrfNoIn(trfNo);
        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfInfoMapper.selectByExample(trfInfoExample);
    }

    /**
     * @param trfNo
     * @return
     */
    public List<TrfInfoPO> selectActiveByTrfNo(String trfNo) {
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
        criteria.andTrfNoEqualTo(trfNo);
        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfInfoMapper.selectByExample(trfInfoExample);
    }


    public TrfInfoPO getByRefSystemIdOrThrow(Integer refSystemId, String trfNo,  List<TrfInfoPO> trfList) {
        Assert.isTrue(trfList!= null && trfList.size() == 1, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "can't found TRF by refSystemId:" +refSystemId+" and trfNo "+trfNo+" ");
        TrfInfoPO existingTrf = trfList.get(0);
        Assert.isTrue(Objects.equals(existingTrf.getRefSystemId(), refSystemId), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "can't found TRF by refSystemId:" +refSystemId+" and trfNo "+trfNo+" ");
        return existingTrf;
    }

    /**
     * @param trfNo
     * @return
     */
    public List<TrfInfoPO> selectActiveAndNotCancelByTrfNo(String trfNo) {
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
        criteria.andTrfNoEqualTo(trfNo);
        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        criteria.andStatusNotEqualTo(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Cancelled.getStatus());
        return trfInfoMapper.selectByExample(trfInfoExample);
    }


    /**
     * @param refSystemId
     * @param trfNo
     * @return
     */
    public TrfInfoPO selectByTrfNo(Integer refSystemId, String trfNo) {
        Assert.notNull(refSystemId, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        TrfInfoExample.Criteria criteria = trfInfoExample.createCriteria();
//        criteria.andRefSystemIdEqualTo(refSystemId);
        criteria.andTrfNoEqualTo(trfNo);
//        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoList = trfInfoMapper.selectByExample(trfInfoExample);
        if (Func.isEmpty(trfInfoList)) {
            return null;
        }
        return trfInfoList.stream().filter(l -> Objects.equals(l.getActiveIndicator(), ActiveIndicatorEnum.Active.getStatus())).findFirst().orElse(trfInfoList.get(0));
    }

    public TrfDOV2 createTrf(TrfDOV2 trfDO) {
        setupSampleLevelAndIntegrationLevel(trfDO);
        RLock rLock = null;
        TrfDOV2 result = null;
        try {
            String requestKey = String.format(String.format("SCI:TRF:REFSYSTEM:%s:%s", trfDO.getHeader().getTrfNo(), trfDO.getHeader().getRefSystemId()).toUpperCase());
            rLock = redissonClient.getLock(requestKey);
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.STATUSCONTROL, ErrorTypeEnum.EXTERNALCALLFAILED);
            ErrorAssert.isTrue(rLock.tryLock(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The TRF Executing, please do not execute continuously！");

            Pair<TrfDOV2, MessageReq> transactionResult = transactionTemplate.execute(transactionStatus -> createTrfImpl(trfDO));
            if (transactionResult != null) {
                result = transactionResult.getLeft();
                MessageReq messageReq = transactionResult.getRight();

                // 事务提交后发送Kafka消息
                if (messageReq != null) {
                    try {
                        kafkaProducer.doSendExternal(TopicConstants.SCI_EVENT, trfDO.getHeader().getTrfNo(), messageReq);
                        log.info("send {} to Topic({})", messageReq.getData(), TopicConstants.SCI_EVENT);
                    } catch (Exception e) {
                        log.warn("Failed to send kafka message for trf update. trfNo:{}, refSystemId:{}",
                                trfDO.getHeader().getTrfNo(), trfDO.getHeader().getRefSystemId(), e);
                    }
                }
            }

            return result;
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    private void setupSampleLevelAndIntegrationLevel(TrfDOV2 trfDO) {
        if (Objects.equals(trfDO.getHeader().getRefSystemId(), RefSystemIdEnum.Shein.getRefSystemId())) {
            trfDO.getHeader().setIntegrationLevel(String.valueOf(calcIntegrationLevel(trfDO)));
        } else {
            if (null != trfDO.getHeader().getSampleLevel()) {
                trfDO.getHeader().setSampleLevel(calcSampleLevel(trfDO));
            }
            Integer integrationLevel = calcIntegrationLevel(trfDO);
            trfDO.getHeader().setIntegrationLevel(integrationLevel == null ? null : String.valueOf(integrationLevel));
        }
    }

    public TrfOrderPO getTrfOrder(String trfNo, Integer refSystemId, String orderNo, String realOrderNo) {
        return trfOrderDomainService.selectOrderInfoByParams(trfNo, orderNo, realOrderNo, refSystemId);
    }

    public List<TrfOrderDOV2> getTrfOrderList(Integer systemId, String orderNo) {
        List<TrfOrderDOV2> trfOrderDOV2List = trfOrderDomainService.selectByExample(null, systemId, orderNo, null);

        return trfOrderDOV2List;
    }

    public TrfStatusControlConfig getTrfStatusControlConfig(Integer refSystemId, String buCode) {
        String configValue = configClient.getConfig(buCode, refSystemId, Constants.CONFIG_STATUS_CONTROL);
        if (Func.isEmpty(configValue)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "There are currently no configuration items. Please update the relevant configuration items！");
        }
        return JSON.parseObject(configValue, TrfStatusControlConfig.class);
    }

    private TrfStatusControlConfig getTrfStatusControlConfig(Integer refSystemId, String buCode, String action, boolean thrEx) {
        String configValue = configClient.getConfig(buCode, refSystemId, Constants.CONFIG_STATUS_CONTROL);
        if (Func.isEmpty(configValue)) {
            if (thrEx) {
                log.error("TrfStatusControl config was not found. identityId={}", refSystemId);
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "There are currently no configuration items. Please update the relevant configuration items！");
            } else {
                return null;
            }
        }

        TrfStatusControlConfig trfStatusControlConfig =
                JSON.parseObject(configValue, TrfStatusControlConfig.class);
        if (trfStatusControlConfig.getActionStatusMapping() == null ||
                trfStatusControlConfig.getActionStatusMapping().get(action) == null) {
            if (thrEx) {
                log.error("TrfStatusControl config item was not found. identityId={}, itemKey={}", refSystemId, action);
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.DATANOTFOUND);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "There are currently no `" + action + "` items. Please update the relevant configuration！");
            } else {
                return null;
            }
        }
        return trfStatusControlConfig;
    }

    public void deleteTrf(Long trfId) {
        TrfInfoPO trfInfoPO = selectByTrfId(trfId);
        if (trfInfoPO == null || ActiveIndicatorEnum.Inactive.getStatus() == trfInfoPO.getActiveIndicator()) {
            return;
        }

        //记录置为无效，状态不变
        trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());
        trfInfoPO.setStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.New.getStatus());
        // 将report状态置为无效
        trfReportDomainService.updateReportAsInvalid(Arrays.asList(trfInfoPO.getTrfNo()), trfInfoPO.getRefSystemId(), null);

        trfOrderDomainService.updateUnBindStatusByTrfId(trfId, null);
        deleteAllTrfReportLevel(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());

        updateTrf(trfInfoPO);
        customerTrfInfoMapper.deleteCustomerTrf(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());
    }

    public List<TrfReportLevelPO> queryInfoByTrfNo(String trfNo, Integer refSystemId) {
        Assert.notBlank(trfNo);
        Assert.notNull(refSystemId);
        TrfReportLevelExample example = new TrfReportLevelExample();
        example.createCriteria().andTrfNoEqualTo(trfNo);
        example.createCriteria().andRefSystemIdEqualTo(refSystemId);
        return trfReportLevelMapper.selectByExample(example);
    }

    private Pair<TrfDOV2, MessageReq> createTrfImpl(TrfDOV2 trfDO) {
        Pair<Long, MessageReq> saveResult = save(trfDO);
        long trfId = saveResult.getLeft();
        MessageReq messageReq = saveResult.getRight();
        TrfHeaderDOV2 header = trfDO.getHeader();
        header.setTrfId(trfId);
        //保存customer
        if (CollUtil.isNotEmpty(trfDO.getCustomerList())) {
            trfCustomerDomainService.batchSave(trfId, trfDO.getCustomerList());
        }

        //保存lab
        TrfLabDOV2 lab = header.getLab();
        if (Objects.nonNull(lab)) {
            lab.setTrfId(trfId);
            trfLabDomainService.save(lab);
        }

        //保存service requirement
        if (Objects.nonNull(trfDO.getServiceRequirement())) {
            TrfServiceRequirementDOV2 serviceRequirement = trfDO.getServiceRequirement();
            serviceRequirement.setTrfId(trfId);
            trfServiceRequirementDomainService.save(trfDO.getServiceRequirement());
        }

        if (CollUtil.isNotEmpty(trfDO.getAttachmentList())) {
            trfAttachmentDomainService.batchSave(trfId, trfDO.getAttachmentList());
        }

        List<TrfProductDOV2> trfProductDOV2List = CollUtil.newArrayList();
        if (Objects.nonNull(trfDO.getProduct())) {
            TrfProductDOV2 product = trfDO.getProduct();
            product.setProductType(ProductType.Product);
            product.setTrfNo(header.getTrfNo());
            trfProductDOV2List.add(trfDO.getProduct());
        }
        if (CollUtil.isNotEmpty(trfDO.getSampleList())) {
            for (TrfProductDOV2 trfProductDOV2 : trfDO.getSampleList()) {
                trfProductDOV2.setProductType(ProductType.Sample);
                trfProductDOV2.setTrfNo(header.getTrfNo());
            }
            trfProductDOV2List.addAll(trfDO.getSampleList());
        }
        if (CollUtil.isNotEmpty(trfProductDOV2List)) {
            trfProductDomainService.batchSave(trfId, trfProductDOV2List);
        }

        if (CollUtil.isNotEmpty(trfDO.getTestSampleList())) {
            trfTestSampleDomainService.batchSave(trfId, trfDO.getTestSampleList());
        }

        if (CollUtil.isNotEmpty(trfDO.getTestLineList())) {
            trfTestItemDomainService.batchSave(trfId, trfDO.getTestLineList());
        }
//        if (Func.isNotEmpty(trfDO.getOrder())) {
//            // TODO create DomainService不应该存在bind逻辑
//            trfOrderDomainService.bind(trfId, trfDO.getOrder());
//        }
        if (Func.isNotEmpty(trfDO.getCareLabelList())) {
            trfCareLabelDomainService.batchSave(trfId, trfDO.getCareLabelList());
        }

        return Pair.of(trfDO, messageReq);
    }

    private Pair<Long, MessageReq> save(TrfDOV2 trfDOV2) {
        TrfHeaderDOV2 trfHeaderDOV2 = trfDOV2.getHeader();
        TrfInfoPO trfInfoPO = null;
        boolean createTrf = false;
        if (Objects.nonNull(trfHeaderDOV2.getTrfId())) {
            trfInfoPO = trfInfoMapper.selectByPrimaryKey(trfHeaderDOV2.getTrfId());
        } else if (Func.isNotEmpty(trfHeaderDOV2.getRefSystemId()) && Func.isNotEmpty(trfHeaderDOV2.getTrfNo())) {
            trfInfoPO = this.selectByTrfNo(trfHeaderDOV2.getRefSystemId(), trfHeaderDOV2.getTrfNo());
        }
        if (Func.isEmpty(trfInfoPO)) {
            trfInfoPO = new TrfInfoPO();
            if (Objects.nonNull(trfHeaderDOV2.getTrfId())) {
                trfInfoPO.setId(trfHeaderDOV2.getTrfId());
            } else {
                trfInfoPO.setId(idService.nextId());
            }
            trfInfoPO.setPendingFlag(PendingFlagEnum.UnPending.getType());
            createTrf = true;
        } else {
            Integer status = trfInfoPO.getStatus();
            TrfStatusEnum statusEnum = TrfStatusEnum.findStatus(status);
            String statusStr;
            if (Func.isNotEmpty(statusEnum)) {
                statusStr = statusEnum.getTextEn();
            } else {
                statusStr = String.valueOf(status);
            }
            boolean checkCustomer = checkCustomer(trfInfoPO.getRefSystemId());
            if (!checkCustomer) {
                if (!Objects.equals(status, TrfStatusEnum.ToBeBound.getStatus())
                        && !Objects.equals(trfInfoPO.getActiveIndicator(), ActiveIndicatorEnum.Inactive.getStatus())) {
                    throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, String.format("TRF {%s} status is {%s} and cannot be import", trfInfoPO.getTrfNo(), statusStr));
                }
            }
        }
        TrfLabDOV2 lab = trfHeaderDOV2.getLab();

        trfHeaderDOV2.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
        trfInfoPO.setTrfNo(trfHeaderDOV2.getTrfNo());
        Optional.ofNullable(trfHeaderDOV2.getSgsTrfNo()).ifPresent(trfInfoPO::setSgsTrfNo);
        trfInfoPO.setServiceType(trfHeaderDOV2.getServiceType());
        trfInfoPO.setProductCategory(trfHeaderDOV2.getProductCategory());
        trfInfoPO.setSelfTestFlag(trfHeaderDOV2.getSelfTestFlag());
        trfInfoPO.setSource(trfHeaderDOV2.getSource());
        trfInfoPO.setChannel(trfHeaderDOV2.getChannel());
        trfInfoPO.setRefSystemId(trfHeaderDOV2.getRefSystemId());
        trfInfoPO.setSystemId(trfHeaderDOV2.getSystemId());
        trfInfoPO.setSampleLevel(trfHeaderDOV2.getSampleLevel());
        trfInfoPO.setIntegrationLevel(trfHeaderDOV2.getIntegrationLevel());
        trfInfoPO.setCustomerTrfId(trfHeaderDOV2.getCustomerTrfId());
        trfInfoPO.setTrfReportLevel(trfHeaderDOV2.getTrfReportLevel());
        if (Objects.nonNull(lab)) {
            trfInfoPO.setLabId(lab.getLabId());
            trfInfoPO.setLabCode(lab.getLabCode());
            trfInfoPO.setBuId(lab.getBuId());
            trfInfoPO.setBuCode(lab.getBuCode());
        }
        if (Objects.nonNull(trfHeaderDOV2.getOthers())) {
            TrfOtherDOV2 others = trfHeaderDOV2.getOthers();
            trfInfoPO.setTrfRemark(others.getTrfRemark());
            if (Objects.nonNull(others.getPending())) {
                TrfPendingDOV2 pending = others.getPending();
                trfInfoPO.setPendingType(pending.getPendingType());
                trfInfoPO.setPendingFlag(pending.getPendingFlag());
                trfInfoPO.setPendingRemark(pending.getPendingRemark());
            }
        }

        Optional<String> buOpt = Optional.ofNullable(trfHeaderDOV2.getLab())
                .flatMap(labDO -> StringUtils.isBlank(labDO.getBuCode()) ? Optional.of("") : Optional.of(labDO.getBuCode()));
        Optional<String> customerGroupCode = Optional.ofNullable(trfDOV2.getCustomerList())
                .flatMap(customerList ->
                        customerList.stream()
                                .filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Buyer.getUsage()))
                                .map(TrfCustomerDOV2::getCustomerGroupCode)
                                .filter(Objects::nonNull)
                                .findFirst()
                )
                .map(Object::toString);
        Optional<String> buyerBossNO = Optional.ofNullable(trfDOV2.getCustomerList())
                .flatMap(customerList ->
                        customerList.stream()
                                .filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Buyer.getUsage()))
                                .map(TrfCustomerDOV2::getBossNo)
                                .filter(Objects::nonNull)
                                .findFirst()
                )
                .map(Object::toString);
        AF.ap3(configClient::getCustomerConfig, buOpt, customerGroupCode, buyerBossNO)
                .map(ConfigInfo::getConfigValue)
                .map(configValue -> JSON.parseObject(configValue, CustomerGeneralConfig.class))
                .map(CustomerGeneralConfig::getFeatures)
                .ifPresent(features -> {
                    JSONObject extFields = Optional.ofNullable(trfHeaderDOV2.getExtFields())
                            .filter(ext -> StringUtils.isNotBlank(ext.toString().trim()))
                            .map(ext -> JSON.parseObject(JSON.toJSONString(ext)))
                            .orElse(new JSONObject());
                    JSONObject ctx = new JSONObject();
                    ctx.put("features", features);
                    extFields.put("ctx", ctx);
                    trfHeaderDOV2.setExtFields(extFields);
                });
        JSONObject extFields = Optional.ofNullable(trfHeaderDOV2.getExtFields())
                .filter(ext -> StringUtils.isNotBlank(ext.toString().trim()))
                .map(ext -> JSON.parseObject(JSON.toJSONString(ext)))
                .orElse(new JSONObject());
        extFields.putIfAbsent(TRF_VERSION_KEY, TRF_VERSION_1692);
        trfInfoPO.setExtFields(JSON.toJSONString(extFields));
        trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
        // 构建MessageReq对象，但不发送
        MessageReq req = null;
        if (createTrf) {
            UserInfo localUser = UserHelper.getLocalUser();
            String localUserName = null;
            if (Func.isNotEmpty(localUser)) {
                localUserName = localUser.getRegionAccount();
            }
            trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
            if (Func.isNotBlank(trfHeaderDOV2.getCreateBy())) {
                trfInfoPO.setCreatedBy(trfHeaderDOV2.getCreateBy());
            } else {
                trfInfoPO.setCreatedBy(Func.isNotBlank(localUserName) ? localUserName : USER_DEFAULT);
            }
            trfInfoPO.setStatus(trfHeaderDOV2.getTrfStatus());
            trfInfoPO.setCreatedDate(DateUtil.now());
            if (Func.isNotBlank(trfHeaderDOV2.getCreateBy())) {
                trfInfoPO.setModifiedBy(trfHeaderDOV2.getCreateBy());
            } else {
                trfInfoPO.setModifiedBy(Func.isNotBlank(localUserName) ? localUserName : USER_DEFAULT);
            }
            trfInfoPO.setModifiedDate(DateUtil.now());
            trfInfoPO.setInterfaceExclude(Func.isEmpty(trfHeaderDOV2.getInterfaceExclude()) ? ActiveIndicatorEnum.Inactive.getStatus() : trfHeaderDOV2.getInterfaceExclude());
            trfInfoMapper.insert(trfInfoPO);
        } else {
            updateTrf(trfInfoPO);
            if (needSendTrfUpdatedToKafka(trfInfoPO)) {
                try {
                    req = new MessageReq();
                    Payload updated = Payload.trfUpdated(trfInfoPO.getRefSystemId(), trfInfoPO.getTrfNo());
                    req.setAction(updated.getKind());
                    req.setLabCode(trfInfoPO.getLabCode());
                    req.setProductLineCode(trfInfoPO.getBuCode());
                    req.setData(objectMapper.writeValueAsString(updated));
                } catch (Exception e) {
                    log.warn("Failed to create kafka message for trf update. trfNo:{}, refSystemId:{}", trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId(), e);
                    req = null;
                }
            }
        }
        return Pair.of(trfInfoPO.getId(), req);
    }

    private boolean needSendTrfUpdatedToKafka(TrfInfoPO trfInfoPO) {
        if (trfUpdatedConfig.isDisabled()) {
            return false;
        }
        if (CollectionUtils.isEmpty(trfUpdatedConfig.getRefSystemIdWhiteList())) {
            return true;
        }
        return Objects.nonNull(trfInfoPO.getRefSystemId()) && trfUpdatedConfig.getRefSystemIdWhiteList().contains(trfInfoPO.getRefSystemId());
    }

    public boolean checkCustomer(Integer refSystemId) {
        GetCustomerConfigReq req = new GetCustomerConfigReq();
        req.setRefSystemId(refSystemId);
        ConfigInfo customerConfig = configClient.getCustomerConfig(req);
        if (Func.isEmpty(customerConfig)) {
            return false;
        }
        CustomerGeneralConfig customerGeneralConfig = JSON.parseObject(customerConfig.getConfigValue(), CustomerGeneralConfig.class);
        if (Func.isEmpty(customerGeneralConfig)) {
            return false;
        }

        String updateTrf = customerGeneralConfig.getUpdateTrf();
        if (Func.isBlank(updateTrf)) {
            return false;
        } else if (Objects.equals(updateTrf, ActiveType.Enable.getStatus().toString())) {
            return true;
        }
        return false;
    }


    public TrfStatusResult toOrder(TrfStatusControlDO statusControlDO) {
        TrfOrderDOV2 order = statusControlDO.getOrder();
        checkOrderInfo(order, "The order status is required");
        order.setOrderStatus(TrfOrderStatusEnum.ToOrder.getStatus());
        return statusControl(statusControlDO);
    }

    private ErrorCode checkOrderInfo(TrfOrderDOV2 order, String The_order_status_is_required) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(order, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), The_order_status_is_required);
        return errorCode;
    }


    public TrfStatusResult toQuotation(TrfStatusControlDO statusControlDO) {
        TrfOrderDOV2 order = statusControlDO.getOrder();
        checkOrderInfo(order, "The order status is required");
        order.setOrderStatus(TrfOrderStatusEnum.ToQuotation.getStatus());
        return statusControl(statusControlDO);
    }


    public TrfStatusResult confirm(TrfStatusControlDO statusControlDO) {
        TrfOrderDOV2 order = statusControlDO.getOrder();
        checkOrderInfo(order, "The order status is required");
        order.setOrderStatus(TrfOrderStatusEnum.Confirmed.getStatus());
        return statusControl(statusControlDO);
    }


    public TrfStatusResult testing(TrfStatusControlDO statusControlDO) {
        TrfOrderDOV2 order = statusControlDO.getOrder();
        checkOrderInfo(order, "The order status is required");
        order.setOrderStatus(TrfOrderStatusEnum.Testing.getStatus());
        return statusControl(statusControlDO);
    }

    private void canReviseReport(TrfInfoPO trfInfoPO, TrfStatusControlDO statusControlDO) {
        TrfOrderDOV2 order = statusControlDO.getOrder();
        //set order status
        order.setOrderStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Testing.getStatus());
        boolean rejectRevisedOnLightMode = false;
        if (Func.isNotEmpty(trfInfoPO)) {
            //SCI-1299
            TrfSyncReq syncReq = statusControlDO.getSyncReq();
            rejectRevisedOnLightMode = configClient.rejectRevisedOnLightMode(syncReq.getHeader().getRefSystemId());
            boolean notLight = configClient.notLightMode(syncReq.getHeader().getRefSystemId(), syncReq.getSystemId(), syncReq.getProductLineCode(), syncReq.getOrder().getCustomerList());
            if(notLight || rejectRevisedOnLightMode) {
                if (!Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus())
                        && !Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Closed.getStatus())
                        && !Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus())) {
                    throw new BizException("The Trf is " + com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getText(trfInfoPO.getStatus()) + " Cannot SyncReviseReport！");
                }
            } else {
                if (!Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus())
                        && !Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Closed.getStatus())
                        && !Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus())
                        && !Objects.equals(trfInfoPO.getStatus(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Testing.getStatus())) {
                    throw new BizException("The Trf is " + com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getText(trfInfoPO.getStatus()) + " Cannot SyncReviseReport！");
                }
            }
        }
        List<TrfReportDOV2> reportList = statusControlDO.getReportList();

        ErrorCode errorCode = checkOrderInfo(order, "The order field is required");
        ErrorAssert.notNull(order.getOrderStatus(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order status is required");
        ErrorAssert.isTrue(!CollectionUtil.hasNull(reportList), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The reportList field is required");

        // 校验trf是否存在
        ErrorAssert.notNull(trfInfoPO, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The trf {} not found", statusControlDO.getTrfNo()));

        List<String> reportNos = reportList.stream().map(TrfReportDOV2::getReportNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(reportNos)) {
            ErrorCode reportNoIsEmpty = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(reportNoIsEmpty, "the reportNo of request is empty");
        }

        List<TrfReportPO> trfReports = trfReportDomainService.selectByTrfIdAndReportNo(trfInfoPO.getId(), reportNos);
        Boolean requiredCompletedOfReport = Optional.ofNullable(trfInfoPO.getExtFields())
                .map(JSON::parseObject)
                .filter(extField -> extField.containsKey(TRF_VERSION_KEY))
                .map(extField -> extField.getInteger(TRF_VERSION_KEY) >= TRF_VERSION_1692)
                .orElse(false);
        if(rejectRevisedOnLightMode && requiredCompletedOfReport) {
            List<String> illegalReportNos = trfReports.stream()
                    .filter(trfReport -> !ReportStatus.check(trfReport.getReportStatus(), ReportStatus.Completed, ReportStatus.Approved))
                    .map(TrfReportPO::getReportNo)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(illegalReportNos)) {
                ErrorCode reportNoIsEmpty = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
                throw new CustomerBizException(reportNoIsEmpty, "reportNo " +illegalReportNos+" is not completed!");
            }
        }
        List<String> requiredCustomerConfirmedReportNo = trfReports.stream()
                .filter(report ->
                        Objects.nonNull(report.getCustomerConfirmStatus())
                                && report.getCustomerConfirmStatus() == CustomerConfirmReportEnum.Pending)
                .map(TrfReportPO::getReportNo)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(requiredCustomerConfirmedReportNo)) {
            ErrorCode reportNoIsEmpty = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.SYNC, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(reportNoIsEmpty, "reportNo " +requiredCustomerConfirmedReportNo+" need confirmed from customer");
        }
    }

    public void canReviseReport(TrfStatusControlDO statusControlDO) {
        TrfInfoPO trfInfoPO = selectByTrfNo(statusControlDO.getRefSystemId(), statusControlDO.getTrfNo());
        canReviseReport(trfInfoPO, statusControlDO);
        canStatusControl(statusControlDO);
    }

    public TrfStatusResult reviseReport(TrfStatusControlDO statusControlDO) {
        TrfInfoPO trfInfoPO = selectByTrfNo(statusControlDO.getRefSystemId(), statusControlDO.getTrfNo());
        canReviseReport(trfInfoPO, statusControlDO);

        List<TrfReportDOV2> reportList = statusControlDO.getReportList();
        reportList.forEach(rpt -> rpt.setTrfId(trfInfoPO.getId()));

        // 如果report状态属于替换/删除类状态，需要批量cancel原report
        List<Integer> toCancelStatus = Arrays.asList(ReportStatus.Reworked.getCode(), ReportStatus.Replaced.getCode(), ReportStatus.Cancelled.getCode());
        List<TrfReportDOV2> toCancelReportList = reportList.stream().filter(report -> toCancelStatus.contains(report.getReportStatus())).collect(Collectors.toList());
        reportDataService.batchCancel(toCancelReportList, statusControlDO.getOrder().getSystemId());

        // 更新TRF状态
        return statusControl(statusControlDO);
    }


    public TrfStatusResult cancelOrder(TrfStatusControlDO statusCtrlDo) {
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.REQUESTNULL);
        checkStatusCtrlDoData(statusCtrlDo, errorCode, order);

        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
        //cancel的订单
        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");
        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.STATUSERROR);
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");

        //检查可unbind的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode());
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());

        ErrorAssert.notNull(whiteItem, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The CancelOrder action not allowed");
        trfOrderDomainService.unbind(trfInfoPO.getId(), statusCtrlDo.getOrder());

        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        int trfOrderSize = CollUtil.size(trfOrderList);

        TrfStatusResult trfStatusResult = new TrfStatusResult();
        trfStatusResult.setOldTrfStatus(trfInfoPO.getStatus());
        trfStatusResult.setNewTrfStatus(trfInfoPO.getStatus());
        if (trfOrderSize == 0) {
            TrfStatusEnum newTrfStatus = TrfStatusEnum.ToBeBound;
            trfStatusResult.setNewTrfStatus(newTrfStatus.getStatus());
        } else {
            Map<String, Integer> statusMapping = controlConfig.getStatusMapping();
            int statusIndexToUse = -1;
            for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
                Integer dbOrderStatusToTrfStatus = statusMapping.get(Func.toStr(trfOrderDOV2.getOrderStatus()));
                int dbStatusIdx = calcTrfStatusIndex(controlConfig.getStatusFlow(), dbOrderStatusToTrfStatus);
                if (statusIndexToUse < dbStatusIdx) {
                    statusIndexToUse = dbStatusIdx;
                }
            }

        }
        if (trfStatusResult.trfStatusChanged()) {
            updateTrfStatus(trfInfoPO, trfStatusResult.getNewTrfStatus());
        }
        return trfStatusResult;
    }

    private void checkStatusCtrlDoData(TrfStatusControlDO statusCtrlDo, ErrorCode errorCode, TrfOrderDOV2 order) {
        ErrorAssert.notNull(statusCtrlDo, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The request is required");
        ErrorAssert.notNull(statusCtrlDo.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId field is required");
        ErrorAssert.notEmpty(statusCtrlDo.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo field is required");

        checkOrderInfo(order, errorCode);
    }

    public TrfStatusResult pending(TrfStatusControlDO statusCtrlDo) {
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.PENDING, ErrorTypeEnum.REQUESTNULL);
        checkStatusCtrlDoData(statusCtrlDo, errorCode, order);

        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.PENDING, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");
        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.PENDING, ErrorTypeEnum.STATUSERROR);
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");
        trfInfoPO.setPendingType(statusCtrlDo.getPendingType());
        trfInfoPO.setPendingRemark(statusCtrlDo.getReasonRemark());

        //cancel的订单
//        1vN时是否可以多次pending吗? 是的话则下面不能设置断言

        //检查action的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode());
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        ErrorAssert.notNull(actionStatusRule, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this trf: {} cannot {}", trfInfoPO.getTrfNo(), statusCtrlDo.getAction()));
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());
        if (com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.check(trfInfoPO.getStatus())) {
            errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.PENDING, ErrorTypeEnum.REVISE_STATUS_ERROR);
        }
        ErrorAssert.notNull(whiteItem, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The Pending action not allowed");

        trfOrderDomainService.pending(trfInfoPO.getId(), statusCtrlDo.getOrder());

        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        TrfStatusResult trfStatusResult = new TrfStatusResult();
        trfStatusResult.setOldPendingFlag(trfInfoPO.getPendingFlag());
        trfStatusResult.setNewPendingFlag(trfInfoPO.getPendingFlag());

        // 有一个pending就算pending
        Integer newPendingFlag = PendingFlagEnum.UnPending.getType();
        for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
            if (PendingFlagEnum.Pending.getType().equals(trfOrderDOV2.getPendingFlag())) {
                newPendingFlag = PendingFlagEnum.Pending.getType();
                break;
            }
        }
        trfStatusResult.setNewPendingFlag(newPendingFlag);

        // save trf log
        trfLogDomainService.save(statusCtrlDo, ChangeTypeEnum.TAGGING.getCode(), trfInfoPO.getStatus(), trfInfoPO.getStatus(), trfInfoPO.getPendingFlag());

        if (trfStatusResult.pendingFlagChanged()) {
            trfInfoPO.setPendingFlag(newPendingFlag);
            updateTrf(trfInfoPO);
        }
        return trfStatusResult;
    }


    public TrfStatusResult unpending(TrfStatusControlDO statusCtrlDo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.UNPENDING, ErrorTypeEnum.REQUESTNULL);
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        checkStatusCtrlDoData(statusCtrlDo, errorCode, order);

        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.UNPENDING, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");

        errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.UNPENDING, ErrorTypeEnum.STATUSERROR);
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");
        if (com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.check(trfInfoPO.getStatus())) {
            errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.UNPENDING, ErrorTypeEnum.REVISE_STATUS_ERROR);
        }
        ErrorAssert.isTrue(PendingFlagEnum.check(trfInfoPO.getPendingFlag(), PendingFlagEnum.Pending), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already un pending");
        //cancel的订单

        //检查action的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode());
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        ErrorAssert.notNull(actionStatusRule, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this trf: {} cannot {}", trfInfoPO.getTrfNo(), statusCtrlDo.getAction()));
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());
        if (com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.check(trfInfoPO.getStatus())) {
            errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.UNPENDING, ErrorTypeEnum.REVISE_STATUS_ERROR);
        }
        ErrorAssert.notNull(whiteItem, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The UnPending action not allowed");
        trfOrderDomainService.unPending(trfInfoPO.getId(), statusCtrlDo.getOrder());

        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        TrfStatusResult trfStatusResult = new TrfStatusResult();
        trfStatusResult.setOldPendingFlag(trfInfoPO.getPendingFlag());
        trfStatusResult.setNewPendingFlag(trfInfoPO.getPendingFlag());

        // 全部unpending才算unpending
        Integer newPendingFlag = PendingFlagEnum.UnPending.getType();
        for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
            if (PendingFlagEnum.Pending.getType().equals(trfOrderDOV2.getPendingFlag())) {
                newPendingFlag = PendingFlagEnum.Pending.getType();
                break;
            }
        }
        trfStatusResult.setNewPendingFlag(newPendingFlag);

        // save trf log
        trfLogDomainService.save(statusCtrlDo, ChangeTypeEnum.TAGGING.getCode(), trfInfoPO.getStatus(), trfInfoPO.getStatus(), trfInfoPO.getPendingFlag());

        if (trfStatusResult.pendingFlagChanged()) {
            trfInfoPO.setPendingFlag(newPendingFlag);
            updateTrf(trfInfoPO);
        }
        return trfStatusResult;
    }

    public TrfStatusResult unbind(TrfStatusControlDO statusCtrlDo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(statusCtrlDo, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The param is required");
        ErrorAssert.notNull(statusCtrlDo.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId field is required");
        ErrorAssert.notEmpty(statusCtrlDo.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo field is required");

        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        checkOrderInfo(order, errorCode);

        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
//        //cancel的订单
        ErrorCode trfErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.notNull(trfInfoPO, trfErrorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");
        trfErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATACONFLICT);
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), trfErrorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");

        //检查可unbind的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode());
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());

        ErrorCode stautusErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.STATUSERROR);
        ErrorAssert.notNull(whiteItem, stautusErrorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The Unbind action not allowed");

        trfOrderDomainService.unbind(trfInfoPO.getId(), statusCtrlDo.getOrder());
        // 将report状态置为无效
        trfReportDomainService.updateReportAsInvalid(Arrays.asList(trfInfoPO.getTrfNo()), trfInfoPO.getRefSystemId(), statusCtrlDo.getOrder().getOrderNo());
        deleteTrfReportLevel(statusCtrlDo.getOrder().getOrderNo(), trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());
        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        int trfOrderSize = CollUtil.size(trfOrderList);

        TrfStatusResult trfStatusResult = new TrfStatusResult();
        trfStatusResult.setOldTrfStatus(trfInfoPO.getStatus());
        trfStatusResult.setNewTrfStatus(trfInfoPO.getStatus());
        trfStatusResult.setOldPendingFlag(trfInfoPO.getPendingFlag());
        trfStatusResult.setNewPendingFlag(trfInfoPO.getPendingFlag());

        if (trfOrderSize == 0) {
            TrfStatusEnum newTrfStatus = TrfStatusEnum.ToBeBound;
            trfStatusResult.setNewTrfStatus(newTrfStatus.getStatus());
            trfStatusResult.setNewPendingFlag(PendingFlagEnum.UnPending.getType());
        } else {
            Map<String, Integer> statusMapping = controlConfig.getStatusMapping();
            int statusIndexToUse = -1;
            for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
                Integer dbOrderStatusToTrfStatus = statusMapping.get(Func.toStr(trfOrderDOV2.getOrderStatus()));
                int dbStatusIdx = calcTrfStatusIndex(controlConfig.getStatusFlow(), dbOrderStatusToTrfStatus);
                if (statusIndexToUse < dbStatusIdx) {
                    statusIndexToUse = dbStatusIdx;
                }
            }

            boolean allMatchedCompleted = trfOrderList.stream().allMatch(trfOrderDOV2 ->
                    Objects.equals(TrfOrderStatusEnum.Completed.getStatus(), trfOrderDOV2.getOrderStatus()));
            boolean allMatchedClosed = trfOrderList.stream().allMatch(trfOrderDOV2 ->
                    Objects.equals(TrfOrderStatusEnum.Closed.getStatus(), trfOrderDOV2.getOrderStatus()));

            // 剩余订单状态全为Completed则trf直接更新成Completed
            if (allMatchedCompleted) {
                trfStatusResult.setNewTrfStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus());
            }
            // 剩余订单状态全为Closed则trf直接更新成Closed
            if (allMatchedClosed) {
                trfStatusResult.setNewTrfStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Closed.getStatus());
            }
            // 不全为Completed时需要剔除Completed和Closed状态，取最靠后的状态
            else {
                List<Integer> orderStatusList =
                        trfOrderList.stream().
                                map(TrfOrderDOV2::getOrderStatus).
                                filter(orderStatus -> !Objects.equals(orderStatus, TrfOrderStatusEnum.Completed.getStatus()) && !Objects.equals(orderStatus, TrfOrderStatusEnum.Closed.getStatus())).
                                distinct().sorted().collect(Collectors.toList());
                if (Func.isEmpty(orderStatusList)) {
                    Optional<Integer> min = trfOrderList.stream().map(TrfOrderDOV2::getOrderStatus).min(Comparator.naturalOrder());
                    min.ifPresent(status -> trfStatusResult.setNewTrfStatus(status));
                } else {
                    trfStatusResult.setNewTrfStatus(statusMapping.get(String.valueOf(orderStatusList.get(orderStatusList.size() - 1))));
                }
            }
        }
        // 重新计算trf状态
        Integer latestTrfStatus = this.calculateTrfStatus(trfStatusResult.getNewTrfStatus(), trfInfoPO.getStatus(), statusCtrlDo.getAction());
        trfStatusResult.setNewTrfStatus(latestTrfStatus);

        if (trfStatusResult.trfStatusChanged()) {
            updateTrfStatus(trfInfoPO, trfStatusResult.getNewTrfStatus());
        } else {
            updateTrf(trfInfoPO);
        }

        Integer newPendingFlag = PendingFlagEnum.UnPending.getType();
        for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
            if (PendingFlagEnum.Pending.getType().equals(trfOrderDOV2.getPendingFlag())) {
                newPendingFlag = PendingFlagEnum.Pending.getType();
                break;
            }
        }

        trfLogDomainService.save(statusCtrlDo, ChangeTypeEnum.ASSOCIATE.getCode(), trfInfoPO.getStatus(), trfInfoPO.getStatus(), trfInfoPO.getPendingFlag());

        trfStatusResult.setNewPendingFlag(newPendingFlag);
        if (trfStatusResult.pendingFlagChanged()) {
            updateTrf(trfInfoPO);
        }
        return trfStatusResult;
    }

    private void deleteTrfReportLevel(String orderNo, String trfNo, Integer refSystemId) {
        if (Func.isBlank(orderNo) || Func.isBlank(trfNo) || Func.isEmpty(refSystemId)) {
            throw new BizException("orderNo or trfNo or refSystemId is null");
        }
        TrfReportLevelExample example = new TrfReportLevelExample();
        example.createCriteria().andOrderNoEqualTo(orderNo).andTrfNoEqualTo(trfNo).andRefSystemIdEqualTo(refSystemId);
        trfReportLevelMapper.deleteByExample(example);
    }

    private void deleteAllTrfReportLevel(String trfNo, Integer refSystemId) {
        if (Func.isBlank(trfNo) || Func.isEmpty(refSystemId)) {
            throw new BizException("orderNo or trfNo or refSystemId is null");
        }
        TrfReportLevelExample example = new TrfReportLevelExample();
        example.createCriteria().andTrfNoEqualTo(trfNo).andRefSystemIdEqualTo(refSystemId);
        trfReportLevelMapper.deleteByExample(example);
    }

    public TrfStatusResult bind(TrfStatusControlDO trfDO) {
        // 数据校验 并设置 获取TrfInfo
        return transactionTemplate.execute((trans) -> {
            TrfInfoPO trfInfoPO = selectByTrfNo(trfDO.getRefSystemId(), trfDO.getTrfNo());
            TrfStatusResult trfStatusResult = new TrfStatusResult();
            trfStatusResult.setOldTrfStatus(trfInfoPO.getStatus());
            trfStatusResult.setNewTrfStatus(TrfStatusEnum.Invoiced.getStatus());
            trfInfoPO.setStatus(TrfStatusEnum.Invoiced.getStatus());

            trfOrderDomainService.bind(trfInfoPO.getId(), trfDO.getOrder());

            updateTrf(trfInfoPO);
            // save trf log
            trfLogDomainService.save(trfDO, ChangeTypeEnum.ASSOCIATE.getCode(), trfInfoPO.getStatus(), trfInfoPO.getStatus(), trfInfoPO.getPendingFlag());
            return trfStatusResult;
        });
    }

    public TrfStatusResult bind(TrfHeaderDOV2 trfDO, TrfOrderDOV2 trfOrderDOV2) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfDO, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trf info cannot null!");
        ErrorAssert.notNull(trfOrderDOV2, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfOrder info cannot null!");
        ErrorAssert.notNull(trfDO.getTrfNo(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfNo cannot null!");
        ErrorAssert.notNull(trfDO.getRefSystemId(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId cannot null!");
        // 数据校验 并设置 获取TrfInfo
        return transactionTemplate.execute((trans) -> {
            TrfInfoPO trfInfoPO = selectByTrfNo(trfDO.getRefSystemId(), trfDO.getTrfNo());
            TrfStatusResult trfStatusResult = new TrfStatusResult();
            trfStatusResult.setOldTrfStatus(trfInfoPO.getStatus());
            trfStatusResult.setNewTrfStatus(TrfStatusEnum.Invoiced.getStatus());
            trfInfoPO.setStatus(TrfStatusEnum.Invoiced.getStatus());

            trfOrderDomainService.bind(trfInfoPO.getId(), trfOrderDOV2);

            updateTrf(trfInfoPO);

            // save trf log
            trfLogDomainService.save(trfDO.getTrfNo(), trfDO.getRefSystemId(), null, null, ChangeTypeEnum.ASSOCIATE.getCode(), trfInfoPO.getStatus(), trfInfoPO.getStatus(), trfInfoPO.getPendingFlag());

            return trfStatusResult;
        });
    }


    /**
     * 1.保存/更新 trf 下的Sample testLine matrix 信息
     * 2.修改trf状态
     *
     * @param trfDO
     * @return
     */
    public TrfStatusResult complete(TrfStatusControlDO trfDO) {
        return statusControl(trfDO);
    }

    /**
     * 1.保存/更新 trf 下的Sample testLine matrix 信息
     * 2.修改trf状态
     *
     * @param trfDO
     * @return
     */
    public TrfStatusResult close(TrfStatusControlDO trfDO) {
        return statusControl(trfDO);
    }


    private void updateTrfStatus(TrfInfoPO trfInfoPO, Integer trfStatus) {
        // 判断当前状态  当前状态 > 推送的状态  则 不更新Trf状态
        // 更新状态
        trfInfoPO.setStatus(trfStatus);
        updateTrf(trfInfoPO);
    }

    private TrfHeaderDOV2 getResultTrfDO(TrfInfoPO trfInfoPO) {
        TrfHeaderDOV2 headerDTO = new TrfHeaderDOV2();
        headerDTO.setTrfId(trfInfoPO.getId());
        headerDTO.setTrfNo(trfInfoPO.getTrfNo());
        headerDTO.setRefSystemId(trfInfoPO.getRefSystemId());
        return headerDTO;
    }


    public TrfInfoPO getTrfInfoByTrfNo(String trfNo, Integer refSystemId) {
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria().andRefSystemIdEqualTo(refSystemId).andTrfNoEqualTo(trfNo).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoPOS = trfInfoMapper.selectByExample(trfInfoExample);
        if (Func.isNotEmpty(trfInfoPOS)) {
            return trfInfoPOS.get(0);
        }
        return null;
    }

    public void updateByPrimaryKey(TrfInfoPO trfInfoPO) {
        trfInfoMapper.updateByPrimaryKey(trfInfoPO);
    }

    public TrfInfoPO selectByTrfId(Long trfId) {
        return trfInfoMapper.selectByPrimaryKey(trfId);
    }

    /**
     * @param ctrl
     * @return 如果trf状态变更返回true，否则返回false
     */
    public TrfStatusResult statusControl(TrfStatusControlDO ctrl) {
        return transactionTemplate.execute(transactionStatus -> doStatusControl(ctrl));
    }

    private void canStatusControl(TrfStatusControlDO statusCtrlDo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        TrfInfoPO trfInfoPO = checkStatusCtrlDoParam(statusCtrlDo, errorCode);
        if (Func.isEmpty(trfInfoPO.getSystemId())) {
            trfInfoPO.setSystemId(statusCtrlDo.getSystemId());
        }

        //2 获取配置的状态管控规则
        ErrorCode configErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.STATUSCONTROL, ErrorTypeEnum.DATANOTFOUND);
        TrfStatusControlConfig statusControlConfig = getTrfStatusControlConfig(statusCtrlDo, trfInfoPO, configErrorCode);

        Map<String, TrfActionStatusRule> actionStatusMapping = statusControlConfig.getActionStatusMapping();
        ErrorAssert.notNull(actionStatusMapping, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have action status mapping!", trfInfoPO.getRefSystemId()));
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        ErrorAssert.notNull(actionStatusRule, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have action status rule!", trfInfoPO.getRefSystemId()));
        //外部状态到trf status映射
        Map<String, Integer> statusMapping = statusControlConfig.getStatusMapping();

        //trf 状态控制 状态前进规则:0 1 2 4 8
        Integer statusRule = statusControlConfig.getStatusRule();
        //action级别状态控制规则
        Integer nodeStatusRule = statusControlConfig.getActionStatusMapping().get(statusCtrlDo.getAction()).getStatusRule();
        statusRule = getStatusRule(statusCtrlDo, nodeStatusRule, trfInfoPO, configErrorCode, statusRule);

        Integer[] statusFlow = statusControlConfig.getStatusFlow();

        //trf状态控制依赖order时，验证order必填
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        checkOrderInfo(order, errorCode);
        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        checkOrderSystemId(trfOrderList, order, configErrorCode);
    }

    private TrfStatusResult doStatusControl(TrfStatusControlDO statusCtrlDo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        TrfInfoPO trfInfoPO = checkStatusCtrlDoParam(statusCtrlDo, errorCode);
        if (Func.isEmpty(trfInfoPO.getSystemId())) {
            trfInfoPO.setSystemId(statusCtrlDo.getSystemId());
        }

        //2 获取配置的状态管控规则
        ErrorCode configErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.STATUSCONTROL, ErrorTypeEnum.DATANOTFOUND);
        TrfStatusControlConfig statusControlConfig = getTrfStatusControlConfig(statusCtrlDo, trfInfoPO, configErrorCode);

        Map<String, TrfActionStatusRule> actionStatusMapping = statusControlConfig.getActionStatusMapping();
        ErrorAssert.notNull(actionStatusMapping, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have action status mapping!", trfInfoPO.getRefSystemId()));
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        ErrorAssert.notNull(actionStatusRule, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have action status rule!", trfInfoPO.getRefSystemId()));
        //外部状态到trf status映射
        Map<String, Integer> statusMapping = statusControlConfig.getStatusMapping();

        //trf 状态控制 状态前进规则:0 1 2 4 8
        Integer statusRule = statusControlConfig.getStatusRule();

        updateTrfIntegrationLevel(trfInfoPO, statusControlConfig);

        //action级别状态控制规则
        Integer nodeStatusRule = statusControlConfig.getActionStatusMapping().get(statusCtrlDo.getAction()).getStatusRule();
        statusRule = getStatusRule(statusCtrlDo, nodeStatusRule, trfInfoPO, configErrorCode, statusRule);

        Integer[] statusFlow = statusControlConfig.getStatusFlow();

        //trf状态控制依赖order时，验证order必填
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        checkOrderInfo(order, errorCode);
        List<TrfOrderDOV2> trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        checkOrderSystemId(trfOrderList, order, configErrorCode);

        //3 关联订单的处理 ,顺序很重要不能变更，trfOrderStatus状态会变更
        boolean bindTrf = buildTrfRel(statusCtrlDo, trfInfoPO, actionStatusRule);

        //trf状态计算
        //输出trf order关键信息，方便问题排查
        for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
            log.info("Trf db info  trfNo={},trfStatus={},orderNo={},orderStatus={}", trfInfoPO.getTrfNo(), trfInfoPO.getStatus(), trfOrderDOV2.getOrderNo(), trfOrderDOV2.getOrderStatus());
        }

        //调用方预期的trf状态

        int trfStatusTo = trfInfoPO.getStatus();


        // //不限制：直接设置为目标状态
        // if (TrfStatusRuleEnum.UNLIMITED.getRule().equals(statusRule)) {
        //     Integer requestOrderStatusToTrfStatus = statusMapping.get(Func.toStr(order.getOrderStatus()));
        //     Assert.notNull(requestOrderStatusToTrfStatus, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.orderStatus incorrect");
        //     trfStatusTo = requestOrderStatusToTrfStatus;

        //     log.info("TrfNo={} 满足unlimited状态前进条件 ", trfInfoPO.getTrfNo());
        // }
        // //任一订单前进状态则前进trf status
        // else if (TrfStatusRuleEnum.ONE_ORDER.getRule().equals(statusRule)) {
        //     //request order status与db order status对比，计算是否满足前进状态
        //     Integer requestOrderStatus = order.getOrderStatus();
        //     Integer requestOrderStatusToTrfStatus = statusMapping.get(Func.toStr(requestOrderStatus));
        //     Assert.notNull(requestOrderStatusToTrfStatus, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.orderStatus incorrect");
        //     // 计算数据库order的最大状态
        //     int maxDbIdx = -1;
        //     for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
        //         Integer dbOrderStatusToTrfStatus = statusMapping.get(Func.toStr(trfOrderDOV2.getOrderStatus()));
        //         int dbStatusIdx = calcTrfStatusIndex(statusFlow, dbOrderStatusToTrfStatus);
        //         if (maxDbIdx < dbStatusIdx) {
        //             maxDbIdx = dbStatusIdx;
        //         }
        //     }
        //     // 计算请求的order状态是否大于当前数据库的状态
        //     int requestStatusIdx = calcTrfStatusIndex(statusFlow, requestOrderStatusToTrfStatus);
        //     if (requestStatusIdx > maxDbIdx) {
        //         trfStatusTo = requestOrderStatusToTrfStatus;
        //         log.info("TrfNo={} 满足ONE_ORDER状态前进条件 ", trfInfoPO.getTrfNo());
        //     } else {
        //         // 如果当前是reviseReport Action，请求order status可以小于当前数据库最大的status
        //         if (TrfActionEnum.SYNC_REVISE_REPORT.getCode().equals(statusCtrlDo.getAction())) {
        //             trfStatusTo = requestOrderStatusToTrfStatus;
        //             log.info("TrfNo={} 满足ONE_ORDER状态前进条件,reviseReport ", trfInfoPO.getTrfNo());
        //         }
        //     }
        // }
        // //所有关联订单状态前进才变更trf status
        // else if (TrfStatusRuleEnum.ALL_ORDER.getRule().equals(statusRule)) {
        //     trfOrderList = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());

        //     //所有订单状态
        //     Integer requestOrderStatusToTrfStatus = statusMapping.get(Func.toStr(order.getOrderStatus()));
        //     Assert.notNull(requestOrderStatusToTrfStatus, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.orderStatus incorrect");
        //     Set<Integer> trfStatusSet = Sets.newHashSet();
        //     trfStatusSet.add(requestOrderStatusToTrfStatus);
        //     for (TrfOrderDOV2 trfOrderDOV2 : trfOrderList) {
        //         Integer dbOrderStatusToTrfStatus = statusMapping.get(Func.toStr(trfOrderDOV2.getOrderStatus()));
        //         if (dbOrderStatusToTrfStatus > requestOrderStatusToTrfStatus) {
        //             continue;
        //         }
        //         trfStatusSet.add(dbOrderStatusToTrfStatus);
        //     }
        //     if (trfStatusSet.size() == 1) {
        //         trfStatusTo = requestOrderStatusToTrfStatus;
        //         log.info("TrfNo={} 满足ALL_ORDER状态前进条件 ", trfInfoPO.getTrfNo());
        //     }
        // }
        // //只允许变更状态一次
        // else if (TrfStatusRuleEnum.ONCE.getRule().equals(statusRule)) {
        //     //request status的下标 大于 db status的下标时，允许操作，否则抛出异常
        //     Integer requestOrderStatusToTrfStatus = statusMapping.get(Func.toStr(order.getOrderStatus()));
        //     Assert.notNull(requestOrderStatusToTrfStatus, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.orderStatus incorrect");
        //     Integer requestStatusIdx = calcTrfStatusIndex(statusFlow, requestOrderStatusToTrfStatus);
        //     Integer dbStatusIdx = calcTrfStatusIndex(statusFlow, trfStatusTo);
        //     Assert.isTrue(requestStatusIdx > dbStatusIdx, ResponseCode.INTERNAL_SERVER_ERROR, StrUtil.format("TRF {} not allow to sync {} status more than once", trfInfoPO.getTrfNo(), trfStatusTo));
        //     trfStatusTo = requestOrderStatusToTrfStatus;
        // } else if (TrfStatusRuleEnum.UPDATE.getRule().equals(statusRule)) {
        //     //do nothing
        // } else {
        //     throw new UnsupportedOperationException();
        // }

        TrfStatusResult trfStatusResult = new TrfStatusResult();
        trfStatusResult.setBindTrf(bindTrf);
        trfStatusResult.setOldTrfStatus(trfInfoPO.getStatus());

        // 使用策略模式计算新状态
        TrfStatusStrategy strategy = strategyFactory.getStrategy(statusRule);
        // 构建状态计算请求对象
        TrfStatusCalculationRequest request = getRequest(statusCtrlDo, trfInfoPO, statusControlConfig, actionStatusRule,
                statusMapping, statusFlow, order, trfOrderList);


        trfStatusTo = strategy.calculateTrfStatus(request);

        // 根据计算出的order状态 + 当前trf状态推出最新TRF状态
        //trfStatusTo = calculateTrfStatus(trfStatusTo, trfInfoPO.getStatus(), statusCtrlDo.getAction());

        //哪些状态可以变更为目标状态
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();

        trfInfoPO = getTrfInfoByTrfNo(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());
        //更新trfStatus
        if (!trfInfoPO.getStatus().equals(trfStatusTo)) {
            Integer whiteItem = ctrlMapping.get(trfStatusTo);
            Assert.notNull(whiteItem, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("TRF {} status is {} and canot sync {} status", statusCtrlDo.getTrfNo(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getTextEn(trfInfoPO.getStatus()), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getTextEn(trfStatusTo)));

            // save trf log
            trfLogDomainService.save(statusCtrlDo, ChangeTypeEnum.NORMAL.getCode(), trfInfoPO.getStatus(), trfStatusTo, trfInfoPO.getPendingFlag());
            trfInfoPO.setStatus(trfStatusTo);
            updateTrf(trfInfoPO);
        }
        trfStatusResult.setNewTrfStatus(trfStatusTo);
        log.info("TrfNo={} status control end,fromStatus:{} ,toStatus={}", trfInfoPO.getTrfNo(), trfStatusResult.getOldTrfStatus(), trfStatusTo);
        return trfStatusResult;
    }

    public static void checkOrderSystemId(List<TrfOrderDOV2> trfOrderList, TrfOrderDOV2 order, ErrorCode configErrorCode) {
        if (!Func.isEmpty(trfOrderList)) {
            boolean allMatch = trfOrderList.stream()
                    .allMatch(existingOrder -> Objects.equals(existingOrder.getSystemId(), order.getSystemId()));
            ErrorAssert.isTrue(
                    allMatch,
                    configErrorCode,
                    ResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    "The systemId of order conflicts with existing orders' systemId");
        }
    }

    private TrfStatusCalculationRequest getRequest(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO,
                                                   TrfStatusControlConfig statusControlConfig, TrfActionStatusRule actionStatusRule,
                                                   Map<String, Integer> statusMapping, Integer[] statusFlow, TrfOrderDOV2 order,
                                                   List<TrfOrderDOV2> trfOrderList) {
        TrfStatusCalculationRequest request = new TrfStatusCalculationRequest();
        request.setOrder(order);
        request.setStatusMapping(statusMapping);
        request.setStatusFlow(statusFlow);
        request.setCurrentTrfStatus(trfInfoPO.getStatus());
        request.setTrfInfoPO(trfInfoPO);
        request.setTrfNo(trfInfoPO.getTrfNo());
        request.setTrfOrderList(trfOrderList);
        request.setSystemId(String.valueOf(statusCtrlDo.getSystemId()));
        request.setAction(statusCtrlDo.getAction());
        request.setStateMachineControl(statusControlConfig.getStateMachineControl());
        request.setCtrlMapping(actionStatusRule.getCtrlMapping());
        return request;
    }

    public static void checkOrderInfo(TrfOrderDOV2 order, ErrorCode errorCode) {
        ErrorAssert.notNull(order, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order field is required");
        ErrorAssert.notNull(order.getSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.systemId field is required");
        ErrorAssert.notEmpty(order.getOrderNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order.orderNo field is required");
    }

    public Integer getStatusRule(TrfStatusControlDO statusCtrlDo, Integer nodeStatusRule, TrfInfoPO trfInfoPO, ErrorCode configErrorCode, Integer statusRule) {
        if (nodeStatusRule == null && trfInfoPO.getIntegrationLevel() != null &&
                Objects.equals(Integer.valueOf(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule())) {
            // 读取系统默认配置
            String defaultConfigValue = configClient.getConfig(ConfigClient.DEFAULST_CONFIG_VALUE_REFSYSTEM_ID, Constants.CONFIG_STATUS_CONTROL);
            TrfStatusControlConfig defaultStatusControlConfig = JSON.parseObject(defaultConfigValue, TrfStatusControlConfig.class);
            nodeStatusRule = defaultStatusControlConfig.getActionStatusMapping().get(statusCtrlDo.getAction()).getStatusRule();

            ErrorAssert.notNull(nodeStatusRule, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("System [identity={}] node [action={}] status rule was not found!", trfInfoPO.getRefSystemId(), statusCtrlDo.getAction()));
            //按细粒度的规则进行状态控制
            statusRule = nodeStatusRule;
        }
        return statusRule;
    }

    public void updateTrfIntegrationLevel(TrfInfoPO trfInfoPO, TrfStatusControlConfig statusControlConfig) {
        if (Func.isEmpty(trfInfoPO.getIntegrationLevel())) {
            trfInfoPO.setIntegrationLevel(String.valueOf(statusControlConfig.getTrfOrderRelationshipRule()));
            updateTrf(trfInfoPO);
        }
    }

    public TrfStatusControlConfig getTrfStatusControlConfig(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO, ErrorCode configErrorCode) {
        String configValue = configClient.getConfig(trfInfoPO.getBuCode(), trfInfoPO.getRefSystemId(), Constants.CONFIG_STATUS_CONTROL);
        ErrorAssert.notNull(configValue, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have config!", trfInfoPO.getRefSystemId()));
        log.info("status control config {},trfNo={}", configValue, statusCtrlDo.getTrfNo());

        TrfStatusControlConfig statusControlConfig = JSON.parseObject(configValue, TrfStatusControlConfig.class);
        ErrorAssert.notNull(statusControlConfig, configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have status control config!", trfInfoPO.getRefSystemId()));
        return statusControlConfig;
    }

    public TrfInfoPO checkStatusCtrlDoParam(TrfStatusControlDO statusCtrlDo, ErrorCode errorCode) {
        ErrorAssert.notNull(statusCtrlDo, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The param is required");
        ErrorAssert.notNull(statusCtrlDo.getAction(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The action field is required");
        ErrorAssert.notNull(statusCtrlDo.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId field is required");
        ErrorAssert.notEmpty(statusCtrlDo.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo field is required");

        //1 基础参数验证
        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The trf {} not found", statusCtrlDo.getTrfNo()));
        //cancel的订单
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The trf {} already cancelled", statusCtrlDo.getTrfNo()));
        return trfInfoPO;
    }

    private boolean buildTrfRel(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO, TrfActionStatusRule actionStatusRule) {
        boolean bindTrf = false;
        TrfOrderDOV2 order = statusCtrlDo.getOrder();
        ErrorCode configErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.STATUSCONTROL, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.notNull(actionStatusRule.getOrderOpType(), configErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), StrUtil.format("this {} not have config", trfInfoPO.getBuCode()));
        //根据action配置对order的操作类型，对order进行处理
        //根据order 操作配置，调用order  domain service
        if (TrfOrderOpTypeEnum.bind.getType() == actionStatusRule.getOrderOpType()) {
            List<TrfOrderDOV2> trfOrderPOList = trfOrderDomainService.selectByExample(trfInfoPO.getId(), order.getSystemId(), order.getOrderNo(), order.getOrderId());
            if (Func.isEmpty(trfOrderPOList) && StringUtils.isNotBlank(order.getRealOrderNo())) {
                trfOrderPOList = trfOrderDomainService.selectByExample(trfInfoPO.getId(), order.getSystemId(), order.getRealOrderNo(), order.getOrderId());
            }
            if (Func.isEmpty(trfOrderPOList)) {
                trfOrderDomainService.bind(statusCtrlDo.getSyncHeader(), trfInfoPO.getId(), statusCtrlDo.getOrder());
                bindTrf = true;
            } else {
                trfOrderDomainService.modifyStatus(statusCtrlDo, trfInfoPO.getId(), statusCtrlDo.getOrder());
            }
        } else if (TrfOrderOpTypeEnum.modifyStatus.getType() == actionStatusRule.getOrderOpType()) {
            //fixme 这个状态在外部通过action定义好了，应该根据statusMapping来确认？
            trfOrderDomainService.modifyStatus(statusCtrlDo, trfInfoPO.getId(), statusCtrlDo.getOrder());
        }

        //维护trf-report-rel
        buildTrfReportRel(statusCtrlDo, trfInfoPO);
        return bindTrf;
    }

    private void buildTrfReportRel(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO) {
        List<TrfReportDOV2> trfReportDOList = statusCtrlDo.getReportList();
        if (com.sgs.framework.tool.utils.CollectionUtil.isEmpty(statusCtrlDo.getReportList())) {
            return;
        }

        //如果有ReportList节点，则保存Trf-report-rel
        //1、遍历reportList ,如果report 节点下没有指定TrfList，则默认为当前Trf
        trfReportDOList.forEach(trfReportDOV2 -> {
            if (com.sgs.framework.tool.utils.CollectionUtil.isEmpty(trfReportDOV2.getTrfList())) {
                createTrfReport(trfInfoPO, trfReportDOV2);
                return;
            }

            /**
             * 说明：Kevin @ 20231109
             * 这个地方应该要判断TrfNo 是否在当前请求中，否则需要报错
             */
            List<TrfReferenceDO> trfReferenceDOList = trfReportDOV2.getTrfList();
            trfReferenceDOList.forEach(trfReferenceDO -> {
                TrfInfoPO reportTrf = getTrfInfoByTrfNo(trfReferenceDO.getTrfNo(), trfInfoPO.getRefSystemId());
                if (reportTrf == null) {
                    log.error("TrfNo={} 不存在，导致Trf-Report-Rel 保存失败 ", trfReferenceDO.getTrfNo());
                    return;
                }

                createTrfReport(reportTrf, trfReportDOV2);
            });

        });
//        trfReportDomainService.batchSave(trfInfoPO.getId(), statusCtrlDo.getReportList());
        //2、如果reportList.report 节点下指定了TrfList，则Report 与指定Trf构建关系
    }

    public void createTrfReport(TrfInfoPO trfInfoPO, TrfReportDOV2 trfReportDO) {
        trfReportDO.setLabId(trfInfoPO.getLabId());
        CustomerConfirmReportEnum confirmReport = Optional.ofNullable(trfInfoPO.getExtFields())
                .map(JSON::parseObject)
                .map(extFields -> extFields.getJSONObject("ctx"))
                .map(ctx -> JSON.parseObject(JSON.toJSONString(ctx), CustomerGeneralConfig.class))
                .filter(CustomerGeneralConfig::requiredCustomerConfirmReport)
                .map(required -> CustomerConfirmReportEnum.Pending)
                .orElse(CustomerConfirmReportEnum.NoRequired);
        trfReportDO.setCustomerConfirmStatus(confirmReport);

        if (com.sgs.framework.tool.utils.CollectionUtil.isEmpty(trfReportDO.getTrfList())) {
            createTrfReportImpl(trfInfoPO.getId(), trfReportDO);
            return;
        }

        /**
         * 说明：Kevin @ 20231109
         * 这个地方应该要判断TrfNo 是否在当前请求中，否则需要报错
         */
        List<TrfReferenceDO> trfReferenceDOList = trfReportDO.getTrfList();
        trfReferenceDOList.forEach(trfReferenceDO -> {
            TrfInfoPO reportTrf = getTrfInfoByTrfNo(trfReferenceDO.getTrfNo(), trfInfoPO.getRefSystemId());
            if (reportTrf == null) {
                log.error("TrfNo={} 不存在，导致Trf-Report-Rel 保存失败 ", trfReferenceDO.getTrfNo());
                return;
            }

            if (Func.isEmpty(trfReportDO.getSystemId())) {
                trfReportDO.setSystemId(reportTrf.getSystemId());
            }
            createTrfReportImpl(reportTrf.getId(), trfReportDO);
        });
    }

    public List<TrfReportDOV2> queryTrfReport(Integer systemId, String orderNo) {
        List<TrfReportPO> trfReportPOList = trfReportDomainService.selectBySystemIdAndOrderNoAndReportNo(systemId, orderNo, null);

        if (com.sgs.framework.tool.utils.CollectionUtil.isEmpty(trfReportPOList)) {
            return new ArrayList<>();
        }

        List<TrfReportDOV2> result = new ArrayList<>();
        trfReportPOList.stream().forEach(trfReportPO -> {
            TrfReportDOV2 trfReportDOV2 = new TrfReportDOV2();
            BeanUtil.copy(trfReportPO, trfReportDOV2);

            result.add(trfReportDOV2);
        });

        return result;
    }

    public Date getReportDueDate(Integer refSystemId, String trfNo) {

        TrfInfoPO infoByTrfNo = getTrfInfoByTrfNo(trfNo, refSystemId);
        if (Func.isEmpty(infoByTrfNo)) {
            return null;
        }
        List<TrfReportPO> reportList = trfReportDomainService.getActiveReportListByTrfId(infoByTrfNo.getId());
        if (Func.isEmpty(reportList)) {
            return null;
        }
        Collections.sort(reportList, Comparator.comparing(TrfReportPO::getReportDueDate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        return reportList.get(0).getReportDueDate();
    }

    public CustomerConfirmReportFlag customerConfirmReport(Integer refSystemId, String trfNo, String reportNo) {
        Assert.notNull(refSystemId, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        Assert.notNull(reportNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The reportNo is required");
        List<TrfInfoPO> trfList = selectActiveAndNotCancelByTrfNo(trfNo);
        TrfInfoPO trfInfoPO = getByRefSystemIdOrThrow(refSystemId, trfNo, trfList);
        TrfReportPO trfReport = trfReportDomainService.selectActiveOneByTrfIdAndReportNo(trfInfoPO.getId(), reportNo)
                .orElseThrow(() -> new BizException("can't found report by refSystemId " + refSystemId + " trfNo " + trfNo + " reportNo " + reportNo));
        trfReport.setCustomerConfirmStatus(CustomerConfirmReportEnum.Confirmed);
        trfReportDomainService.updateTrfReport(trfReport);
        return CustomerConfirmReportFlag.confirmed(reportNo);
    }

    public CustomerConfirmReportTrf<CustomerConfirmReportFlag> queryReportConfirmFlag(Integer refSystemId, String trfNo, List<String> reportNoList) {
        Assert.notNull(refSystemId, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        Assert.notNull(trfNo, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        if(CollectionUtils.isEmpty(reportNoList)) {
            return CustomerConfirmReportTrf.empty(refSystemId, trfNo);
        }
        List<TrfInfoPO> trfList = selectActiveAndNotCancelByTrfNo(trfNo);
        if(CollectionUtils.isEmpty(trfList)) {
            return CustomerConfirmReportTrf.empty(refSystemId, trfNo);
        }
        TrfInfoPO existingTrf = trfList.get(0);
        Assert.isTrue(Objects.equals(existingTrf.getRefSystemId(), refSystemId), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "can't found TRF by refSystemId:" +refSystemId+" and trfNo "+trfNo+" ");
        List<TrfReportPO> trfReportPOS = trfReportDomainService.selectByTrfIdAndReportNo(existingTrf.getId(), reportNoList);
        return CustomerConfirmReportTrf.of(refSystemId, trfNo, trfReportPOS.stream()
                .map(trfReport -> CustomerConfirmReportFlag.of(trfReport.getReportNo(), trfReport.getCustomerConfirmStatus()))
                .collect(Collectors.toList()));
    }

    public void completedDeliveryReport(List<String> reportNoList, Long apiId) {
        if (CollectionUtils.isEmpty(reportNoList)) {
            return;
        }
        reportNoList.forEach(reportNo -> {
                    trfReportDomainService.updateTrfReportDeliveryFlagAfterCallback(
                            apiId,
                            reportNo,
                            DeliveryFlagEnum.SUCCESS.getCode()
                    );
                }
        );
    }

    public void failedDeliveryReport(List<String> reportNoList, Long apiId) {
        if (CollectionUtils.isEmpty(reportNoList)) {
            return;
        }
        reportNoList.forEach(reportNo -> {
                    trfReportDomainService.updateTrfReportDeliveryFlagAfterCallback(
                            apiId,
                            reportNo,
                            DeliveryFlagEnum.FAIL.getCode()
                    );
                }
        );
    }

    public void conditionDeliveryReport(List<String> reportNoList, Long apiId) {
        if (CollectionUtils.isEmpty(reportNoList)) {
            return;
        }
        reportNoList.forEach(reportNo -> {
                    trfReportDomainService.updateTrfReportDeliveryFlagAfterCallback(
                            apiId,
                            reportNo,
                            DeliveryFlagEnum.CONDITION.getCode()
                    );
                }
        );
    }

    public void repeatedDeliveryReport(List<String> reportNoList, Long apiId) {
        if (CollectionUtils.isEmpty(reportNoList)) {
            return;
        }
        reportNoList.forEach(reportNo -> {
                    trfReportDomainService.updateTrfReportResendFlag(
                            apiId,
                            reportNo
                    );
                }
        );
    }

    public void markDeliveryReport(Long subscriberId, Integer subscriber, Long apiId, List<String> reportNoList) {
        if (CollectionUtils.isEmpty(reportNoList)) {
            return;
        }
        reportNoList.stream()
                .map(trfReportPO -> TrfReportDeliveryInfoObject.toDelivery(subscriberId, subscriber, apiId, trfReportPO))
                .peek(deliveryInfo -> log.info("mark delivery info {}", deliveryInfo))
                .forEach(deliveryInfo -> {
                    try {
                        trfReportDomainService.saveTrfReportDeliveryInfo(deliveryInfo);
                    } catch (DuplicateKeyException e) {
                        log.warn("delivery info exists : {}", deliveryInfo);
                        //ignore
                    }
                });
    }

    public void completedDeliveryQuotation(Long trfId, List<String> quotationNoList) {
        if (Func.isEmpty(quotationNoList)) {
            return;
        }

        TrfInfoPO trf = this.selectByTrfId(trfId);

        quotationNoList.stream().forEach(quotationNo -> {
            String[] quotationNoArr = quotationNo.split("-");

            trfQuotationDomainService.delivery(trf.getTrfNo(), trf.getRefSystemId(), quotationNoArr[0], Integer.parseInt(quotationNoArr[1]), DeliveryFlagEnum.SUCCESS.getCode());
        });

    }

    public void failedDeliveryQuotation(Long trfId, List<String> quotationNoList) {
        if (Func.isEmpty(quotationNoList)) {
            return;
        }

        TrfInfoPO trf = this.selectByTrfId(trfId);

        quotationNoList.stream().forEach(quotationNo -> {
            String[] quotationNoArr = quotationNo.split("-");

            trfQuotationDomainService.delivery(trf.getTrfNo(), trf.getRefSystemId(), quotationNoArr[0], Integer.parseInt(quotationNoArr[1]), DeliveryFlagEnum.NEW.getCode());
        });
    }

    public void markDeliveryQuotation(Long trfId, List<String> quotationNoList) {
        if (Func.isEmpty(quotationNoList)) {
            return;
        }

        TrfInfoPO trf = this.selectByTrfId(trfId);

        quotationNoList.stream().forEach(quotationNo -> {
            String[] quotationNoArr = quotationNo.split("-");

            trfQuotationDomainService.delivery(trf.getTrfNo(), trf.getRefSystemId(), quotationNoArr[0], Integer.parseInt(quotationNoArr[1]), DeliveryFlagEnum.ING.getCode());
        });
    }

    /**
     * 不对外提供服务：因为没有考虑TrfReportDO里面的TrfList
     * <p>
     * 应该使用 saveTrfReport
     *
     * @param trfId
     * @param trfReportDO
     */
    private void createTrfReportImpl(Long trfId, TrfReportDOV2 trfReportDO) {
        trfReportDO.setTrfId(trfId);
        trfReportDomainService.save(trfReportDO);
    }

    public void createTrfReport(TrfInfoPO trfInfoPO, List<TrfReportDOV2> trfReportDOList) {
        Assert.notNull(trfInfoPO, "Trf not Found!");

        if (com.sgs.framework.tool.utils.CollectionUtil.isEmpty(trfReportDOList)) {
            log.info(" Trf : {}, trfReportDOList is empty.", trfInfoPO.getTrfNo());
            return;
        }

        trfReportDOList.forEach(trfReportDOV2 -> {
            createTrfReport(trfInfoPO, trfReportDOV2);
        });

    }

    private Integer calculateTrfStatus(Integer trfStatusTo, Integer trfCurrentStatus, String action) {
        // 如果TRF当前状态在completed之前,根据配置来变化，这里不限制
        if (trfCurrentStatus < com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus()) {
            return trfStatusTo;
        } else if (trfCurrentStatus == com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus()
                || trfCurrentStatus == com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Closed.getStatus()) {
            // 如果TRF当前状态在completed或closed,trf状态要往前走，返回revise
            if (trfStatusTo < com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus()) {
                if (Func.equals(TrfActionEnum.SYNC_REVISE_REPORT.getCode(), action)) {
                    return com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus();
                } else {
                    return trfCurrentStatus;
                }
            }
        }

        // todo 限制TRF revise只能去completed
        boolean trfRevising = Objects.equals(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus(), trfCurrentStatus);
        boolean toStatusNotCompleted = !Objects.equals(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus(), trfStatusTo);
        if (trfRevising && toStatusNotCompleted) {
            trfStatusTo = trfCurrentStatus;
        }

        // todo 限制TRF 只有completed和closed状态能到revise
        boolean toStatusIsRevise = Objects.equals(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus(), trfStatusTo);
        boolean trfNotCompletedOrClosed = !Objects.equals(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus(), trfCurrentStatus)
                && !Objects.equals(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Closed.getStatus(), trfCurrentStatus);
        if (toStatusIsRevise && trfNotCompletedOrClosed) {
            trfStatusTo = trfCurrentStatus;
        }

        // 其他情况不做干预直接返回
        return trfStatusTo;
    }


    private int calcTrfStatusIndex(Integer[] statusFlow, Integer trfStatus) {
        if (ArrayUtil.isEmpty(statusFlow)) {
            return -1;
        }
        int idx = -1;
        for (int i = 0; i < statusFlow.length; i++) {
            int status = statusFlow[i];
            if (trfStatus.compareTo(status) == 0) {
                idx = i;
                break;
            }
        }
        return idx;
    }


    public TrfHeaderDOV2 returnTrf(TrfStatusControlDO trfDO) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.RETURN, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfDO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfDO is required");
        ErrorAssert.notNull(trfDO.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        ErrorAssert.notEmpty(trfDO.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");

        TrfInfoPO trfInfoPO = selectByTrfNo(trfDO.getRefSystemId(), trfDO.getTrfNo());
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");
        //cancel的订单
        //检查可Remove的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfigOrDefaultConfig(trfDO, trfInfoPO);
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(trfDO.getAction());
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());

        ErrorAssert.notNull(whiteItem, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The Remove action not allowed");

        Integer from = trfInfoPO.getStatus();
        // save trf log
        trfLogDomainService.save(trfDO, ChangeTypeEnum.REMOVE.getCode(), from, from, trfInfoPO.getPendingFlag());

//        List<TrfLogPO> trfLogPOS = trfLogDomainService.selectTrfLog(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());

        trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());

        trfInfoPO.setRemoveType(trfDO.getReasonType());
        trfInfoPO.setRemoveRemark(trfDO.getReasonRemark());
        trfInfoPO.setModifiedBy(UserHelper.getLocalUser() == null ? USER_DEFAULT : UserHelper.getLocalUser().getName());
        trfInfoPO.setStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.New.getStatus());
        trfInfoPO.setSgsTrfNo(null);
        updateTrf(trfInfoPO);

        // 移除todo list中的数据
        this.deleteTodoInfoData(trfInfoPO.getRefSystemId(), trfInfoPO.getTrfNo());

        trfOrderDomainService.updateOrderActiveFlag(trfInfoPO.getId());

        return convertTrfHeader(trfInfoPO, null, null);
    }

    private TrfStatusControlConfig getTrfStatusControlConfigOrDefaultConfig(TrfStatusControlDO statusControlDO, TrfInfoPO trfInfoPO) {
//        优先取客户配置
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode(), statusControlDO.getAction(), false);
        if (controlConfig == null) {
//            客户配置不存在时取系统兜底配置
            controlConfig = getTrfStatusControlConfig(0, "", statusControlDO.getAction(), true);
        }
        return controlConfig;
    }

    private void updateTrf(TrfInfoPO trfInfoPO) {
        trfInfoPO.setModifiedDate(DateUtils.getNow());
        trfInfoPO.setModifiedBy(USER_DEFAULT);
        if (Func.isNotEmpty(trfInfoPO)) {
            trfInfoPO.setTrfExpectDueDate(trfOrderDomainService.getDueDateByTrfId(trfInfoPO.getId()));
        }
        trfInfoMapper.updateByPrimaryKey(trfInfoPO);
    }

    public void deleteTodoInfoData(Integer refSystemId, String trfNo) {
        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystemId, Collections.singletonList(trfNo));
    }


    public TrfHeaderDOV2 cancelTrf(TrfStatusControlDO statusCtrlDo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(statusCtrlDo, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The param is required");
        ErrorAssert.notNull(statusCtrlDo.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        ErrorAssert.notEmpty(statusCtrlDo.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");

        TrfInfoPO trfInfoPO = selectByTrfNo(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());
        ErrorAssert.notNull(trfInfoPO, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf not found");
        ErrorAssert.isTrue(!TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.Canceled), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trf already cancelled");
        //cancel的订单

        //检查action的trf status白名单
        TrfStatusControlConfig controlConfig = getTrfStatusControlConfigOrDefaultConfig(statusCtrlDo, trfInfoPO);
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(statusCtrlDo.getAction());
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());
        Assert.notNull(whiteItem, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The CancelTrf action not allowed");

        trfOrderDomainService.batchCancel(trfInfoPO.getId(), statusCtrlDo.getReasonType(), statusCtrlDo.getReasonRemark());

        Integer from = trfInfoPO.getStatus();
        // save trf log
        trfLogDomainService.save(statusCtrlDo, ChangeTypeEnum.CANCEL.getCode(), from, TrfStatusEnum.Canceled.getStatus(), trfInfoPO.getPendingFlag());

        trfInfoPO.setCancelType(statusCtrlDo.getReasonType());
        trfInfoPO.setCancelRemark(statusCtrlDo.getReasonRemark());
        trfInfoPO.setStatus(TrfStatusEnum.Canceled.getStatus());

        updateTrf(trfInfoPO);

        // 移除todo list中的数据
        this.deleteTodoInfoData(statusCtrlDo.getRefSystemId(), statusCtrlDo.getTrfNo());

//        List<TrfLogPO> trfLogPOS = trfLogDomainService.selectTrfLog(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId());
        return convertTrfHeader(trfInfoPO, null, null);
    }

    public List<BoundTrfRelDTO> getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject) {
        return getBoundTrfInfoList(reqObject, getAllTrfStatus());
    }

    /**
     * @param reqObject
     * @return
     */
    public List<BoundTrfRelDTO> getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject, List<Integer> allTrfStatus) {
        //调用commonService接口 获取reason相关数据
        List<DataDictionary> list = frameWorkClient.getDataDictionaryList(Constants.TODO_List_Reason_List, reqObject.getProductLineCode());
        Map<String, String> rlMap = Maps.newHashMap();
        if (Func.isNotEmpty(list)) {
            list.forEach(l -> rlMap.put(l.getSysKey(), l.getSysValue()));
        }
        SearchType searchType = reqObject.getSearchType();
        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        BeanUtils.copyProperties(reqObject, searchTrfInfoDTO);
        List<BoundTrfRelDTO> boundTrfRelDTOS = Lists.newArrayList();
        if (Func.isNotEmpty(reqObject.getPage()) && Func.isNotEmpty(reqObject.getRows())) {
            PageHelper.startPage(reqObject.getPage(), reqObject.getRows(), true, true, null);
        }
        if (searchType.check(SearchType.BoundListType)) {
            searchTrfInfoDTO.setSearchType(1);
            if (Func.isEmpty(searchTrfInfoDTO.getStatus())) {
                searchTrfInfoDTO.setActionStatus(allTrfStatus);
            }
            boundTrfRelDTOS = orderRelationshipExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);
        } else {
            searchTrfInfoDTO.setSearchType(0);
            Map<String, List<String>> requestMaps = searchTrfInfoDTO.getRequestMaps();
            if (Func.isNotEmpty(requestMaps)) {
                requestMaps.remove("createMoreOrder");
            }
            // 如果当前勾选了查询已绑定订单的按钮
            if (reqObject.getCreateMoreOrder()) {
                List<Integer> boundTrfStatus = getBoundTrfStatus();
                searchTrfInfoDTO.setActionStatus(boundTrfStatus);
                boundTrfRelDTOS = orderRelationshipExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);
            } else {
                List<Integer> unBoundTrfStatus = getUnBoundTrfStatus();
                searchTrfInfoDTO.setActionStatus(unBoundTrfStatus);
                boundTrfRelDTOS = orderRelationshipExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);
            }
        }
        if (Func.isNotEmpty(boundTrfRelDTOS)) {
            boundTrfRelDTOS.forEach(
                    l -> l.setCancelReason(rlMap.get(l.getCancelReason()))
            );
        }
        return boundTrfRelDTOS;
    }

    public List<Integer> getBoundTrfStatus() {
        Integer[] actionStatus = {
                TrfStatusEnum.Invoiced.getStatus(),
                TrfStatusEnum.ToBeTested.getStatus(),
                TrfStatusEnum.Testing.getStatus(),
                com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus()
        };
        return Arrays.asList(actionStatus);
    }

    public List<Integer> getAllTrfStatus() {
        Integer[] actionStatus = {
                TrfStatusEnum.ToPrice.getStatus(),
                TrfStatusEnum.Invoiced.getStatus(),
                TrfStatusEnum.ToBeTested.getStatus(),
                TrfStatusEnum.Testing.getStatus(),
                TrfStatusEnum.Detected.getStatus(),
                TrfStatusEnum.Completed.getStatus(),
                TrfStatusEnum.Canceled.getStatus(),
                com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus()
        };
        return Arrays.asList(actionStatus);
    }

    public List<Integer> getUnBoundTrfStatus() {
        Integer[] actionStatus = {
                TrfStatusEnum.ToBeBound.getStatus()
        };
        return Arrays.asList(actionStatus);
    }


    /**
     * @param pageSize 每页显示的数量
     * @param pageNum  当前页码
     *                 <br> 1. 起始位置边界值处理： 同 subList 方法
     *                 <br> 2. 终止位置：无需处理，会自动处理边界问题
     */
    private List<?> partition(List<?> list, int pageSize, int pageNum) {
        int count = list.size(); // 总记录数
        // 计算总页数
        int pages = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
        // 起始位置
        int start = pageNum <= 0 ? 0 : (pageNum > pages ? (pages - 1) * pageSize : (pageNum - 1) * pageSize);
        // 终止位置
        int end = pageSize;
        return list.stream().skip(start).limit(pageSize).collect(Collectors.toList());
    }

    /**
     * 计算客户对接模式
     *
     * @param trfDO
     * @return
     */
    public Integer calcIntegrationLevel(TrfDOV2 trfDO) {
        Assert.notNull(trfDO);
        Assert.notNull(trfDO.getHeader());
        TrfLabDOV2 trfLabDOV2 = trfDO.getHeader().getLab();
        return getTrfOrderRelationshipRule(trfDO.getHeader().getRefSystemId(), trfLabDOV2.getBuCode());
    }

    public Integer calcIntegrationLevel(Integer refSysytemId, String buCode) {
        return getTrfOrderRelationshipRule(refSysytemId, buCode);
    }

    private Integer getTrfOrderRelationshipRule(Integer refSystemId, String buCode) {
        if (!SCITransitionUtils.isSwitched(refSystemId, buCode)) {
            return null;
        }

        String configValue = configClient.getConfig(buCode, refSystemId, Constants.CONFIG_STATUS_CONTROL);
        if (StringUtils.isBlank(configValue)) {
            log.warn("TrfStatusControl configuration was not found. RefSystemId={}, BuCode={}",
                    refSystemId, buCode);
            throw new BizException("TrfStatusControl configuration was not found!");
        }

        TrfStatusControlConfig scc = JSON.parseObject(configValue, TrfStatusControlConfig.class);
        Assert.notNull(scc.getTrfOrderRelationshipRule(), "Default value was not set for customer integration mode");

        return scc.getTrfOrderRelationshipRule();
    }

    public Integer calcSampleLevel(TrfDOV2 trfDO) {
        Assert.notNull(trfDO);
        Assert.notNull(trfDO.getHeader());
        Assert.notNull(trfDO.getHeader().getLab());

        return getTrfOrderRelationshipRule(trfDO.getHeader().getRefSystemId(), trfDO.getHeader().getLab().getBuCode());
    }

    public TrfDTO getTrfBaseInfo(String trfNo, Integer refSystemId) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfNo, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfNo should not be null");
        ErrorAssert.notNull(refSystemId, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId should not be null");

        TrfInfoExample example = new TrfInfoExample();
        example.createCriteria().andTrfNoEqualTo(trfNo).
                andRefSystemIdEqualTo(refSystemId).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(trfInfoPOList)) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "trf 不存在");
        }
        TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOList, 0);
        List<TrfAttachmentPO> trfAttachmentPOs = trfAttachmentDomainService.getTrfAttachment(trfInfoPO.getId());
        List<TrfProductPO> trfProductProductList = trfProductDomainService.getTrfProductProductList(trfInfoPO.getId());
        List<TrfProductPO> trfProductSampleList = trfProductDomainService.getTrfProductSampleList(trfInfoPO.getId());

        List<TrfTestItemPO> trfTestItemPOList = trfTestItemDomainService.getTrfTestItemList(trfInfoPO.getId());
        TrfServiceRequirementDOV2 trfServiceRequirementPOS = trfServiceRequirementDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfCustomerDOV2> trfCustomerDOV2s = trfCustomerDomainService.selectByTrfId(trfInfoPO.getId());
        TrfLabPO trfLabPO = trfLabDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfTestSamplePO> trfTestSamplePOList = trfTestSampleDomainService.getTrfTestSampleList(trfInfoPO.getId());
        List<TrfOrderDOV2> trfOrderDOV2s = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfProductAttrPO> trfProductAttrPOLsit = trfProductDomainService.getTrfProductAttrPOProductLsit(trfInfoPO.getId());
        List<TrfProductAttrPO> trfProductAttrPOSampleLsit = trfProductDomainService.getTrfProductAttrPOSampleList(trfInfoPO.getId());
        CustomerTrfInfoRsp customerTrf = trfInfoExtMapper.getTrfInfo(trfInfoPO.getRefSystemId(), trfInfoPO.getTrfNo());
        TrfDTO trfDTO = TrfInfoPOConvertor.convertTrfDTO(trfInfoPO, customerTrf);// header 信息
        List<TrfCareLabelPO> trfCareLabelPOList = trfCareLabelDomainService.selectTrfCareLabelList(trfInfoPO.getId());//
        TrfInfoPOConvertor.convertTrfLabDTO(trfDTO, trfLabPO);
        TrfInfoPOConvertor.convertTrfSampleDffDto(trfDTO, trfProductSampleList, trfProductAttrPOSampleLsit);
        TrfInfoPOConvertor.convertTrfCustomerDTO(trfDTO, trfCustomerDOV2s);
        TrfInfoPOConvertor.convertTrfAttachmentDTO(trfDTO, trfAttachmentPOs);
        TrfInfoPOConvertor.convertTrfProductDTO(trfDTO, trfProductProductList, trfProductAttrPOLsit);
        TrfInfoPOConvertor.convertTrfTestItemDTO(trfDTO, trfTestItemPOList);
        TrfInfoPOConvertor.convertTrfServiceRequirementDOV2(trfDTO, trfServiceRequirementPOS);
        TrfInfoPOConvertor.convertTrfTestSampleDTO(trfDTO, trfTestSamplePOList);
        TrfInfoPOConvertor.convertTrfOrderDTO(trfDTO, trfOrderDOV2s);
        TrfInfoPOConvertor.convertTrfCareLabelDTO(trfDTO, trfCareLabelPOList);
        return trfDTO;
    }

    public TrfDTO getTrfBaseInfo(String trfNo) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfNo, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfNo should not be null");

        TrfInfoExample example = new TrfInfoExample();
        example.createCriteria().andTrfNoEqualTo(trfNo).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(trfInfoPOList)) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "trf 不存在");
        }
        if (trfInfoPOList.size() != 1) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "to many trfs by TrfNo:" + trfNo);
        }
        TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOList, 0);
        List<TrfAttachmentPO> trfAttachmentPOs = trfAttachmentDomainService.getTrfAttachment(trfInfoPO.getId());
        List<TrfProductPO> trfProductProductList = trfProductDomainService.getTrfProductProductList(trfInfoPO.getId());
        List<TrfProductPO> trfProductSampleList = trfProductDomainService.getTrfProductSampleList(trfInfoPO.getId());

        List<TrfTestItemPO> trfTestItemPOList = trfTestItemDomainService.getTrfTestItemList(trfInfoPO.getId());
        TrfServiceRequirementDOV2 trfServiceRequirementPOS = trfServiceRequirementDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfCustomerDOV2> trfCustomerDOV2s = trfCustomerDomainService.selectByTrfId(trfInfoPO.getId());
        TrfLabPO trfLabPO = trfLabDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfTestSamplePO> trfTestSamplePOList = trfTestSampleDomainService.getTrfTestSampleList(trfInfoPO.getId());
        List<TrfOrderDOV2> trfOrderDOV2s = trfOrderDomainService.selectByTrfId(trfInfoPO.getId());
        List<TrfProductAttrPO> trfProductAttrPOLsit = trfProductDomainService.getTrfProductAttrPOProductLsit(trfInfoPO.getId());
        List<TrfProductAttrPO> trfProductAttrPOSampleLsit = trfProductDomainService.getTrfProductAttrPOSampleList(trfInfoPO.getId());
        CustomerTrfInfoRsp customerTrf = trfInfoExtMapper.getTrfInfo(trfInfoPO.getRefSystemId(), trfInfoPO.getTrfNo());
        TrfDTO trfDTO = TrfInfoPOConvertor.convertTrfDTO(trfInfoPO, customerTrf);// header 信息
        List<TrfCareLabelPO> trfCareLabelPOList = trfCareLabelDomainService.selectTrfCareLabelList(trfInfoPO.getId());//
        TrfInfoPOConvertor.convertTrfLabDTO(trfDTO, trfLabPO);
        TrfInfoPOConvertor.convertTrfSampleDffDto(trfDTO, trfProductSampleList, trfProductAttrPOSampleLsit);
        TrfInfoPOConvertor.convertTrfCustomerDTO(trfDTO, trfCustomerDOV2s);
        TrfInfoPOConvertor.convertTrfAttachmentDTO(trfDTO, trfAttachmentPOs);
        TrfInfoPOConvertor.convertTrfProductDTO(trfDTO, trfProductProductList, trfProductAttrPOLsit);
        TrfInfoPOConvertor.convertTrfTestItemDTO(trfDTO, trfTestItemPOList);
        TrfInfoPOConvertor.convertTrfServiceRequirementDOV2(trfDTO, trfServiceRequirementPOS);
        TrfInfoPOConvertor.convertTrfTestSampleDTO(trfDTO, trfTestSamplePOList);
        TrfInfoPOConvertor.convertTrfOrderDTO(trfDTO, trfOrderDOV2s);
        TrfInfoPOConvertor.convertTrfCareLabelDTO(trfDTO, trfCareLabelPOList);
        return trfDTO;
    }

    public void updateTrfStatusAndDeliveryFlag(String trfNo, Integer refSystemId, Optional<Integer> changeStatus, List<TrfReportDOV2> reportList) {

        if (Func.isNotEmpty(trfNo) && Func.isNotEmpty(refSystemId) && changeStatus.isPresent()) {
            TrfInfoExample example = new TrfInfoExample();
            example.createCriteria().andTrfNoEqualTo(trfNo).
                    andRefSystemIdEqualTo(refSystemId).
                    andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
            List<TrfInfoPO> trfInfoPOS = trfInfoMapper.selectByExample(example);
            if (Func.isNotEmpty(trfInfoPOS)) {
                TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOS, 0);
                Integer status = changeStatus.get();
                if (Func.isNotEmpty(status)) {
                    trfInfoPO.setStatus(status);
                    trfInfoMapper.updateByPrimaryKey(trfInfoPO);
                }
            }
        }
        if (Func.isNotEmpty(refSystemId) && Func.isNotEmpty(reportList)) {
            List<String> collect = reportList.stream().map(TrfReportDOV2::getReportNo).distinct().collect(Collectors.toList());
            if (Func.isEmpty(collect)) {
                return;
            }
            trfReportDomainService.updateTrfReportDeliveryFlag(collect, refSystemId);
            if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO)) {
                trfReportDomainService.updateTrfReportDeliveryFlag(collect, RefSystemIdEnum.Target.getRefSystemId());
            }
        }
    }

    /**
     * 判断trf 是否绑定
     *
     * @param trfId
     * @return
     */
    public boolean checkBind(Long trfId) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(com.sgs.framework.tool.utils.ObjectUtil.isNotEmpty(trfId), errorCode, ResponseCode.FAIL.getCode(), "trfId 不可以为空");
        TrfInfoPO trfInfoPO = trfInfoMapper.selectByPrimaryKey(trfId);
        if (ObjectUtil.isNull(trfInfoPO) || TrfConstants.DEL_INACTIVE == trfInfoPO.getActiveIndicator()) {
            return Boolean.FALSE;
        }
        if (trfInfoPO.getStatus() >= com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus()) {
            return Boolean.FALSE;
        }
        if (Objects.nonNull(trfInfoPO.getIntegrationLevel()) && (
                Objects.equals(Integer.valueOf(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.MORE_VS_ONE.getRule())
                        || Objects.equals(Integer.valueOf(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_ONE.getRule())
        )
        ) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 判断trf 是否未绑定
     *
     * @return
     */

    public boolean checkUnbind(Long trfId, Integer systemId, TrfUnbindReq unbindReq) {
        Assert.notNull(trfId);
        Assert.notNull(systemId);
        Assert.notBlank(unbindReq.getOrder().getOrderNo());
        String orderNo = unbindReq.getOrder().getOrderNo();
        TrfInfoPO trfInfoPO = trfInfoMapper.selectByPrimaryKey(trfId);
        //Trf 不存在或无效，则直接返回
        if (ObjectUtil.isNull(trfInfoPO) || TrfConstants.DEL_INACTIVE == trfInfoPO.getActiveIndicator()) {
            return false;
        }
        //TODO By 配置 哪些状态可以解绑
//        String configvalue = configClient.getConfigForIntegerationLevel(systemId, TrfConstants.PRODUCTION_SL, TrfConstants.INTEGERATION_LEVEL);
//        if (StringUtils.isEmpty(configvalue)) {
//            throw new BizException(ResponseCode.FAIL, "请检查状态配置！");
//        }
//        if (configvalue.contains(String.valueOf(trfInfoPO.getStatus()))) {
//            return Boolean.FALSE;
//        }
        // 如果当前order是pending状态也不可操作unbind
        TrfOrderPO trfOrderPO = trfOrderDomainService.selectOrderInfo(trfId, orderNo, systemId);
        if(Func.isEmpty(trfOrderPO) && Func.isNotEmpty(unbindReq.getOrder().getRealOrderNo())) {
            trfOrderPO = trfOrderDomainService.selectOrderInfo(trfId, unbindReq.getOrder().getRealOrderNo(), systemId);
        }
        if (Func.isEmpty(trfOrderPO) || Objects.equals(trfOrderPO.getPendingFlag(), ActiveType.Enable.getStatus())) {
            return Boolean.FALSE;
        }

        if (trfInfoPO.getStatus() >= com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Completed.getStatus()) {
            // SCI-724 status == Revise and sourceType == OrderToTrf 允许 unbind
            if (trfStatusIsRevise(trfInfoPO) && trfSourceIsOrderToTrf(trfInfoPO)) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private static boolean trfSourceIsOrderToTrf(TrfInfoPO trfInfoPO) {
        return TrfSourceType.is(trfInfoPO.getSource(), TrfSourceType.Order2TRF);
    }

    private static boolean trfStatusIsRevise(TrfInfoPO trfInfoPO) {
        return com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.check(trfInfoPO.getStatus());
    }

    public List<TrfInfoPO> selectByTrfIds(List<Long> trfIds) {
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria().andIdIn(trfIds);
        return trfInfoMapper.selectByExample(trfInfoExample);
    }

    public List<TrfInfoPO> selectByTrfIdsActive(List<Long> trfIds) {
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria().andIdIn(trfIds).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfInfoMapper.selectByExample(trfInfoExample);
    }

    public void saveCustomerTrf(CustomerTrfInfoPO trf) {
        customerTrfInfoMapper.insert(trf);
    }

    public void batchSaveCustomerTrf(List<CustomerTrfInfoPO> trfs) {
        trfInfoExtMapper.batchInsert(trfs);
    }

    public List<TrfReportDTO> updateReportList(List<TrfReportDTO> reportList, Integer labId) {
        List<TrfReportDTO> list = trfReportDomainService.updateReportList(reportList, labId);
        return list;
    }

    public void updateReportDeliveryFlagByTrfNo(String trfNo, Integer refSystemId) {
        TrfInfoPO trfInfoPO = selectByTrfNo(refSystemId, trfNo);
        if (Func.isEmpty(trfInfoPO)) {
            return;
        }
        List<String> reportNos = trfReportDomainService.selectByTrfId(trfInfoPO.getId()).stream()
                .map(TrfReportPO::getReportNo)
                .collect(Collectors.toList());

        trfReportDomainService.updateTrfReportDeliveryFlag(trfInfoPO.getId(), DeliveryFlagEnum.NEW.getCode());
        trfReportDomainService.updateTrfReportDeliveryFlag(reportNos);
    }

    public Integer getInterfaceExclude(String trfNo, Integer refSystemId) {
        TrfInfoPO trfInfoByTrfNo = getTrfInfoByTrfNo(trfNo, refSystemId);
        if (Func.isNotEmpty(trfInfoByTrfNo)) {
            return trfInfoByTrfNo.getInterfaceExclude();
        }
        return null;
    }

    public void saveTrfReportLevelData(List<String> externalSampleNoList, String trfNo, Integer refSystemId, String orderNo, String bizType) {
        if (Func.isEmpty(externalSampleNoList)) {
            return;
        }
        List<TrfReportLevelPO> list = new ArrayList<>();
        externalSampleNoList.forEach(
                sampleNo -> {
                    TrfReportLevelPO trfReportLevelPO = new TrfReportLevelPO();
                    trfReportLevelPO.setId(idService.nextId());
                    trfReportLevelPO.setTrfNo(trfNo);
                    trfReportLevelPO.setRefSystemId(refSystemId);
                    trfReportLevelPO.setOrderNo(orderNo);
                    trfReportLevelPO.setBizNo(sampleNo);
                    trfReportLevelPO.setBizType(bizType);
                    trfReportLevelPO.setCreatedDate(new Date());
                    trfReportLevelPO.setLastModifiedTimestamp(new Date());
                    list.add(trfReportLevelPO);
                }
        );
        if (Func.isNotEmpty(list)) {
            trfReportLevelMapper.batchInsert(list);
        }
    }
}
