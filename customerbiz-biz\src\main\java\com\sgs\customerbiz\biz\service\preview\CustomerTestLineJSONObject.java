package com.sgs.customerbiz.biz.service.preview;

import com.alibaba.fastjson.JSONObject;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

public class CustomerTestLineJSONObject extends JSONObject {


    public static final String ITEM_NO = "itemNo";
    public static final String TEST_PACKAGE_ID = "testPackageId";

    public String getItemNo() {
        return Optional.ofNullable(getString(ITEM_NO)).orElse("");
    }

    public String getPackageNo() {
        return Optional.ofNullable(getString(TEST_PACKAGE_ID)).orElse("");
    }

    @NotNull
    public static CustomerTestLineJSONObject ofTestProperty(String testLine) {
        CustomerTestLineJSONObject customerTestLineJSONObject = new CustomerTestLineJSONObject();
        customerTestLineJSONObject.put("testProperty", testLine);
        return customerTestLineJSONObject;
    }

}
