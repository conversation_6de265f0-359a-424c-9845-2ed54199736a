package com.sgs.customerbiz.app.process;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.facade.model.dto.TodoListBaseDataDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class TodoListImportToTRFProcess extends TodoListService implements BaseProcess<TrfImportResult,TrfImportReq>{
    @Resource
    private ImportToTRFProcess importToTRFProcess;

    @Autowired
    private TodoListService todoListService;

    public TodoListImportToTRFProcess(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public List<TrfImportResult> getTrfImportResult(String trfNo,Integer refSystemId,TodoListBaseDataDTO data, LabInfo labInfo) {
        List<TrfImportResult> importResults = new ArrayList<>();
        TrfImportReq trfImportReq = buildTrfImportReq(trfNo, refSystemId, labInfo);
        TrfImportResult trfDTO = doProcess(trfImportReq);
        if (trfDTO != null) {
            importResults.add(trfDTO);
        }
        return importResults;
    }

    @Override
    public TrfImportResult doProcess(TrfImportReq object) {
        return importToTRFProcess.doProcess(object);
    }
}
