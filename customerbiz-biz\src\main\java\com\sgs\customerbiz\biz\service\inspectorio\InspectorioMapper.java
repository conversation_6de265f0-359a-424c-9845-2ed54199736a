package com.sgs.customerbiz.biz.service.inspectorio;

import com.sgs.customerbiz.integration.dto.inspectorio.RevisableData;
import com.sgs.framework.model.enums.RefSystemIdEnum;

import java.util.Date;
import java.util.List;

/**
 * 抽象Inspectorio数据Mapper的通用操作
 * @param <T> PO对象类型
 * @param <E> Example对象类型
 */
public interface InspectorioMapper<T, E> {
    int countByExample(E example);
    List<T> page(int offset, int limit);
    int batchInsert(List<T> list);
    int batchUpdate(List<T> list);
    String getExternalId(T record);
    String getData(T record);
    Integer getActiveIndicator(T record);
    Date getCreatedDate(T record);
    T convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum);
    T convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, String datePattern);
    T convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns);
    T createCopyForUpdate(T original, RevisableData newData, RefSystemIdEnum refSystemIdEnum);
    T createCopyForUpdate(T original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, String datePattern);
    T createCopyForUpdate(T original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns);
    T createCopyForDelete(T original);
} 