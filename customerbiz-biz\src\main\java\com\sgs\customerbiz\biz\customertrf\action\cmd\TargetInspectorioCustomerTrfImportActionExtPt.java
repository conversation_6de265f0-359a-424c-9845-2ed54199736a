package com.sgs.customerbiz.biz.customertrf.action.cmd;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.NumberUtil;
import com.sgs.customerbiz.facade.model.file.FileInfo;
import com.sgs.customerbiz.integration.FileClient;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSampleAttrDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSampleDffDTO;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SgsSystem;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.biz.customertrf.action.CustomerTrfImportActionExtPt;
import com.sgs.framework.tool.utils.Func;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import static com.sgs.customerbiz.core.constants.Constants.*;

@Service
@Slf4j
@Extension(scenario = "10028")
public class TargetInspectorioCustomerTrfImportActionExtPt extends DefaultCustomerTrfImportActionExtPt implements CustomerTrfImportActionExtPt {

    private final FileClient fileClient;

    public static final Set<String> tableIntegrationKeys = ImmutableSet.of(
            "csw_fpu_reportAfter50WashDryTesting",
            "csw_proto_details",
            "csw_trim_details",
            "csw_gpu_details",
            "programInformation",
            "zipper",
            "rsl_childrenProductContainsPuFoamDetail",
            "rsl_productTreatedWithFlameRetardantsDetail",
            "rsl_productTreatedWaterStainOilResistantRepellentProofDetail",
            "reportDistributionEmails",
            "rsl_textileFabricsWithScrapeableCoatings_fabricMaterialCoatings",
            "rsl_textileFabricsWithScrapeableCoatings_embellishmentType",
            "rsl_plasticMetalComponent",
            "rsl_leatherCoatingsSubstrates_leatherColor",
            "rsl_tintedOverdyedfabricFinish_embellishmentType",
            "rsl_textileFabricsWithWaterStainOilResistantRepellentFinishes_embellishmentType",
            "productClaimRelease"
    );

    public static final Set<String> sampleKeys = ImmutableSet.of(
            "rawMaterialReports"
    );

    public TargetInspectorioCustomerTrfImportActionExtPt(FileClient fileClient) {
        this.fileClient = fileClient;
    }

    @Override
    protected void validateCustomerTrfJson(JSONObject customerTrfJson) {
        String status = Optional.ofNullable(customerTrfJson)
                .filter(detail -> detail.containsKey("status"))
                .map(detail -> detail.getString("status"))
                .orElseThrow(() -> new IllegalStateException("details not contains 'status' From inspectorio"));
        if (!status.equals("confirmed")) {
            throw new IllegalStateException("can't import inspectorio, inspectorio status is not confirmed");
        }  
    }

    @Override
    protected String preConvertCustomerTrfJson(String customerTrfJson) {
        Map<String, Object> map = new HashMap<>();
        JSONObject customerTrfJsonObject = JSONObject.parseObject(customerTrfJson);
        // 目前是说只会取forTestCreation
        Object forTestCreationObj = JSONPath.eval(customerTrfJsonObject, "$.questionnaire.forTestCreation");
        if (Func.isNotEmpty(forTestCreationObj) && forTestCreationObj instanceof Collection) {
            List<ForTestCreationAttr> forTestCreationAttrs = JSONObject.parseArray(JSONObject.toJSONString(forTestCreationObj), ForTestCreationAttr.class);
            if (Func.isNotEmpty(forTestCreationAttrs)) {
                forTestCreationAttrs.forEach(
                        l -> {
                            if (sampleKeys.contains(l.getIntegrationKey())) {
                                List<String> headers = l.getHeaders();
                                List<Object> values = l.getValues();
                                if(Func.isEmpty(headers) || Func.isEmpty(values)) {
                                    return;
                                }
                                List<Pair<Integer, String>> headersWithIndex = IntStream.range(0,headers.size())
                                        .mapToObj(i -> new Pair<>(i, replaceSpecialChars(headers.get(i))))
                                        .collect(Collectors.toList());
                                List<JSONObject> jsonValues = new ArrayList<>();
                                for (Object value : values) {
                                    if (value instanceof List) {
                                        List<?> v = ((List<?>) value);
                                        JSONObject obj = new JSONObject();
                                        headersWithIndex.forEach(h -> {
                                            obj.put(h.getSecond(), v.get(h.getFirst()));
                                        });
                                        jsonValues.add(obj);
                                    }
                                }
                                map.put(replaceSpecialChars(l.getIntegrationKey().trim()), jsonValues);
                            }
                            else if (tableIntegrationKeys.contains(l.getIntegrationKey())) {
                                List<Object> values = l.getValues();
                                if (Func.isEmpty(values)) {
                                    values = new ArrayList<>();
                                }
                                List<List<Object>> valuesList = new ArrayList<>();
                                if (!values.isEmpty()) {
                                    for (Object value : values) {
                                        if (value instanceof List) {
                                            valuesList.add((List<Object>) value);
                                        } else {
                                            // 如果 value 不是 List，将其包装在一个新的 List 中
                                            valuesList.add(Lists.newArrayList(value));
                                        }
                                    }
                                }
                                List<String> headers = l.getHeaders();
                                if (Func.isNotEmpty(headers)) {
                                    for (int i = 0; i < headers.size(); i++) {
                                        map.put(replaceSpecialChars(l.getIntegrationKey() + "." + headers.get(i).trim()), formatDataByIndex((valuesList), i));
                                    }
                                }

                            } else {
                                // key不会重复
                                if (Func.isNotEmpty(l.getValues())) {
                                    map.put(replaceSpecialChars(l.getIntegrationKey().trim()), l.getValues().stream().map(Object::toString).collect(Collectors.joining(",")));
                                }
                            }
                        }
                );
            }
        }
        Object questionnaireAttachmentObj = JSONPath.eval(customerTrfJsonObject, "$.attachments.questionnaire");
        Object activityAttachmentObj = JSONPath.eval(customerTrfJsonObject, "$.attachments.activity");
        Object generalInformationAttachmentObj = JSONPath.eval(customerTrfJsonObject, "$.attachments.generalInformation");
        Object testResultAttachmentObj = JSONPath.eval(customerTrfJsonObject, "$.attachments.testResult");
        List<InspectorAttachment> list = new ArrayList<>();
        convertAttachment(list, questionnaireAttachmentObj);
        convertAttachment(list, activityAttachmentObj);
        convertAttachment(list, generalInformationAttachmentObj);
        convertAttachment(list, testResultAttachmentObj);
//        convertAttachment(list, map.get("otherDocuments"));
//        convertAttachment(list, map.get("otherDocuments_stuffedFilledProducts"));
//        convertAttachment(list, map.get("otherDocuments_lawLabelRegistration_originatingState"));
//        convertAttachment(list, map.get("otherDocuments_lawLabelRegistration_regNumber"));
//        convertAttachment(list, map.get("otherDocuments_apparelExceptionNotice"));
//        convertAttachment(list, map.get("otherDocuments_trimComplianceDeclaration"));
//        convertAttachment(list, map.get("otherDocuments_childrenSleepwearLooseFit"));
//        convertAttachment(list, map.get("otherDocuments_releaseForNonTestablePerformanceClaims"));
//        convertAttachment(list, map.get("otherDocuments_productClaimsReleaseForm"));
//        convertAttachment(list, map.get("otherDocuments_stuffedFilledProductsAttachment"));
//        convertAttachment(list, map.get("otherDocuments_testingDeviationForm"));
//        convertAttachment(list, map.get("otherDocuments_toxicologyRiskAssessment"));
//        convertAttachment(list, map.get("otherDocuments_carb"));
//        convertAttachment(list, map.get("otherDocuments_fcc"));
//        convertAttachment(list, map.get("otherDocuments_fda"));
//        convertAttachment(list, map.get("otherDocuments_componentStageTestReport"));
//        convertAttachment(list, map.get("otherDocuments_lawLabelRegistrationAttachment"));
//        convertAttachment(list, map.get("otherDocuments_otherTypes"));


        customerTrfJsonObject.put("questionnaire", map);


        List<InspectorAttachment> uploaded = safeUploaded(list);
        customerTrfJsonObject.put("customerAttachments", uploaded);

        return customerTrfJsonObject.toJSONString();
    }

    @NotNull
    private List<InspectorAttachment> safeUploaded(List<InspectorAttachment> list) {
        try {
            List<InspectorAttachment> initUUID = list.stream().map(node -> node.cloudId(UUID.randomUUID().toString())).collect(Collectors.toList());
            Map<String, String> imgMap = initUUID.stream()
                    .filter(node -> Objects.nonNull(node) && StringUtils.isNotBlank(node.getUrl()))
                    .collect(Collectors.toMap(
                            InspectorAttachment::getCloudId,
                            InspectorAttachment::getUrl)
                    );
            Map<String, InspectorAttachment> attachmentMap = initUUID.stream()
                    .filter(node -> Objects.nonNull(node) && StringUtils.isNotBlank(node.getUrl()))
                    .collect(Collectors.toMap(
                            InspectorAttachment::getCloudId, Function.identity())
                    );
            Map<String, CustomResult<FileInfo>> uploadResult = fileClient.parallelUpload(SgsSystem.SODA, imgMap);
            return attachmentMap.values().stream()
                    .filter(attachment -> uploadResult.containsKey(attachment.getCloudId()))
                    .filter(attachment -> uploadResult.get(attachment.getCloudId()).isSuccess())
                    .map(attachment -> attachment.cloudId(uploadResult.get(attachment.getCloudId()).getData().getCloudID()))
                    .collect(Collectors.toList());
        }  catch (ExecutionException | InterruptedException e) {
            log.warn("got an error when parallel uploading picture", e);
            return list;
        }
    }

    public static String replaceSpecialChars(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // Replace any non-alphanumeric character (except Chinese characters) with underscore and remove spaces
        return input.replaceAll("\\s+", "").replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_");
    }

    @Override
    protected TrfDTO dffConvertForSample(TrfDTO trfDTO) {
        List<TrfSampleDffDTO> trfDTOSampleList = trfDTO.getSampleList();
        //1、处理Trf.SampleList节点
        if (Func.isEmpty(trfDTOSampleList)) {
            //如果没有SampleList节点，则直接返回
            return trfDTO;
        }

        AtomicReference<Integer> count = new AtomicReference<>(1);
        //2、处理Trf.SampleList 节点
        trfDTOSampleList.forEach(trfSampleDTO -> {

            if (Func.isEmpty(trfSampleDTO.getProductItemNo())) {
                trfSampleDTO.setProductItemNo(Constants.SupplierCode + count.get());
            }
            List<TrfSampleAttrDTO> sampleAttrDTOList = trfSampleDTO.getSampleAttrList();
            if (Func.isEmpty(sampleAttrDTOList)) {
                //如果没有SampleAttrList节点，则直接返回
                return;
            }
            //3、处理SampleAttr 结构
            Integer refSystemId = trfDTO.getHeader().getRefSystemId();
            String buCode = null;
            if (Func.isNotEmpty(trfDTO.getHeader().getLab()) && Func.isNotEmpty(trfDTO.getHeader().getLab().getBuCode())) {
                buCode = trfDTO.getHeader().getLab().getBuCode();
            }

            String templateId = trfDTO.getHeader().getTrfTemplateId();
            String templateType = trfDTO.getHeader().getTrfTemplateType();

            sampleAttrDTOList = doConvertForSample(sampleAttrDTOList, refSystemId, buCode, templateId, templateType);

            //重新赋值
            trfSampleDTO.setSampleAttrList(sampleAttrDTOList);
            count.getAndSet(count.get() + 1);
        });
        return trfDTO;
    }

    public static void main(String[] args) {
        // Test with spaces, parentheses and dots
        String test1 = "Hello World (test).txt";
        System.out.println("Original: " + test1);
        System.out.println("Replaced: " + replaceSpecialChars(test1));

        // Test with Chinese characters
        String test2 = "你好 World (测试).txt";
        System.out.println("\nOriginal: " + test2);
        System.out.println("Replaced: " + replaceSpecialChars(test2));

        // Test with empty string
        String test3 = "//\\";
        System.out.println("\nOriginal: " + test3);
        System.out.println("Replaced: " + replaceSpecialChars(test3));

        // Test with null
        String test4 = null;
        System.out.println("\nOriginal: " + test4);
        System.out.println("Replaced: " + replaceSpecialChars(test4));
    }

    private void convertAttachment(List<InspectorAttachment> list, Object obj) {
        if (Func.isEmpty(obj) || Objects.isNull(list)) {
            return;
        }
        if (obj instanceof List) {
            List<Object> attachmentList = (List<Object>) obj;
            for (Object attachmentObj : attachmentList) {
                if (attachmentObj instanceof Map) {
                    Map<String, Object> attachmentMap = (Map<String, Object>) attachmentObj;
                    String name = (String) attachmentMap.get("name");
                    String url = (String) attachmentMap.get("url");
                    list.add(new InspectorAttachment(null,name, url));
                }
            }
        }
    }

    public static String formatDataByIndex(List<List<Object>> values, int index) {
        if (values == null || values.isEmpty()) {
            return "";
        }

        if (index < 0 || index >= values.get(0).size()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (List<Object> row : values) {
            if (index < row.size()) {
                if (result.length() > 0) {
                    result.append(",");
                }
                result.append(row.get(index));
            }
        }

        return result.toString();
    }

    public static String formatData(List<String> headers, List<List<String>> values) {
        if (headers == null || values == null || headers.isEmpty() || values.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < headers.size(); i++) {
            result.append(headers.get(i)).append("：");
            for (int j = 0; j < values.size(); j++) {
                if (j > 0) {
                    result.append(",");
                }
                if (i < values.get(j).size()) {
                    result.append(values.get(j).get(i));
                }
            }
            result.append("\n");
        }

        // 去掉最后一个多余的换行符
        if (result.length() > 0) {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }

    @Data
    public static final class ForTestCreationAttr {
        private List<Object> values;
        private String type;
        private String integrationKey;
        private List<String> headers;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class InspectorAttachment{
        private String cloudId;
        private String name;
        private String url;

        public InspectorAttachment cloudId(String newCloudId) {
            return new InspectorAttachment(newCloudId, name, url );
        }
    }
}
