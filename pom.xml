<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sgs.customerbiz</groupId>
    <artifactId>sgs-customerbiz</artifactId>
    <packaging>pom</packaging>
    <version>1.0.22</version>
    <modules>
        <module>customerbiz-core</module>
        <module>customerbiz-facade-model</module>
        <module>customerbiz-model</module>
        <module>customerbiz-dbstorages</module>
        <module>customerbiz-facade</module>
        <module>customerbiz-domain</module>
        <module>customerbiz-facade-impl</module>
        <module>customerbiz-integration</module>
        <module>customerbiz-test</module>
        <module>customerbiz-web</module>
        <module>customerbiz-biz</module>
        <module>customerbiz-mybatis-generator</module>
        <module>customerbiz-dfv</module>
        <module>config-api</module>
        <module>config-impl</module>
        <module>sci-common-service</module>
        <module>customerbiz-validation</module>
        <module>customerbiz-process</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <swagger.bootstrapui.version>1.9.6</swagger.bootstrapui.version>

        <!-- 文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring.boot.version>2.4.2</spring.boot.version>
        <redisson.version>3.13.6</redisson.version>

        <spring-kafka.version>2.4.0.RELEASE</spring-kafka.version>
        <kafkaclient.version>1.1.6</kafkaclient.version>

        <!--数据库配置-->
        <mybatis.version>3.4.1</mybatis.version>
        <mybatis-spring.version>1.3.0</mybatis-spring.version>
        <mysql.version>5.1.31</mysql.version>
        <druid.version>1.1.14</druid.version>

        <pagehelper.version>5.1.8</pagehelper.version>

        <httpclient.version>4.5.2</httpclient.version>
        <httpcore.version>4.4.4</httpcore.version>

        <jedis.version>3.3.0</jedis.version>

        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.0.15.Final</hibernate-validator.version>

        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-net.version>3.4</commons-net.version>
        <commons-collection4.version>4.4</commons-collection4.version>
        <lombok.version>1.16.18</lombok.version>
        <hutool.version>5.8.18</hutool.version>
        <cola.version>4.3.1</cola.version>

        <log4j-over-slf4j.version>1.7.25</log4j-over-slf4j.version>
        <fastjson.version>1.2.4</fastjson.version>
        <poi.version>3.17</poi.version>
        <jboss-jaxrs-api.version>1.0.0.Final</jboss-jaxrs-api.version>

        <dubbo.version>2.8.4</dubbo.version>
        <junit.version>4.12</junit.version>
        <jmockit.version>1.27</jmockit.version>

        <zkclient.version>0.1</zkclient.version>
        <javassist.version>3.12.1.GA</javassist.version>

        <commons-discovery.version>0.5</commons-discovery.version>

        <dom4j.version>1.6.1</dom4j.version>
        <joda-time.verson>2.10</joda-time.verson>
        <cxf.version>3.1.6</cxf.version>

        <springfox.version>3.0.0</springfox.version>
        <hystrix-version>1.5.9</hystrix-version>

        <aviator.version>5.3.3</aviator.version>

        <caffeine.version>2.9.0</caffeine.version>
        <grus-async.version>1.0.18</grus-async.version>
        <lombok.version>1.16.18</lombok.version>

        <user.version>1.0.6</user.version>

        <!-- self version -->
        <customerbiz.version>1.0.22</customerbiz.version>
        <customerbiz-facade.version>1.0.22</customerbiz-facade.version>
        <customerbiz-model.version>1.0.22</customerbiz-model.version>
        <customerbiz-dfv.version>1.0.22</customerbiz-dfv.version>
        <customerbiz-validation.version>1.0.22</customerbiz-validation.version>
        <config-api.version>1.0.22</config-api.version>
        <testdatabiz.version>1.3.46</testdatabiz.version>
        <sgs-framework.version>1.0.727-beta</sgs-framework.version>
        <sgs-framework.config>1.0.739-beta</sgs-framework.config>
        <sgs-framework-model.version>1.0.935-beta</sgs-framework-model.version>
        <otsnotes.version>1.1.27</otsnotes.version>
        <preorder-facade.version>2.1.147</preorder-facade.version>
        <grus-core.version>1.1.0</grus-core.version>
        <bizlog.version>1.1.0</bizlog.version>
        <trimslocal.version>1.1.72</trimslocal.version>
        <extsystem.version>1.0.55</extsystem.version>
        <gpo-core-api.version>1.0.21</gpo-core-api.version>
        <sgs.dff.facade.version>1.0.3</sgs.dff.facade.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <liteflow.version>2.12.3</liteflow.version>
        <tool-traceability.version>1.0.25-SNAPSHOT</tool-traceability.version>
        <vavr.version>0.10.6</vavr.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- https://mvnrepository.com/artifact/io.vavr/vavr -->
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>${vavr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs</groupId>
                <artifactId>tool-traceability</artifactId>
                <version>${tool-traceability.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-core</artifactId>
                <version>${liteflow.version}</version>
            </dependency>
            <!-- spring-boot配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>
            <!--kafka-->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka-test</artifactId>
                <scope>test</scope>
            </dependency>-->
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-kafkaclient</artifactId>
                <version>${kafkaclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.kafka</groupId>
                        <artifactId>spring-kafka</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-bizlog</artifactId>
                <version>${bizlog.version}</version>
            </dependency>


            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>
            <!--数据库配置-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 第三方Mybatis分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!--validator-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${jboss-jaxrs-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collection4.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <!-- MethodProvider -->
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http</artifactId>
                <version>${cxf.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.bootstrapui.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-metrics-event-stream</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-javanica</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-servo-metrics-publisher</artifactId>
                <version>${hystrix-version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.verson}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-async</artifactId>
                <version>${grus-async.version}</version>
            </dependency>

            <!-- self -->
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-domain</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-web</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-model</artifactId>
                <version>${customerbiz-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-facade</artifactId>
                <version>${customerbiz-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-facade-model</artifactId>
                <version>${customerbiz-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-facade-impl</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-core</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-integration</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-test</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-dbstorages</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-mybatis-generator</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>sci-common-service</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-dfv-api</artifactId>
                <version>${customerbiz-dfv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-dfv-service</artifactId>
                <version>${customerbiz-dfv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-validation</artifactId>
                <version>${customerbiz-validation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-biz</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.config</groupId>
                <artifactId>config-api</artifactId>
                <version>${config-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.config</groupId>
                <artifactId>config-impl</artifactId>
                <version>${config-api.version}</version>
            </dependency>
            <!--  -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
            </dependency>


            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>${commons-discovery.version}</version>
            </dependency>


            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>${commons-discovery.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-core</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-config</artifactId>
                <version>${sgs-framework.config}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-model</artifactId>
                <version>${sgs-framework-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-tool</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-swagger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                </exclusions>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>preorder-facade</artifactId>
                <version>${preorder-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-core</artifactId>
                <version>${grus-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.trimslocal</groupId>
                <artifactId>trimslocal-facade</artifactId>
                <version>${trimslocal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.user</groupId>
                <artifactId>UserManagementFacadeService</artifactId>
                <version>${user.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.extsystem</groupId>
                <artifactId>extsystem-facade</artifactId>
                <version>${extsystem.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>gpo-core-api</artifactId>
                <version>${gpo-core-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-extension-starter</artifactId>
                <version>${cola.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.config</groupId>
                <artifactId>config-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.config</groupId>
                <artifactId>config-impl</artifactId>
                <version>${project.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>my-deploy-release</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>my-deploy-snapshot</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
