package com.sgs.customerbiz.biz.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.service.inspectorio.InspectorioDataBizService;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class SyncLululemonPackageAndTestLineInfoXXLJobScheduler {

    private final InspectorioDataBizService inspectorioDataBizService;

    @Data
    public static class SyncLululemonPackageAndTestLineInfoXXLJobParam {
        private Integer sizeOfApi = 10;
        private Integer sizeOfDb = 10;
        private Integer maxSizeOfApi = Integer.MAX_VALUE;
        private List<String> datePatterns = Arrays.asList(
            "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.S'Z'"
        );
    }

    @XxlJob("syncLululemonPackageAndTestLineInfoHandler")
    public void syncLululemonPackageAndTestLineInfoHandler() {
        JobLogUtil.info(log, "start sync lululemon package and testline info handler on " + JobLogUtil.now());

        try {
            String jobParam = XxlJobHelper.getJobParam();
            InspectorioDataBizService.InspectorioSyncParam param;
            
            if (jobParam != null && !jobParam.trim().isEmpty()) {
                SyncLululemonPackageAndTestLineInfoXXLJobParam jobParamObj = JSON.parseObject(jobParam, SyncLululemonPackageAndTestLineInfoXXLJobParam.class);
                JobLogUtil.info(log, "param from job : {}", jobParamObj);
                
                param = new InspectorioDataBizService.InspectorioSyncParam();
                param.setSizeOfApi(jobParamObj.getSizeOfApi());
                param.setSizeOfDb(jobParamObj.getSizeOfDb());
                param.setMaxSizeOfApi(jobParamObj.getMaxSizeOfApi());
                param.setDatePatterns(jobParamObj.getDatePatterns());
            } else {
                // 使用默认参数
                param = new InspectorioDataBizService.InspectorioSyncParam();
                param.setSizeOfApi(10);
                param.setSizeOfDb(10);
                param.setMaxSizeOfApi(Integer.MAX_VALUE);
                // datePatterns使用默认值
                JobLogUtil.info(log, "using default param: {}", param);
            }

            inspectorioDataBizService.syncLululemonPackageAndTestLineInfo(param);
        } catch (Throwable t) {
            JobLogUtil.error(log, t, "sync lululemon package and testline info handler error");
            XxlJobHelper.handleFail(t.getMessage());
        }
        JobLogUtil.info(log, "success sync lululemon package and testline info handler on " + JobLogUtil.now());
    }
} 