package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InspectorioPackageInfoMapper {
    int countByExample(InspectorioPackageInfoExample example);

    int deleteByExample(InspectorioPackageInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(InspectorioPackageInfoPO record);

    int insertSelective(InspectorioPackageInfoPO record);

    List<InspectorioPackageInfoPO> selectByExample(InspectorioPackageInfoExample example);

    InspectorioPackageInfoPO selectByPrimaryKey(Integer id);

    List<InspectorioPackageInfoPO> page(@Param("offset") int offset, @Param("limit") int limit);

    int updateByExampleSelective(@Param("record") InspectorioPackageInfoPO record, @Param("example") InspectorioPackageInfoExample example);

    int updateByExample(@Param("record") InspectorioPackageInfoPO record, @Param("example") InspectorioPackageInfoExample example);

    int updateByPrimaryKeySelective(InspectorioPackageInfoPO record);

    int updateByPrimaryKey(InspectorioPackageInfoPO record);

    int batchInsert(List<InspectorioPackageInfoPO> list);

    int batchUpdate(List<InspectorioPackageInfoPO> list);
}