package com.sgs.customerbiz.biz.service.preview;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.ImmutableList;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.customerbiz.biz.config.PreviewProps;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TestLinePreviewDTO;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.framework.tool.utils.Func;
import io.vavr.control.Validation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PreviewSyncReportService {

    public static final String TEST_LINE_TEMPLATES = "TestLineTemplates";

    private final PreviewProps previewProps;


    private final TrfInfoExtMapper trfInfoExtMapper;

    private final CustomerTestLineService customerTestLineService;

    private final SgsTestLineService testLineService;

    private final ConfigClient configClient;

    private final JsonDataConvertor jsonDataConvertor;

    private final PreviewRequestValidator validator;

    public PreviewSyncReportService(PreviewProps previewProps,
                                    TrfInfoExtMapper trfInfoExtMapper,
                                    CustomerTestLineService customerTestLineService,
                                    SgsTestLineService testLineService,
                                    ConfigClient configClient, JsonDataConvertor jsonDataConvertor, PreviewRequestValidator validator) {
        this.previewProps = previewProps;
        this.trfInfoExtMapper = trfInfoExtMapper;
        this.customerTestLineService = customerTestLineService;
        this.testLineService = testLineService;
        this.configClient = configClient;
        this.jsonDataConvertor = jsonDataConvertor;
        this.validator = validator;
    }

    public List<TestLinePreviewDTO> preview(TrfSyncReq request) {
        return preview(toTrfFullDTO(request));
    }

    public List<TestLinePreviewDTO> preview(TrfFullDTO request) {
        // 校验参数
        Validation<String, TrfFullDTO> validate = validator.validate(request);
        TrfFullDTO validRequest = validate.getOrElseThrow(() -> new IllegalArgumentException(validate.getError()));
        // 加载所需要的数据
        TestLinePreviewCtx ctx = createCtx(validRequest);
        if(ctx.isCustomerTrfEmpty()) {
            return Collections.emptyList();
        }
        // 根据模板，将客户的trf数据转换成需要预览的列表数据 List<CustomerTestLineJSONObject>
        // 根据CustomerTestLineJSONObject的itemNo属性查询ILayer得到 @TestLineMappingInfoV2DTO 多个customerTestLine可能对应到一个Mapping
        List<CustomerTestLine> customerTestLines = customerTestLineService.testLines(ctx.customerGroupCode, validRequest.getBuCode(), ctx.customerTestLineTemplate, ctx.customerTrfInfoMap.values());
        // 根据request的testLineList通过ILayer查询得到 @TestLineMappingInfoV2DTO 一个testLine可能对应到多个Mapping
        List<SgsTestLine> trfTestLineDTOS = testLineService.testLines(ctx.customerGroupCode, validRequest.getBuCode(), validRequest.getTestLineList());
        // 通过CustomerTestLineJSONObjectData 对应的Mapping和testLineList的Mapping的asKey进行比较，执行一个zipWith操作
        List<TestLineRow> rows = zip(customerTestLines, trfTestLineDTOS);
        JSONObject root = new JSONObject();
        root.put("testLines", rows);
        JSON convert = jsonDataConvertor.convert(JSON.toJSONString(root, SerializerFeature.WriteMapNullValue), ctx.finalTestLineTemplate);

        return Optional.ofNullable(convert)
                .map(c -> (JSONObject) c)
                .map(c -> c.getJSONArray("rows"))
                .map(rowList -> JSONArray.parseArray(rowList.toJSONString(), TestLinePreviewDTO.class))
                .orElse(Collections.emptyList());

    }

    public static TrfFullDTO toTrfFullDTO(TrfSyncReq syncReq) {
        TrfFullDTO trfFullDTO = new TrfFullDTO();
        trfFullDTO.setProductLineCode(syncReq.getProductLineCode());
        trfFullDTO.setBuCode(syncReq.getBuCode());
        trfFullDTO.setTrfList(Optional.ofNullable(syncReq.getHeader()).map(TrfSyncHeaderDTO::getTrfList).orElse(Collections.emptyList()));
        trfFullDTO.setOrderList(Optional.ofNullable(syncReq.getOrder()).<List<TrfOrderDTO>>map(ImmutableList::of).orElse(Collections.emptyList()));
        trfFullDTO.setReportList(syncReq.getReportList());
        trfFullDTO.setTestLineList(syncReq.getTestLineList());
        trfFullDTO.setTestSampleList(syncReq.getTestSampleList());
        trfFullDTO.setTestResultList(syncReq.getTestResultList());
        trfFullDTO.setQuotationList(syncReq.getQuotationList());
        trfFullDTO.setInvoiceList(syncReq.getInvoiceList());
        return trfFullDTO;
    }

    @NotNull
    private TestLinePreviewCtx createCtx(TrfFullDTO validRequest) {
        // 检查请求数据，并且根据请求数据，加载必要的如客户trf列表数据
        Optional<List<TrfHeaderDTO>> trfListOpt = Optional.ofNullable(validRequest.getTrfList());
        Optional<Integer> refSystemId = Optional.ofNullable(validRequest.getTrfList())
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(trfList -> trfList.stream().findFirst())
                .map(TrfHeaderDTO::getRefSystemId);
        Optional<String> productLineCode = Optional.of(validRequest.getBuCode()).filter(StringUtils::isNotBlank);
        Optional<String> buyerCustomerGroupCode = Optional.ofNullable(validRequest.getOrderList())
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(orders -> orders.stream().findFirst())
                .map(TrfOrderDTO::getCustomerList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(customers -> customers.stream()
                        .filter(customer -> Objects.equals(customer.getCustomerUsage(), CustomerUsage.Buyer.getUsage()))
                        .findAny()
                )
                .map(TrfCustomerDTO::getCustomerGroupCode)
                .filter(StringUtils::isNotBlank);
        return AF.ap4(AF.lift4(this::createCtx),trfListOpt,refSystemId, buyerCustomerGroupCode, productLineCode)
                .orElseThrow(() -> new IllegalArgumentException("createCtx failed!"));
    }

    @NotNull
    private TestLinePreviewCtx createCtx(List<TrfHeaderDTO> trfList, Integer refSystemId, String customerGroupCode, String productLineCode) {
        Map<String, CustomerTrfInfoRsp> customerTrfInfoMap = trfList.stream()
                .map(trf -> trfInfoExtMapper.getTrfInfo(trf.getRefSystemId(), trf.getTrfNo()))
                .collect(Collectors.toMap(CustomerTrfInfoRsp::getTrfNo, Function.identity(), (v1, v2) -> v1));

        ConfigGetReq configGetReq = new ConfigGetReq();
        configGetReq.setProductLine(productLineCode);
        configGetReq.setIdentityId(Func.toStr(refSystemId));
        configGetReq.setConfigKey(TEST_LINE_TEMPLATES);
        String configValue = configClient.getConfig(configGetReq);
        String customerTestLineTemplate = Optional.ofNullable(configValue)
                .map(value -> JSONObject.parseObject(value, Feature.OrderedField))
                .map(json -> json.getJSONObject("customerTestLineTemplate"))
                .map(Objects::toString)
                .orElseThrow(() -> new IllegalArgumentException("TestLineTemplates Not Found From SCI by " + refSystemId));
        String finalTestLineTemplate = Optional.ofNullable(configValue)
                .map(value -> JSONObject.parseObject(value, Feature.OrderedField))
                .map(json -> json.getJSONObject("finalTestLineTemplate"))
                .map(Objects::toString)
                .orElseThrow(() -> new IllegalArgumentException("TestLineTemplates Not Found From SCI by " + refSystemId));
        return new TestLinePreviewCtx(customerTrfInfoMap, customerGroupCode, customerTestLineTemplate, finalTestLineTemplate);
    }

    private static class TestLinePreviewCtx {
        public final Map<String, CustomerTrfInfoRsp> customerTrfInfoMap;
        public final String customerGroupCode;
        public final String customerTestLineTemplate;
        public final String finalTestLineTemplate;

        public TestLinePreviewCtx(Map<String, CustomerTrfInfoRsp> customerTrfInfoMap, String customerGroupCode, String customerTestLineTemplate, String finalTestLineTemplate) {
            this.customerTrfInfoMap = customerTrfInfoMap;
            this.customerGroupCode = customerGroupCode;
            this.customerTestLineTemplate = customerTestLineTemplate;
            this.finalTestLineTemplate = finalTestLineTemplate;
        }

        public boolean isCustomerTrfEmpty() {
            return customerTrfInfoMap.isEmpty();
        }
    }

    public List<TestLineRow> zip(List<CustomerTestLine> customerTestLines, List<SgsTestLine> trfTestLineDTOS) {

        Map<Boolean, List<CustomerTestLine>> customerTestLineGroupByHasMapping = customerTestLines.stream().collect(Collectors.groupingBy(CustomerTestLine::hasMapping));
        Map<Boolean, List<SgsTestLine>> sgsTestLineGroupByMapping = trfTestLineDTOS.stream().collect(Collectors.groupingBy(SgsTestLine::hasMapping));

        List<TestLineRow> notMatchSgsTestLineList = sgsTestLineGroupByMapping.getOrDefault(false,Collections.emptyList()).stream()
                .map(TestLineRow::onlySgs)
                .collect(Collectors.toList());
        List<TestLineRow> notMatchCustomerTestLineList = customerTestLineGroupByHasMapping.getOrDefault(false,Collections.emptyList()).stream()
                .map(TestLineRow::onlyCustomer)
                .collect(Collectors.toList());

        List<CustomerTestLine> customerTestLineHasMappingList = customerTestLineGroupByHasMapping.getOrDefault(true, Collections.emptyList());
        List<SgsTestLine> sgsTestLineHasMappingList = sgsTestLineGroupByMapping.getOrDefault(true, Collections.emptyList());
        Map<String, List<SgsTestLine>> sgsTestLineHasMappingMap = sgsTestLineHasMappingList.stream().collect(Collectors.groupingBy(SgsTestLine::asKey));
        List<TestLineRow> rows = new ArrayList<>();
        for (CustomerTestLine customerTestLine : customerTestLineHasMappingList) {
            if(sgsTestLineHasMappingMap.containsKey(customerTestLine.asKey())){
                for (SgsTestLine sgsTestLine : sgsTestLineHasMappingMap.get(customerTestLine.asKey())) {
                    rows.add(TestLineRow.match(customerTestLine, SgsTestLine.copyUsingJson(sgsTestLine)));
                }
            } else {
                notMatchSgsTestLineList.add(TestLineRow.onlyCustomer(customerTestLine));
            }
        }
        for (CustomerTestLine customerTestLine : customerTestLineHasMappingList) {
            sgsTestLineHasMappingMap.remove(customerTestLine.asKey());
        }
        sgsTestLineHasMappingMap.values().forEach((vs) -> vs.forEach(v -> notMatchCustomerTestLineList.add(TestLineRow.onlySgs(SgsTestLine.copyUsingJson(v)))));
        rows.addAll(notMatchCustomerTestLineList);
        rows.addAll(notMatchSgsTestLineList);
        // CustomerTestLine和 TrfTestLineDTO都实现了 TestMappingKey
        return rows;
    }
}
