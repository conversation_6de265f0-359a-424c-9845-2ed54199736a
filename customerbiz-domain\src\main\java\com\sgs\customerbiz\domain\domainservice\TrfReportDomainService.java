package com.sgs.customerbiz.domain.domainservice;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfReportDeliveryInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfReportMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.domainobject.v2.TrfReportDOV2;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.enums.DeliveryFlagEnum;
import com.sgs.customerbiz.model.trf.enums.ResendFlagEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.otsnotes.facade.model.enums.ActiveIndicatorEnum;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;

@Service
@Slf4j
public class TrfReportDomainService {
    @Resource
    private IdService idService;

    @Resource
    private TrfReportMapper trfReportMapper;

    @Resource
    private TrfReportDeliveryInfoMapper trfReportDeliveryInfoMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private TrfInfoMapper trfInfoMapper;

    public boolean batchCancel(List<TrfReportDOV2> reportList) {
        // 1.根据trfID查找report list
        List<String> reportNoList = reportList.stream().map(TrfReportDOV2::getReportNo).collect(Collectors.toList());
        List<TrfReportPO> dbTrfReportList = this.selectByTrfIdAndReportNo(null, reportNoList);
        if (Func.isEmpty(dbTrfReportList)) {
            dbTrfReportList = new ArrayList<>();
        }
        dbTrfReportList = dbTrfReportList.stream().filter(trfReport -> !Objects.equals(trfReport.getReportStatus(), ReportStatus.Cancelled.getCode())).collect(Collectors.toList());
        if (dbTrfReportList.isEmpty() || Func.isEmpty(dbTrfReportList)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFREPORTDOMAIN, ErrorFunctionTypeEnum.GETINFO, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), String.format("not found report,reportNo:%s", reportNoList));
            //throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, String.format("not found report,reportNo:%s", reportNoList));
        }

        // 2.构建需要cancel的 TrfReportPO
        List<TrfReportPO> toCancelReportList = dbTrfReportList.stream().map(rpt -> {
            TrfReportPO trfReport = new TrfReportPO();
            trfReport.setId(rpt.getId());
            trfReport.setReportStatus(ReportStatus.Cancelled.getCode());
            trfReport.setModifiedDate(new Date());
            return trfReport;
        }).collect(Collectors.toList());
        ;

        // 3.批量cancel report
        return Boolean.TRUE.equals(transactionTemplate.execute(trans -> {
            int updateCount = trfReportMapper.batchUpdate(toCancelReportList);
            if (updateCount < reportList.size()) {
                trans.setRollbackOnly();
                return false;
            }
            return true;
        }));
    }

    public String getRootReportNo(String reportNo, String orderNo) {
        TrfReportExample byReportNo = new TrfReportExample();
        byReportNo.createCriteria().andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> reportListByReportNo = trfReportMapper.selectByExample(byReportNo);
        if(Func.isEmpty(reportListByReportNo)) {
            return null;
        }
        String orderNoByReportNo = reportListByReportNo.stream().findFirst().map(TrfReportPO::getOrderNo).orElse(orderNo);

        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria().andOrderNoEqualTo(orderNoByReportNo).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        for (TrfReportPO trfReportPO : trfReportPOS) {
            map.put(trfReportPO.getReportNo(),trfReportPO.getOriginReportNo());
        }
        if (Func.isEmpty(map)) {
            return null;
        }

        while (true) {
            String value = map.get(reportNo);
            if (Func.isEmpty(value) || Objects.equals(reportNo, value)) {
                return reportNo;
            }
            reportNo = value;
        }
    }
    public List<TrfReportPO> selectByReportNoList(List<String> reportNoList) {
        if (Func.isEmpty(reportNoList)) {
            return null;
        }
        TrfReportExample example = new TrfReportExample();
        example.createCriteria().andReportNoIn(reportNoList);
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(example);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        return trfReportPOS;
    }

    public List<TrfReportPO> selectByActiveReportNoList(List<String> reportNoList, Long trfId) {
        if (Func.isEmpty(reportNoList)) {
            return null;
        }
        TrfReportExample example = new TrfReportExample();
        example.createCriteria().andReportNoIn(reportNoList).andTrfIdEqualTo(trfId).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(example);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        return trfReportPOS;
    }

    public Boolean isSend(String reportNo,Integer subscriber) {
        TrfReportDeliveryInfoExample reportDeliveryInfoExample = new TrfReportDeliveryInfoExample();
        reportDeliveryInfoExample.createCriteria().andSubscriberEqualTo(subscriber).andReportNoEqualTo(reportNo).andDeliveryFlagNotEqualTo(DeliveryFlagEnum.NEW.getCode());
        int i = trfReportDeliveryInfoMapper.countByExample(reportDeliveryInfoExample);
        return !Objects.equals(i, 0);
    }

    public List<TrfHeaderDTO> selectTrfByReportNo(String reportNo, Integer systemId) {
        if (Func.isBlank(reportNo) || Func.isEmpty(systemId)) {
            return null;
        }

        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria().andReportNoEqualTo(reportNo).andSystemIdEqualTo(systemId).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        List<Long> trfIds = trfReportPOS.stream().map(TrfReportPO::getTrfId).collect(Collectors.toList());
        if (Func.isEmpty(trfIds)) {
            return null;
        }

        List<TrfInfoPO> list = trfDomainService.selectByTrfIds(trfIds);
        if (Func.isEmpty(list)) {
            return null;
        }
        return list.stream().map(l -> {
            TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
            trfHeaderDTO.setTrfNo(l.getTrfNo());
            trfHeaderDTO.setRefSystemId(l.getRefSystemId());
            return trfHeaderDTO;
        }).collect(Collectors.toList());
    }

    public void save(TrfReportDOV2 trfReportDOV2) {
        Assert.notNull(trfReportDOV2);
        Assert.notNull(trfReportDOV2.getTrfId());

        List<String> reportNoList = new ArrayList<>();
        reportNoList.add(trfReportDOV2.getReportNo());
        List<TrfReportPO> trfReportPOList = selectByTrfIdAndReportNo(trfReportDOV2.getTrfId(), reportNoList);

        //如果TrfId + reportNo 不存在重复，直接保存新纪录
        if (CollectionUtil.isEmpty(trfReportPOList)) {
            TrfReportPO newTrfReport = buildNewTrfReport(trfReportDOV2);
            ;
            trfReportMapper.insert(newTrfReport);
            return;
        }

        //如果存在，则做更新处理
        if (trfReportPOList.size() != 1) {
            log.error("TrfId={} and reportNo={}  找到多条TrfReport记录 ", trfReportDOV2.getTrfId(), trfReportDOV2.getReportNo());
            return;
        }

        TrfReportPO trfReportPO = trfReportPOList.get(0);
        //当前只允许更新状态 @20231109
        trfReportPO.setReportStatus(trfReportDOV2.getReportStatus());
        trfReportPO.setReportDueDate(trfReportDOV2.getReportDueDate());
        trfReportPO.setOriginReportNo(trfReportDOV2.getOriginalReportNo());

        trfReportMapper.updateByPrimaryKey(trfReportPO);
    }

    private TrfReportPO buildNewTrfReport(TrfReportDOV2 trfReportDOV2) {
        Date now = DateUtil.now();

        TrfReportPO trfReportPO = new TrfReportPO();
        trfReportPO.setId(idService.nextId());
        trfReportPO.setTrfId(trfReportDOV2.getTrfId());
        trfReportPO.setSystemId(trfReportDOV2.getSystemId());
        trfReportPO.setOrderId(trfReportDOV2.getOrderId());
        trfReportPO.setOrderNo(trfReportDOV2.getOrderNo());
        trfReportPO.setReportId(trfReportDOV2.getReportId());
        trfReportPO.setReportNo(trfReportDOV2.getReportNo());
        trfReportPO.setReportStatus(trfReportDOV2.getReportStatus());
        trfReportPO.setOriginReportNo(trfReportDOV2.getOriginalReportNo());
        trfReportPO.setReportDueDate(trfReportDOV2.getReportDueDate());
        trfReportPO.setDeliveryFlag(DeliveryFlagEnum.NEW.getCode());
        trfReportPO.setActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
        trfReportPO.setCreatedBy(USER_DEFAULT);
        trfReportPO.setCreatedDate(now);
        trfReportPO.setModifiedBy(USER_DEFAULT);
        trfReportPO.setModifiedDate(now);
        trfReportPO.setCustomerConfirmStatus(trfReportDOV2.getCustomerConfirmStatus());

        return trfReportPO;
    }

    public List<TrfReportPO> selectByReportNosAndActive(List<String> reportNos) {
        TrfReportExample example = new TrfReportExample();
        example.createCriteria().andReportNoIn(reportNos).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfReportMapper.selectByExample(example);
    }

    public TrfReportPO selectByReportNo(String reportNo) {
        return selectByReportNo(reportNo,null);
    }

    public TrfReportPO selectByReportNo(String reportNo,Integer refSystemId) {
        TrfReportExample example = new TrfReportExample();
        example.createCriteria().andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(example);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        return trfReportPOS.get(0);
    }

    private static final List<Integer> activeReportStatus = ImmutableList.of(
            ReportStatus.New.getCode(),
            ReportStatus.Approved.getCode(),
            ReportStatus.Draft.getCode(),
            ReportStatus.Combined.getCode(),
            ReportStatus.Completed.getCode()
    );

    public Optional<TrfReportPO> selectActiveOneByTrfIdAndReportNo(Long trfId, String reportNo) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                .andReportStatusIn(activeReportStatus);
        return Optional.ofNullable(trfReportMapper.selectByExample(trfReportExample))
                .filter(rs -> rs.size() == 1)
                .flatMap(rs -> rs.stream().findFirst());
    }

    public List<TrfReportPO> selectByTrfIdAndActive(Long trfId) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                .andReportStatusIn(activeReportStatus);
        return trfReportMapper.selectByExample(trfReportExample);
    }


    public List<TrfReportPO> selectByTrfId(Long trfId) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                .andReportStatusNotEqualTo(ReportStatus.Cancelled.getCode());
        return trfReportMapper.selectByExample(trfReportExample);
    }


    public List<TrfReportPO> selectByTrfId(Long trfId, Boolean flag) {
        TrfReportExample trfReportExample = new TrfReportExample();
        TrfReportExample.Criteria criteria = trfReportExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        if (!flag) {
            criteria.andReportStatusNotEqualTo(ReportStatus.Cancelled.getCode());
        }
        return trfReportMapper.selectByExample(trfReportExample);
    }

    public Boolean reportIsSuccess(Integer refSystemId, String reportNo) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
        if (Func.isEmpty(trfReportPOS)) {
            return false;
        }
        TrfReportPO trfReportPO = trfReportPOS.get(0);
        TrfReportDeliveryInfoExample flagExample = new TrfReportDeliveryInfoExample();
        flagExample.createCriteria()
                        .andReportNoEqualTo(trfReportPO.getReportNo())
                                .andSubscriberEqualTo(refSystemId);
        List<TrfReportDeliveryInfoPO> deliveryInfoList = trfReportDeliveryInfoMapper.selectByExample(flagExample);
        if(Func.isEmpty(deliveryInfoList)) {
            return false;
        }
        Integer deliveryFlag = deliveryInfoList.get(0).getDeliveryFlag();
        Integer resendFlag = deliveryInfoList.get(0).getResendFlag();
        return !Objects.equals(deliveryFlag, DeliveryFlagEnum.NEW.getCode())
                && !Objects.equals(deliveryFlag, DeliveryFlagEnum.FAIL.getCode())
                && !Objects.equals(resendFlag, ResendFlagEnum.Yes.getCode())
                && !Func.isEmpty(deliveryFlag);
    }

    public List<TrfReportPO> selectByTrfIdAndReportNo(Long trfId, List<String> reportNoList) {
        TrfReportExample trfReportExample = new TrfReportExample();
        TrfReportExample.Criteria criteria = trfReportExample.createCriteria();
        if (Func.isNotEmpty(trfId)) {
            criteria.andTrfIdEqualTo(trfId);
        }
        criteria
                .andReportNoIn(reportNoList)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
        ;
        return trfReportMapper.selectByExample(trfReportExample);
    }

    public List<TrfReportPO> selectBySystemIdAndOrderNoAndReportNo(Integer systemId, String orderNo, String reportNo) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andSystemIdEqualTo(systemId)
                .andOrderNoEqualTo(orderNo)
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
        ;
        return trfReportMapper.selectByExample(trfReportExample);
    }

    public List<TrfReportPO> selectByOrderNo(Integer systemId,String orderNo) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria().andSystemIdEqualTo(systemId).andOrderNoEqualTo(orderNo).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfReportMapper.selectByExample(trfReportExample);
    }

    public List<TrfReportPO> getActiveReportListByTrfId(Long trfId) {
        TrfReportExample reportExample = new TrfReportExample();
        reportExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andReportStatusNotIn(Arrays.asList(ReportStatus.Cancelled.getCode(), ReportStatus.Reworked.getCode()))
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfReportMapper.selectByExample(reportExample);
    }

    public List<TrfReportDeliveryInfoPO> getTrfReportDeliveryInfoListBy(Long apiId, List<String> reportNoList) {
        TrfReportDeliveryInfoExample example = new TrfReportDeliveryInfoExample();
        example.createCriteria()
                .andApiIdEqualTo(apiId)
                .andReportNoIn(reportNoList);
        return trfReportDeliveryInfoMapper.selectByExample(example);
    }

    /**
     * 给UpdateInfo使用
     * @param reportNo
     */
    public void updateTrfReportDeliveryFlag(List<String> reportNo) {
        TrfReportDeliveryInfoExample example = new TrfReportDeliveryInfoExample();
        example.createCriteria().andReportNoIn(reportNo);
        List<TrfReportDeliveryInfoPO> trfReportDeliveryInfoPOS = trfReportDeliveryInfoMapper.selectByExample(example);
        if (Func.isEmpty(trfReportDeliveryInfoPOS)) {
            return;
        }
        trfReportDeliveryInfoPOS.forEach(l->l.setResendFlag(ResendFlagEnum.Yes.getCode()));
        trfReportDeliveryInfoMapper.batchUpdate(trfReportDeliveryInfoPOS);
    }

    /**
     * 给syncReviewConclusion使用
     * 给Modify使用
     * @param reportNo
     * @param refSystemId
     */
    public void updateTrfReportDeliveryFlag(List<String> reportNo, Integer refSystemId) {
        TrfReportDeliveryInfoExample example = new TrfReportDeliveryInfoExample();
        example.createCriteria().andSubscriberEqualTo(refSystemId).andReportNoIn(reportNo);
        List<TrfReportDeliveryInfoPO> trfReportDeliveryInfoPOS = trfReportDeliveryInfoMapper.selectByExample(example);
        if (Func.isEmpty(trfReportDeliveryInfoPOS)) {
            return;
        }
        trfReportDeliveryInfoPOS.forEach(l->l.setResendFlag(ResendFlagEnum.Yes.getCode()));
        trfReportDeliveryInfoMapper.batchUpdate(trfReportDeliveryInfoPOS);
    }

    /**
     * 更新report
     *
     * @param trfId
     * @param flag
     */
    @Deprecated
    public void updateTrfReportDeliveryFlag(Long trfId, Integer flag) {
        TrfReportExample example = new TrfReportExample();
        example.createCriteria().andTrfIdEqualTo(trfId);
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(example);
        if (Func.isEmpty(trfReportPOS)) {
            return;
        }
        trfReportPOS.forEach(l->l.setDeliveryFlag(flag));
        trfReportMapper.batchUpdate(trfReportPOS);
    }


    public void saveTrfReportDeliveryInfo(TrfReportDeliveryInfoPO trfReportDeliveryInfoPO) {
        Assert.notNull(trfReportDeliveryInfoPO, "trfReportDeliveryInfoPO must not be null");
        trfReportDeliveryInfoMapper.insert(trfReportDeliveryInfoPO);
    }

    /**
     * 给callback回传成功，失败和被条件阻挡使用
     * @param apiId
     * @param reportNo
     * @param flag
     * @return
     */
    public Optional<TrfReportDeliveryInfoPO> updateTrfReportDeliveryFlagAfterCallback(Long apiId, String reportNo, Integer flag) {
        Assert.notNull(apiId, "subscriberId must not be null");
        Assert.notNull(flag, "flag must not be null");
        Assert.notBlank(reportNo, "reportNo must not be blank");
        Optional<TrfReportDeliveryInfoPO> deliveryInfoOption = Optional.ofNullable(
                trfReportDeliveryInfoMapper.selectByPrimaryKey(reportNo, apiId)
        );
        if (deliveryInfoOption.isPresent()) {
            TrfReportDeliveryInfoPO deliveryInfoPO = deliveryInfoOption.get();
            deliveryInfoPO.setDeliveryFlag(flag);
            deliveryInfoPO.setResendFlag(ResendFlagEnum.No.getCode());
            Date now = new Date();
            deliveryInfoPO.setCreateDate(now);
            deliveryInfoPO.setUpdateDate(now);
            trfReportDeliveryInfoMapper.updateByPrimaryKeySelective(deliveryInfoPO);
        }
        return deliveryInfoOption;
    }

    /**
     * 给重复发送使用
     * @param apiId
     * @param reportNo
     * @param flag
     * @return
     */
    public Optional<TrfReportDeliveryInfoPO> updateTrfReportResendFlag(Long apiId, String reportNo) {
        Assert.notNull(apiId, "subscriberId must not be null");
        Assert.notBlank(reportNo, "reportNo must not be blank");
        Optional<TrfReportDeliveryInfoPO> deliveryInfoOption = Optional.ofNullable(
                trfReportDeliveryInfoMapper.selectByPrimaryKey(reportNo, apiId)
        );
        if (deliveryInfoOption.isPresent()) {
            TrfReportDeliveryInfoPO deliveryInfoPO = deliveryInfoOption.get();
            deliveryInfoPO.setResendFlag(ResendFlagEnum.No.getCode());
            trfReportDeliveryInfoMapper.updateByPrimaryKeySelective(deliveryInfoPO);
        }
        return deliveryInfoOption;
    }

    public Integer getSystemIdByReportNo(String reportNo) {
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria()
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(trfReportExample);
        TrfReportPO trfReportPO = CollUtil.get(trfReportPOS, 0);
        return Func.isEmpty(trfReportPO) ? null : trfReportPO.getSystemId();
    }

    public TrfReportPO selectById(Long trfReportId) {
        return trfReportMapper.selectByPrimaryKey(trfReportId);
    }


    public void updateTrfReport(TrfReportPO trfReportPO) {
        if (Func.isEmpty(trfReportPO)) {
            return;
        }
        trfReportPO.setModifiedDate(new Date());
        trfReportMapper.updateByPrimaryKey(trfReportPO);
    }

    public void updateReportAsInvalid(List<String> trfNos, Integer refSystemId, String orderNo) {
        if (Func.isEmpty(trfNos) || Func.isEmpty(refSystemId)) {
            return;
        }
        trfReportMapper.updateReportAsInvalid(trfNos, refSystemId, orderNo);
    }

    public List<TrfReportPO> selectByTrfNo(String trfNo, int refSystemId) {
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria().andTrfNoEqualTo(trfNo).andRefSystemIdEqualTo(refSystemId).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> list = trfInfoMapper.selectByExample(trfInfoExample);
        if (Func.isEmpty(list)) {
            return null;
        }
        TrfInfoPO trfInfoPO = list.get(0);
        TrfReportExample trfReportExample = new TrfReportExample();
        trfReportExample.createCriteria().andTrfIdEqualTo(trfInfoPO.getId()).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        return trfReportMapper.selectByExample(trfReportExample);
    }

    public List<TrfReportDTO> updateReportList(List<TrfReportDTO> reportList, Integer labId) {
        List<String> reportNoList = reportList.stream().map(TrfReportDTO::getReportNo).collect(Collectors.toList());
        TrfReportExample trfReportExample = new TrfReportExample();
        TrfReportExample.Criteria criteria = trfReportExample.createCriteria();
        criteria.andReportNoIn(reportNoList).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).andReportStatusNotEqualTo(ReportStatus.Cancelled.getCode());
//        if (Func.isNotEmpty(labId)) {
//            criteria.andLabIdEqualTo(labId);
//        }
        List<TrfReportPO> reportPOS = trfReportMapper.selectByExample(trfReportExample);
        if (Func.isEmpty(reportPOS)) {
            return null;
        }

        List<Long> trfIds = reportPOS.stream().map(TrfReportPO::getTrfId).distinct().collect(Collectors.toList());

        List<TrfInfoPO> list = trfDomainService.selectByTrfIdsActive(trfIds);
        ArrayListMultimap<Long, TrfInfoPO> trfMap = ArrayListMultimap.create();

        if (Func.isNotEmpty(list)) {
            list.forEach(
                    l -> trfMap.put(l.getId(), l)
            );
        }


        Map<String, TrfReportDTO> map = reportList.stream().collect(Collectors.toMap(TrfReportDTO::getReportNo, Function.identity()));

        Map<String, Long> trfReportRelMap = new HashMap<>();

        Date date = new Date();
        reportPOS.forEach(l -> {
            trfReportRelMap.put(l.getReportNo(), l.getTrfId());
            TrfReportDTO trfReportDTO = map.get(l.getReportNo());
            if (Func.isNotEmpty(trfReportDTO)) {
                TrfConclusionDTO conclusion = trfReportDTO.getConclusion();
                l.setComments(conclusion.getComments());
                l.setReviewConclusion(conclusion.getReviewConclusion());
            }
            l.setModifiedDate(date);
        });

        reportList.forEach(l -> {
            l.setReportNo(StringUtil.trimWhitespace(l.getReportNo()));
            map.put(l.getReportNo(), l);
            Long aLong = trfReportRelMap.get(l.getReportNo());
            if (Func.isNotEmpty(aLong) && Func.isNotEmpty(trfMap.get(aLong))) {
                List<TrfInfoPO> trfInfoPOS = trfMap.get(aLong);
                List<TrfReferenceDTO> trfList = new ArrayList<>();
                TrfLabDTO labDTO = l.getLab();
                if (Func.isEmpty(labDTO)) {
                    labDTO = new TrfLabDTO();
                }

                for (TrfInfoPO trfInfoPO : trfInfoPOS) {
                    labDTO.setLabCode(trfInfoPO.getLabCode());
                    labDTO.setLabId(Long.valueOf(trfInfoPO.getLabId()));

                    TrfReferenceDTO trf = new TrfReferenceDTO();
                    trf.setTrfNo(trfInfoPO.getTrfNo());
                    trf.setRefSystemId(trfInfoPO.getRefSystemId());
                    trfList.add(trf);
                }

                l.setSystemId(Long.valueOf(reportPOS.get(0).getSystemId()));
                l.setLab(labDTO);
                l.setTrfList(trfList);
            }
        });

        trfReportMapper.batchUpdate(reportPOS);


        return reportList;
    }

    public List<String> fullTestReportNo(String trfNo, Integer refSystemId) {
        TrfInfoPO trfInfoByTrfNo = trfDomainService.getTrfInfoByTrfNo(trfNo, refSystemId);

        TrfReportExample reportExample = new TrfReportExample();
        reportExample.createCriteria().andTrfIdEqualTo(trfInfoByTrfNo.getId()).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).andDeliveryFlagNotEqualTo(DeliveryFlagEnum.NEW.getCode());
        List<TrfReportPO> trfReportPOS = trfReportMapper.selectByExample(reportExample);
        if (Func.isEmpty(trfReportPOS)) {
            return null;
        }
        return trfReportPOS.stream().map(TrfReportPO::getReportNo).distinct().collect(Collectors.toList());
    }
}
