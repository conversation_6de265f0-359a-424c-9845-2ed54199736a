package com.sgs.customerbiz.biz.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.service.inspectorio.InspectorioDataBizService;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SyncLululemonTestLineInfoXXLJobScheduler {

    private final InspectorioDataBizService inspectorioDataBizService;

    @Data
    public static class SyncLululemonTestLineInfoXXLJobParam {
        private Integer sizeOfApi = 10;
        private Integer sizeOfDb = 10;
        private Integer maxSizeOfApi = Integer.MAX_VALUE;
    }

    @XxlJob("syncLululemonTestLineInfoHandler")
    public void syncLululemonTestLineInfoHandler() {
        JobLogUtil.info(log, "start sync lululemon test line info handler on " + JobLogUtil.now());

        try {
            String jobParam = XxlJobHelper.getJobParam();
            InspectorioDataBizService.InspectorioSyncParam param;
            
            if (jobParam != null && !jobParam.trim().isEmpty()) {
                SyncLululemonTestLineInfoXXLJobParam jobParamObj = JSON.parseObject(jobParam, SyncLululemonTestLineInfoXXLJobParam.class);
                JobLogUtil.info(log, "param from job : {}", jobParamObj);
                
                param = new InspectorioDataBizService.InspectorioSyncParam();
                param.setSizeOfApi(jobParamObj.getSizeOfApi());
                param.setSizeOfDb(jobParamObj.getSizeOfDb());
                param.setMaxSizeOfApi(jobParamObj.getMaxSizeOfApi());
            } else {
                // 使用默认参数
                param = new InspectorioDataBizService.InspectorioSyncParam();
                param.setSizeOfApi(10);
                param.setSizeOfDb(10);
                param.setMaxSizeOfApi(Integer.MAX_VALUE);
                JobLogUtil.info(log, "using default param: {}", param);
            }

            inspectorioDataBizService.syncLululemonTestLineInfo(param);
        } catch (Throwable t) {
            JobLogUtil.error(log, t, "sync lululemon test line info handler error");
            XxlJobHelper.handleFail(t.getMessage());
        }
        JobLogUtil.info(log, "success sync lululemon test line info handler on " + JobLogUtil.now());
    }
} 