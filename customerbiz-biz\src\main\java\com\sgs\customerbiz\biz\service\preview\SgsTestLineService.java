package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingKey;
import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingTestLineLike;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.framework.core.base.CustomResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SgsTestLineService {

    private final LocalILayerClient iLayerClient;

    public SgsTestLineService(LocalILayerClient iLayerClient) {
        this.iLayerClient = iLayerClient;
    }

    public List<SgsTestLine> testLines(String customerGroupCode, String productLineCode, Collection<TrfTestLineDTO> testLineList) {
        CheckTestLineMappingReq req = new CheckTestLineMappingReq();
        req.setCustomerGroupCode(customerGroupCode);
        req.setProductLineCode(productLineCode);
        List<MappingTestLineReq> testLineRequests = testLineList.stream()
                .map(this::convertToMappingTestLineReq).collect(Collectors.toList());
        req.setTestLines(testLineRequests);
        CustomResult<List<TestLineMappingInfoV2DTO>> customResult = iLayerClient.queryTestLineMappingExists(req, TestLineMappingInfoV2DTO.class);
        List<TestLineMappingInfoV2DTO> testLineMappingInfoV2DTOS = Optional.ofNullable(customResult.getData())
                .orElseThrow(() -> new IllegalStateException("Querying testLineMapping from ILayer failed. "));
        Map<String, TestLineMappingInfoV2DTO> mappingMap = testLineMappingInfoV2DTOS.stream()
                .filter(mapping -> StringUtils.isNotBlank(mapping.getItemCode()))
                .collect(Collectors.toMap(TestMappingKey::asKey, Function.identity(), (v1, v2) -> v1));
        return testLineList.stream()
                .map(testLine -> new SgsTestLine(testLine, mappingMap.get(testLine.asKey())))
                .collect(Collectors.toList());
    }

    private MappingTestLineReq convertToMappingTestLineReq(TestMappingTestLineLike testLine) {
        MappingTestLineReq testLineReq = new MappingTestLineReq();
        testLineReq.setPpNo(testLine.getPpNo());
        testLineReq.setTestLineId(testLine.getTestLineId());
        testLineReq.setCitationId(testLine.getCitationId());
        testLineReq.setCitationType(testLine.getCitationType());
        return testLineReq;
    }

}
