package com.sgs.customerbiz.domain.domainobject.v2;

import com.sgs.customerbiz.model.trf.enums.CustomerConfirmReportEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class TrfReportDOV2 implements Serializable {

    private Long id;

    private Long trfId;

    private Integer systemId;

    private String orderId;

    private String orderNo;

    private Integer reportStatus;

    private String originalReportNo;

    private String rootReportNo;
    private String oldReportNo;
    private String reportVersion;
    private String rslstatus;
    private String failCode;

    private Integer deliveryFlag;

    private String reportId;

    private String reportNo;
    private String testMatrixMergeMode;
    private String reportRemark;

    // add 231115
    private Date reportDueDate;

    private Integer labId;

    // add 231108  - - ReportDO 中不应该有trflist
    private List<TrfReferenceDO> trfList;

    private CustomerConfirmReportEnum customerConfirmStatus;

}
