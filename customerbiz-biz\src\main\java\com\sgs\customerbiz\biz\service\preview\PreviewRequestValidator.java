package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import io.vavr.control.Option;
import io.vavr.control.Validation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PreviewRequestValidator {

    private final ConfigClient configClient;


    public PreviewRequestValidator(ConfigClient configClient) {
        this.configClient = configClient;
    }

    public Validation<String, TrfFullDTO> validate(TrfFullDTO request) {
        Option<Integer> refSystemIdOpt = Option.of(request)
                .map(TrfFullDTO::getTrfList)
                .filter(CollectionUtils::isNotEmpty)
                .filter(trfList -> trfList.stream().map(TrfHeaderDTO::getRefSystemId).allMatch(Objects::nonNull))
                .filter(trfList -> trfList.stream().map(TrfHeaderDTO::getRefSystemId).distinct().count() == 1)
                .map(trfList -> trfList.get(0).getRefSystemId());
        if (refSystemIdOpt.isEmpty()) {
            return Validation.invalid("refSystemId of request is required and should be same!");
        }
        Option<String> productLineCodeOpt = Option.of(request.getBuCode()).filter(StringUtils::isNotBlank);
        if (productLineCodeOpt.isEmpty()) {
            return Validation.invalid("productLineCode of request is required!");
        }
        Option<List<String>> trfNoListOpt = Option.of(request).map(TrfFullDTO::getTrfList)
                .filter(CollectionUtils::isNotEmpty)
                .filter(trfList -> trfList.stream().allMatch(Objects::nonNull))
                .map(trfList -> trfList.stream().map(TrfHeaderDTO::getTrfNo).collect(Collectors.toList()))
                .filter(trfNoList -> trfNoList.stream().allMatch(StringUtils::isNotBlank));
        if (trfNoListOpt.isEmpty()) {
            return Validation.invalid("trfNo of request is required!");
        }
        Option<List<TrfTestLineDTO>> testLineListOpt = Option.of(request)
                .map(TrfFullDTO::getTestLineList)
                .filter(CollectionUtils::isNotEmpty)
                .filter(testLineList -> testLineList.stream().allMatch(Objects::nonNull));

        if (testLineListOpt.isEmpty()) {
            return Validation.invalid("testLineList of request is required!");
        }

        return Validation.valid(request);
    }


}
