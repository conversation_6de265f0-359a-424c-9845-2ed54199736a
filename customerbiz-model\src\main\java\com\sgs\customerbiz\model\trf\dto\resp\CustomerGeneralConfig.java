package com.sgs.customerbiz.model.trf.dto.resp;

import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.model.trf.enums.TrfImportMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/3/29 14:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerGeneralConfig {

    public static final String FEATURE_REJECT_IMPORT_FOR_DIFF_LAB_CODE = "rejectImportForDiffLabCode";
    public static final String FEATURE_INTEGRATION_SGSMART = "integrationSGSMart";
    public static final String FEATURE_USING_SGSTRF_TO_ORDER = "usingSgsTrfToOrder";
    public static final String FEATURE_REQUIRED_CUSTOMER_CONFIRM_REPORT = "requiredCustomerConfirmReport";

    public static final Set<String> DEFAULT_SUPPORT_IMPORT_MODES = ImmutableSet.of(TrfImportMode.TRF_IMPORT_MODE_PULL);

    private String integrationSystemName;

    private Integer integrationSystemRefSystemId;

    private String customerNameEn;

    private String customerNameCn;
    private String customerNo;
    private String customerGroupCode;
    private String customerBu;
    private String integrationLevel;
    private String customerLanguage;
    private String currency;
    private String integrationCustomerSystem;
    private List<String> source;
    private Set<String> supportImportModes = DEFAULT_SUPPORT_IMPORT_MODES;
    private String mockData;

    private List<DefaultCustomer> defaultCustomer;

    private List<CustomerDataEntryModeConfig> dataEntryModel;

    private String updateTrf;

    private RecipientConfig reviewConclusionRecipient;

    private CustomerValidationConfig validation;

    private String customerFilterFieldMapping;

    private Emails emails;

    private String rejectRevisedOnLightMode;

    /**
     * @option integrationSGSMart:"yes","y","1","true","on"
     * @option usingSgsTrfToOrder:"yes","y","1","true","on"
     * @option requiredCustomerConfirmReport:"yes","y","1","true","on"
     * @since requiredCustomerConfirmReport SCI-1645
     */
    private Map<String, String> features;

    private static final Set<String> enabledStrs = ImmutableSet.of(
            "yes","y","1","true","on"
    );

    public boolean rejectImportForDiffLabCode() {
        return featureEnabled(FEATURE_REJECT_IMPORT_FOR_DIFF_LAB_CODE);
    }

    public boolean isIntegratedSGSMart() {
        return featureEnabled(FEATURE_INTEGRATION_SGSMART);
    }

    public boolean usingSGSTrfToOrder() {
        return featureEnabled(FEATURE_USING_SGSTRF_TO_ORDER);
    }

    public boolean requiredCustomerConfirmReport() {
        return featureEnabled(FEATURE_REQUIRED_CUSTOMER_CONFIRM_REPORT);
    }

    public boolean featureEnabled(String feature) {
        boolean hasIntegrationSGSMart = Objects.nonNull(features) && features.containsKey(feature);
        return hasIntegrationSGSMart && enabledStrs.contains(features.getOrDefault(feature, "").trim().toLowerCase());
    }

    @Data
    public static class Emails{
        private EmailNode reviewConclusion;
        private EmailNode inspectorioSync;
    }

    @Data
    public static class EmailNode {
        private String mailSubject;
        private String mailText;
        private Map<String, List<String>> mailTo;
        private Set<String> mailCc;
        private boolean hasAttachment;
        private String script;
        private Set<String> defaultMailTo;
    }

    @Data
    public static class DefaultCustomer {
        private Integer customerUsage;

        private Long bossNo;

        private String customerName;

        private String customerAddress;

        private String contactName;

        private String contactEmail;

        private String contactTelephone;

        private Integer language;

    }
}
