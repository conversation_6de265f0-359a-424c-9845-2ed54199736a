package com.sgs.customerbiz.biz.event.handler.converter.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.event.handler.converter.DataConvertStrategy;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfPushEvent;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * TRF推送事件转换策略
 */
@Order(2)
@Component
public class TrfPushConvertStrategy implements DataConvertStrategy {

    @Resource
    private DataConvertor<String, String, JSON> jsonDataConvertor;

    @Override
    public boolean supports(ObjectEvent event, EventSubscribeDTO subscribe) {
        return event instanceof TrfPushEvent;
    }

    @Override
    public Object convert(ObjectEvent event, EventSubscribeDTO subscribe, 
            CollectedData collectedData, SystemApiDTO systemApi) {
        Object payload = event.getPayload();
        //since SCI-1743
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(payload));
        jsonObject.put("extra", collectedData.getExtraData());
        return jsonDataConvertor.convert(jsonObject.toJSONString(),
            systemApi.getRequestBodyTemplate());
    }
} 