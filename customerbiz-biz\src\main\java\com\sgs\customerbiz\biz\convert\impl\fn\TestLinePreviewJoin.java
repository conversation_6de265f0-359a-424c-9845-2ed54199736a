package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

@Component
public class TestLinePreviewJoin extends StringOperationFn {
    @Override
    public Object invoke(Object arg1, Object arg2) {
        if (arg1 ==null || arg2 == null) {
            return null;
        }

        String arg1Str = arg1.toString();
        String arg2Str = arg2.toString();

        if (Func.isEmpty(arg1Str) || Func.isEmpty(arg2Str)) {
            return null;
        }
        return arg1Str + "（" + arg2Str + "）";
    }

    @Override
    public String getName() {
        return "testLinePreviewJoin";
    }

    @Override
    public String desc() {
        return "testLinePreviewJoin";
    }
}