# Import2TRF

## 流程图

```mermaid
graph TD
    %% 主流程
    Main[Import2TRF_Main] --> ValidateComponent[importToTRFValidateComponent]
    ValidateComponent --> ModelRouter[importTRFModelRouterComponent]
    ModelRouter -->|PUSH| PushFlow[SubChain_PushImport2TRF]
    ModelRouter -->|PULL| PullFlow[SubChain_PullImportTRF]
    ModelRouter -->|default| DefaultFlow[Import2TRF]

    %% PUSH模式子流程
    PushFlow --> PushGetCustomer[pushImportGetCustomerActionComponent]
    PushGetCustomer --> PushConvert[importToTRFConvertComponent]
    PushConvert --> PushPreHandle[importToTRFPreHandleComponent]
    PushPreHandle --> PushMapping[testLineMappingComponent]
    PushMapping --> PushAfterConvert[importToTRFAfterConvertComponent]
    PushAfterConvert --> PushCreate[pushImportToTRFCreateActionComponent]
    PushCreate --> PushSaveDB[pushImportToTrfSaveDBComponent]
    PushSaveDB --> PushNotify[pushEventNotifyActionComponent]

    %% PULL模式子流程路由
    PullFlow --> PullRouter[importToTRFRouterComponent]
    PullRouter -->|PULL| PullSubFlow[PullImport2TRF]
    PullRouter -->|PULL_2| SmartFlow[SmartImport2TRF]
    PullRouter -->|default| PullSubFlow

    %% PULL模式子流程
    PullSubFlow --> GetCustomer[getCustomerTRFComponent]
    GetCustomer --> PullConvert[pullImportTrfConvertComponent]
    PullConvert --> PullPreHandle[importToTRFPreHandleComponent]
    PullPreHandle --> PullMapping[testLineMappingComponent]
    PullMapping --> PullAfterConvert[importToTRFAfterConvertComponent]
    PullAfterConvert --> PullCreate[importToTRFCreateActionComponent]
    PullCreate --> PullSaveDB[importToTrfSaveDBComponent]
    PullSaveDB --> PullNotify[eventNotifyActionComponent]

    %% 默认模式子流程
    DefaultFlow --> DefaultGetCustomer[getCustomerTRFComponent]
    DefaultGetCustomer --> DefaultConvert[importToTRFConvertComponent]
    DefaultConvert --> DefaultPreHandle[importToTRFPreHandleComponent]
    DefaultPreHandle --> DefaultMapping[testLineMappingComponent]
    DefaultMapping --> DefaultAfterConvert[importToTRFAfterConvertComponent]
    DefaultAfterConvert --> DefaultCreate[importToTRFCreateActionComponent]
    DefaultCreate --> DefaultSaveDB[importToTrfSaveDBComponent]
    DefaultSaveDB --> DefaultNotify[eventNotifyActionComponent]

    %% SMART模式子流程
    SmartFlow --> SmartPreHandle[smartImportTRFPreHandleComponent]
    SmartPreHandle --> SmartValidate[smartImportTrfValidateComponent]
    SmartValidate --> SmartSaveDB[smartImportToTrfSaveDBComponent]
```



## 流程概述

Import2TRF流程是一个将客户TRF(Test Request Form)导入到系统的流程。根据flow.el.xml的定义，Import2TRF有多种模式：
- 主流程 `Import2TRF_Main`
- PUSH模式子流程 `SubChain_PushImport2TRF`
- PULL模式子流程 `SubChain_PullImportTRF`
- SMART模式子流程 `SmartImport2TRF`
- 默认模式子流程 `Import2TRF`

## importToTRFValidateComponent

### 调用的类和方法

- `SciTrfBizService.validateImportTrf(TrfImportReq importReq)`
  1. 验证导入TRF请求的参数
  2. 如果importReq.getImportMode()为空，则设置为TRF_IMPORT_MODE_PULL
  3. 调用dfvHelper.validateParams验证参数
  4. 返回refSystemId（参考系统ID）

## importTRFModelRouterComponent

### 调用的类和方法

- `TrfImportReq.getImportMode()`
  1. 获取导入模式
  2. 根据导入模式返回不同的路由标签（"PUSH"或"PULL"）
  3. 路由到不同的子流程

## getCustomerTRFComponent

### 调用的类和方法

- `CustomerTrfDomainService.getCustomerTrf(TrfImportReq importReq)`
  1. 根据TrfNo向指定系统refSystemId获取CustomerTrf(JSON)
  2. 使用ExtensionExecutor执行CustomerTrfImportActionExtPt接口的getCustomerTrf方法
  3. 根据refSystemId选择不同的实现类
  4. 验证importReq.getTrfNo()和importReq.getRefSystemId()不为空
  5. 构建BizScenario对象，使用refSystemId作为场景值
  6. 执行对应的扩展点实现

## pushImportGetCustomerActionComponent

### 调用的类和方法

- 继承自`GetCustomerTRFComponent`
- 重写了`getCustomerTrf`方法
- `Assert.notBlank(importReq.getCustomerTrf(), "customerTrf cannot be null in PUSH Mode !")`
  1. 验证PUSH模式下customerTrf不能为空
  2. 直接返回importReq.getCustomerTrf()，不需要从外部系统获取

## importToTRFConvertComponent

### 调用的类和方法

- `CustomerTrfDomainService.convert(String customerTrfJson, Integer refSystemId, Integer systemId, String labCode, String buCode, String trfTemplateId, String trfTemplateType, String formId, String gridId, TrfLabContactDTO labContact)`
  1. 将客户TRF JSON转换为SGS TRF结构
  2. 验证customerTrfJson和refSystemId不为空
  3. 获取导入场景
  4. 构建BizScenario对象
  5. 执行对应的CustomerTrfImportActionExtPt扩展点实现的convert方法
  6. 设置TRF来源为TrfToOrder

## pullImportTrfConvertComponent

### 调用的类和方法

- 继承自`ImportToTRFConvertComponent`
- `PullImportTrfActionExtPt.defaultLabInfoIfNull(TrfDTO sgsTRF)`
  1. 如果TRF的实验室信息为空，设置默认值

## importToTRFPreHandleComponent

### 调用的类和方法

- `DefaultImportTrfActionExtPt.initTrfLab(TrfDTO trfDTO, TrfImportReq importReq)`
  1. 初始化TRF的实验室信息
  2. 如果importReq中有labCode，则使用importReq中的labCode
  3. 如果importReq中没有labCode，则使用默认的labCode

## testLineMappingComponent

### 调用的类和方法

- `DefaultImportTrfActionExtPt.tlMapping(TrfDTO trfDTO, TrfImportReq importReq)`
  1. 将客户的测试线映射到SGS的测试线
  2. 根据配置获取测试线映射关系
  3. 应用映射关系到TRF的测试线

## importToTRFAfterConvertComponent

### 调用的类和方法

- `DefaultImportTrfActionExtPt.afterConvert(TrfDTO sgsTRF, TrfImportReq importReq)`
  1. 转换后的后置处理
  2. 根据refSystemId选择不同的处理逻辑
  3. 可能会调用afterConvertProcessor进行处理

## importToTRFCreateActionComponent

### 调用的类和方法

- `DefaultImportTrfActionExtPt.validateImportTrf(TrfDTO sgsTRF, TrfImportReq importReq)`
  1. 验证导入的TRF数据

- `DefaultImportTrfActionExtPt.preHandler(TrfDTO sgsTRF, TrfImportReq importReq)`
  1. 预处理TRF数据

- `TrfConvertor.toTrfDOV2(TrfDTO sgsTRF)`
  1. 将TrfDTO转换为TrfDOV2对象

- `DefaultImportTrfActionExtPt.beforeCreateTrf(TrfDOV2 trfParam, TrfDTO sgsTRF, TrfImportReq importReq)`
  1. 创建TRF前的处理
  2. 如果refSystemId是TARGET_INSPECTORIO，则调用sgsMartService.createSgsmartTrfByCustomerTrf

- `DefaultImportTrfActionExtPt.getTrfNewEvent(TrfDTO sgsTRF, TrfImportReq importReq)`
  1. 获取TRF创建事件
  2. 创建TrfNewEvent对象，设置触发者为CUSTOMER

## pushImportToTRFCreateActionComponent

### 调用的类和方法

- 继承自`ImportToTRFCreateActionComponent`
- 重写了`preHandler`、`beforeCreateTrf`和`validateImportTrf`方法
- `PushImportTrfActionExtPt.preHandler(TrfDTO sgsTRF, TrfImportReq importReq)`
  1. PUSH模式下的预处理

- `PushImportTrfActionExtPt.beforeCreateTrf(TrfDOV2 trfParam, TrfDTO sgsTRF, TrfImportReq importReq)`
  1. PUSH模式下创建TRF前的处理
  2. 检查refSystemId是否在usingSGSTrfNo集合中
  3. 如果是，则调用sgsMartService.createSgsmartTrfByCustomerTrf

## importToTrfSaveDBComponent

### 调用的类和方法

- `TrfDomainService.createTrf(TrfDOV2 trfParam)`
  1. 创建TRF
  2. 使用分布式锁确保同一个TRF不会被并发处理
  3. 在事务中执行createTrfImpl
  4. 事务提交后发送Kafka消息

- `DefaultImportTrfActionExtPt.saveTodolistTRF(TrfDTO sgsTRF, String customerTRFJson, TrfImportReq importReq, boolean needLabCode)`
  1. 保存TRF到待办列表
  2. 根据refSystemId选择不同的处理逻辑
  3. 调用todoListService.importTrfInfoData

## pushImportToTrfSaveDBComponent

### 调用的类和方法

- 继承自`ImportToTrfSaveDBComponent`
- 重写了`needLabCode()`方法，返回false

## eventNotifyActionComponent

### 调用的类和方法

- `ApplicationEventPublisher.publishEvent(Object event)`
  1. 发布事件
  2. 遍历requestContext.getTrfEventList()或requestContext.getTrfActionEventList()
  3. 对每个事件调用applicationEventPublisher.publishEvent

## pushEventNotifyActionComponent

### 调用的类和方法

- 继承自`EventNotifyActionComponent`
- `PushImportTrfActionExtPt.postImoprtExt(TrfDTO trfDTO, TrfImportReq importReq)`
  1. PUSH模式下导入后的处理
  2. 可能会发布TRF创建事件

## smartImportTRFPreHandleComponent

### 调用的类和方法

- `DefaultImportTrfActionExtPt.preHandler(TrfImportReq reqObject)`
  1. SMART模式下的预处理

## smartImportTrfValidateComponent

### 调用的类和方法

- `SGSMartImportTrfActionExtPt.checkConfig(TrfImportReq importReq)`
  1. 检查配置信息

- `SGSMartImportTrfActionExtPt.setRefSystemId(TrfImportReq importReq)`
  1. 设置参考系统ID
  2. 可能会调用外部系统获取信息
  3. 返回BaseResponse<JSONObject>结果

## smartImportToTrfSaveDBComponent

### 调用的类和方法

- 继承自`ImportToTrfSaveDBComponent`
- 可能有特定于SMART模式的保存逻辑

## importToTRFRouterComponent

### 调用的类和方法

- `TrfImportReq.getImportMode()`
  1. 获取导入模式
  2. 根据导入模式和refSystemId返回不同的路由标签（"PULL"或"PULL_2"）
