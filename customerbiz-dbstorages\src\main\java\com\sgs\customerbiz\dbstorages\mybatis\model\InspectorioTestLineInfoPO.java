package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.Date;

public class InspectorioTestLineInfoPO {
    /**
     * id INTEGER(10) 必填<br>
     * 
     */
    private Integer id;

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * refSystemId
     */
    private Integer refSystemId;

    /**
     * external_id VARCHAR(50) 必填<br>
     * 外部ID
     */
    private String externalId;

    /**
     * external_name VARCHAR(255) 必填<br>
     * 外部名称
     */
    private String externalName;

    /**
     * revision VARCHAR(50)<br>
     * 数据版本
     */
    private String revision;

    /**
     * data OTHER<br>
     * 数据内容
     */
    private String data;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 有效无效标记：0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * id INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getId() {
        return id;
    }

    /**
     * id INTEGER(10) 必填<br>
     * 设置 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * 获得 refSystemId
     */
    public Integer getRefSystemId() {
        return refSystemId;
    }

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * 设置 refSystemId
     */
    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    /**
     * external_id VARCHAR(50) 必填<br>
     * 获得 外部ID
     */
    public String getExternalId() {
        return externalId;
    }

    /**
     * external_id VARCHAR(50) 必填<br>
     * 设置 外部ID
     */
    public void setExternalId(String externalId) {
        this.externalId = externalId == null ? null : externalId.trim();
    }

    /**
     * external_name VARCHAR(255) 必填<br>
     * 获得 外部名称
     */
    public String getExternalName() {
        return externalName;
    }

    /**
     * external_name VARCHAR(255) 必填<br>
     * 设置 外部名称
     */
    public void setExternalName(String externalName) {
        this.externalName = externalName == null ? null : externalName.trim();
    }

    /**
     * revision VARCHAR(50)<br>
     * 获得 数据版本
     */
    public String getRevision() {
        return revision;
    }

    /**
     * revision VARCHAR(50)<br>
     * 设置 数据版本
     */
    public void setRevision(String revision) {
        this.revision = revision == null ? null : revision.trim();
    }

    /**
     * data OTHER<br>
     * 获得 数据内容
     */
    public String getData() {
        return data;
    }

    /**
     * data OTHER<br>
     * 设置 数据内容
     */
    public void setData(String data) {
        this.data = data;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 有效无效标记：0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 有效无效标记：0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", refSystemId=").append(refSystemId);
        sb.append(", externalId=").append(externalId);
        sb.append(", externalName=").append(externalName);
        sb.append(", revision=").append(revision);
        sb.append(", data=").append(data);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append("]");
        return sb.toString();
    }
}