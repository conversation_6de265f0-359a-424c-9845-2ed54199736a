package com.sgs.customerbiz.validation.props;

import com.google.common.collect.ImmutableSet;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Set;

@ConfigurationProperties("sci.validation")
@Data
public class ValidationProps {

    private Set<Integer> listOfReplaceDff = ImmutableSet.of(
            RefSystemIdEnum.LOWES.getRefSystemId(),
            RefSystemIdEnum.F21.getRefSystemId(),
            RefSystemIdEnum.JO_ANN.getRefSystemId(),
            RefSystemIdEnum.Walmart.getRefSystemId(),
            RefSystemIdEnum.Walmart_Group.getRefSystemId(),
            RefSystemIdEnum.Target.getRefSystemId(),
            RefSystemIdEnum.BigLots.getRefSystemId(),
            RefSystemIdEnum.DollarTree.getRefSystemId(),
            RefSystemIdEnum.Veyer.getRefSystemId(),
            RefSystemIdEnum.TARGET_INSPECTORIO.getRefSystemId(),
            RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId()
    );
    private Set<Integer> listOfSystemIdToMergeBasic = ImmutableSet.of(62);
    private FilterProp globalsDisabled = new FilterProp();

    @Data
    public static class FilterProp {

        private String disabledScript = "mode == 'ConfirmMatrix' && refSystemId != 7";

    }

    public boolean notReplace(Integer refSystemId) {
        return !listOfReplaceDff.contains(refSystemId);
    }

}
