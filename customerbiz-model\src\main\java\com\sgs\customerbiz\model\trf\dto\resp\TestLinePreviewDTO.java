package com.sgs.customerbiz.model.trf.dto.resp;

import lombok.Data;

@Data
public class TestLinePreviewDTO {

    private boolean fromCustomerTrf;
    private boolean matched;
    private String customerTestPackageId;
    private String customerTestPackageName;
    private String customerTestPackage;
    private String customerTestPropertyId;
    private String customerTestPropertyName;
    private String customerTestProperty;
    private String customerCategoryId;
    private String customerCategoryName;
    private String customerCategory;
    private String customerSubcategoryId;
    private String customerSubcategoryName;
    private String customerSubcategory;
    private Integer mappingInfoPpNo;
    private Integer mappingInfoTestLineId;
    private Integer mappingInfoCitationId;
    private Integer mappingInfoCitationType;
    private String result;
    private String conclusion;
    private String pp;
    private String testLine;
    private String citation;

    public boolean isNotMatched() {
        return ! isMatched();
    }

}
