<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.InspectorioTestLineInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ref_system_id" property="refSystemId" jdbcType="INTEGER" />
    <result column="external_id" property="externalId" jdbcType="VARCHAR" />
    <result column="external_name" property="externalName" jdbcType="VARCHAR" />
    <result column="revision" property="revision" jdbcType="VARCHAR" />
    <result column="data" property="data" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, ref_system_id, external_id, external_name, revision, `data`, active_indicator, 
    created_date, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_inspectorio_test_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_inspectorio_test_line
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_inspectorio_test_line
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoExample" >
    delete from tb_inspectorio_test_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO" >
    insert into tb_inspectorio_test_line (id, ref_system_id, external_id, 
      external_name, revision, `data`, 
      active_indicator, created_date, modified_date
      )
    values (#{id,jdbcType=INTEGER}, #{refSystemId,jdbcType=INTEGER}, #{externalId,jdbcType=VARCHAR}, 
      #{externalName,jdbcType=VARCHAR}, #{revision,jdbcType=VARCHAR}, #{data,jdbcType=OTHER}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO" >
    insert into tb_inspectorio_test_line
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="refSystemId != null" >
        ref_system_id,
      </if>
      <if test="externalId != null" >
        external_id,
      </if>
      <if test="externalName != null" >
        external_name,
      </if>
      <if test="revision != null" >
        revision,
      </if>
      <if test="data != null" >
        `data`,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="refSystemId != null" >
        #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="externalId != null" >
        #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="externalName != null" >
        #{externalName,jdbcType=VARCHAR},
      </if>
      <if test="revision != null" >
        #{revision,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        #{data,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_inspectorio_test_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="page" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_inspectorio_test_line
    order by id limit #{offset}, #{limit}
  </select>
    <update id="updateByExampleSelective" parameterType="map" >
    update tb_inspectorio_test_line
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.refSystemId != null" >
        ref_system_id = #{record.refSystemId,jdbcType=INTEGER},
      </if>
      <if test="record.externalId != null" >
        external_id = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalName != null" >
        external_name = #{record.externalName,jdbcType=VARCHAR},
      </if>
      <if test="record.revision != null" >
        revision = #{record.revision,jdbcType=VARCHAR},
      </if>
      <if test="record.data != null" >
        `data` = #{record.data,jdbcType=OTHER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_inspectorio_test_line
    set id = #{record.id,jdbcType=INTEGER},
      ref_system_id = #{record.refSystemId,jdbcType=INTEGER},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      external_name = #{record.externalName,jdbcType=VARCHAR},
      revision = #{record.revision,jdbcType=VARCHAR},
      `data` = #{record.data,jdbcType=OTHER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO" >
    update tb_inspectorio_test_line
    <set >
      <if test="refSystemId != null" >
        ref_system_id = #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="externalId != null" >
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="externalName != null" >
        external_name = #{externalName,jdbcType=VARCHAR},
      </if>
      <if test="revision != null" >
        revision = #{revision,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        `data` = #{data,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO" >
    update tb_inspectorio_test_line
    set ref_system_id = #{refSystemId,jdbcType=INTEGER},
      external_id = #{externalId,jdbcType=VARCHAR},
      external_name = #{externalName,jdbcType=VARCHAR},
      revision = #{revision,jdbcType=VARCHAR},
      `data` = #{data,jdbcType=OTHER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_inspectorio_test_line
      (`id`,`ref_system_id`,`external_id`,
      `external_name`,`revision`,`data`,
      `active_indicator`,`created_date`,`modified_date`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=INTEGER},#{ item.refSystemId, jdbcType=INTEGER},#{ item.externalId, jdbcType=VARCHAR},
      #{ item.externalName, jdbcType=VARCHAR},#{ item.revision, jdbcType=VARCHAR},#{ item.data, jdbcType=OTHER},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedDate, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_inspectorio_test_line 
      <set>
        <if test="item.refSystemId != null"> 
          `ref_system_id` = #{item.refSystemId, jdbcType = INTEGER},
        </if> 
        <if test="item.externalId != null"> 
          `external_id` = #{item.externalId, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalName != null"> 
          `external_name` = #{item.externalName, jdbcType = VARCHAR},
        </if> 
        <if test="item.revision != null"> 
          `revision` = #{item.revision, jdbcType = VARCHAR},
        </if> 
        <if test="item.data != null"> 
          `data` = #{item.data, jdbcType = OTHER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = INTEGER}
        </if>
      </where>
    </foreach>
  </update>
</mapper>