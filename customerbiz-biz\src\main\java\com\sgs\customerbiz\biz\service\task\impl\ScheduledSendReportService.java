package com.sgs.customerbiz.biz.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONAware;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.dto.req.SendReportScheduled;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.dto.DataSyncExtra;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.dto.TaskObjectRelDTO;
import com.sgs.customerbiz.biz.dto.TrfEventPoolDTO;
import com.sgs.customerbiz.biz.enums.EventPoolStatus;
import com.sgs.customerbiz.biz.event.handler.HandleContextHolder;
import com.sgs.customerbiz.biz.exception.ScheduledCollectDataException;
import com.sgs.customerbiz.biz.service.aftersplit.DataAfterSplitHandler;
import com.sgs.customerbiz.biz.service.aftersplit.DataAfterSplitService;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.datacollector.DataCollectService;
import com.sgs.customerbiz.biz.service.datacollector.DataCollectorContextHolder;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertService;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.biz.service.task.impl.batch.PageCollector;
import com.sgs.customerbiz.biz.service.task.impl.batch.SendLocalILayer;
import com.sgs.customerbiz.biz.service.task.impl.batch.SendLocalILayerBodyMerger;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.TrfCtx;
import com.sgs.customerbiz.biz.service.task.impl.message.IgnoreAttrNameMap;
import com.sgs.customerbiz.biz.utils.*;
import com.sgs.customerbiz.core.config.EmailRecipientConfig;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfEventPoolPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.EmailClient;
import com.sgs.customerbiz.integration.dto.EMailRequest;
import com.sgs.customerbiz.model.trf.enums.TaskParamObjectTypeEnum;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.framework.core.util.IdUtil;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 定时任务处理
 */
@Service
@Slf4j
public class ScheduledSendReportService {

    private final TrfDomainService trfDomainService;
    private final TrfEventPoolService trfEventPoolService;
    private final EventSubscribeService eventSubscribeService;
    private final IdService idService;
    private final ConfigClient configClient;
    private final DataCollectService dataCollectService;
    private final DataPreConvertService dataPreConvertService;

    private final DataConvertor<String, String, JSON> jsonDataConvertor;

    private final TaskService taskService;

    private final Map<Long, SendLocalILayerBodyMerger> mergerMap;

    private final EmailClient emailClient;

    private final EmailRecipientConfig emailRecipientConfig;

    private final DataAfterSplitService dataAfterSplitService;

    public ScheduledSendReportService(TrfDomainService trfDomainService,
                                      TrfEventPoolService trfEventPoolService,
                                      EventSubscribeService eventSubscribeService,
                                      IdService idService,
                                      ConfigClient configClient,
                                      DataCollectService dataCollectService,
                                      DataPreConvertService dataPreConvertService,
                                      DataConvertor<String, String, JSON> jsonDataConvertor,
                                      TaskService taskService,
                                      List<SendLocalILayerBodyMerger> mergers,
                                      EmailClient emailClient,
                                      EmailRecipientConfig emailRecipientConfig,
                                      DataAfterSplitService dataAfterSplitService) {
        this.trfDomainService = trfDomainService;
        this.trfEventPoolService = trfEventPoolService;
        this.eventSubscribeService = eventSubscribeService;
        this.idService = idService;
        this.configClient = configClient;
        this.dataCollectService = dataCollectService;
        this.dataPreConvertService = dataPreConvertService;
        this.jsonDataConvertor = jsonDataConvertor;
        this.taskService = taskService;
        this.mergerMap = mergers.stream().collect(Collectors.toMap(SendLocalILayerBodyMerger::getApiId, Function.identity()));
        this.emailClient = emailClient;
        this.emailRecipientConfig = emailRecipientConfig;
        this.dataAfterSplitService = dataAfterSplitService;
    }

    @Builder
    @Getter
    @ToString
    public static class SubscribeCtx {

        private EventSubscribeDTO subscribe;

        private SystemApiDTO systemApi;

    }

    @Transactional
    public void sendReport(ScheduledSendReportRequest send) {
        Integer refSystemId = send.getRefSystemId();
        Optional<Long> apiId = send.getApiId();

        // 查询一段时间内的trf通过trf_event_pool
        List<TrfEventPoolDTO> trfEventPoolPOList = trfEventPoolService.findByWaitSendSubscriber(
                refSystemId,
                apiId,
                send.getStartDateTime().orElse(null),
                send.getEndDateTime(),
                send.getEventPoolStatusList()
        );
        List<TrfCtx> trfCtxList = prepareTrfCtxList(trfEventPoolPOList, send.getEndDateTime(), refSystemId);
        JobLogUtil.info(log, "refSystemId: {} find trfCtxList: {}", refSystemId, trfCtxList.size());

        // 找到对应的订阅和API
        SubscribeCtx subscribeCtx = prepareSubscribeCtxList(refSystemId, apiId);
        JobLogUtil.info(log,"refSystemId: {} pick subscribeCtx: {}", refSystemId, subscribeCtx);

        // 一个trf一个trf的收集数据
        List<TrfCtx> prepareSendList = prepareCollectDataBy(trfCtxList, subscribeCtx);
        JobLogUtil.info(log,"refSystemId: {} collectTrfData: {}", refSystemId, prepareSendList.size());

        List<Pair<List<TaskInfoDTO>, List<TrfCtx>>> toSendTaskInfoList;
        if(CollectionUtils.isEmpty(prepareSendList) && send.isSendWhenEmpty()) {
            // 添加空数据 for target
            toSendTaskInfoList = packageTaskInfoBy(true, ImmutableList.of(TrfCtx.createEmpty(subscribeCtx)), send);
            JobLogUtil.info(log,"refSystemId: {} packageEmptyTaskInfo: {} by mode: {}",
                    refSystemId,
                    toSendTaskInfoList.size(),
                    send.getSendMode());
        } else {
            toSendTaskInfoList = packageTaskInfoBy(false, prepareSendList, send);
            // 聚合发送模式
        }

        JobLogUtil.info(log,"refSystemId: {} packageTaskInfo: {} by mode: {}",
                refSystemId,
                toSendTaskInfoList.size(),
                send.getSendMode());

        // 标记reportDelivery 关联eventPool 并提交task
        for (Pair<List<TaskInfoDTO>, List<TrfCtx>> pair : toSendTaskInfoList) {
            List<TaskInfoDTO> toSendTaskInfo = pair.getFirst();
            List<TrfCtx> toSendTrfCtxList = pair.getSecond();

            toSendTrfCtxList.forEach(trfCtx ->
                    JobLogUtil.info(log,"mark delivery report subscriber: {}, trfNo: {}, toMarkDelivery: {}",
                            trfCtx.getSubscribe().getSubscriber(),
                            trfCtx.isNotEmpty() ? trfCtx.getTrf().getTrfNo() : refSystemId + "Empty",
                            trfCtx.getDeliveryReportNos())
            );
            List<TrfEventPoolPO> toUpdatedTrfEventPool = pair.getSecond().stream()
                    .flatMap(ctx -> ctx.getOrderedTrfEventPoolList().stream())
                    .map(dto -> TrfEventPoolService.createToUpdatedFrom(
                            dto.getId(),
                            toSendTaskInfo.get(0).getTaskId(),
                            EventPoolStatus.Processing.getCode()))
                    .collect(Collectors.toList());
            if( ! CollectionUtils.isEmpty(toUpdatedTrfEventPool)) {
                trfEventPoolService.batchUpdate(toUpdatedTrfEventPool);
            }
            for (TaskInfoDTO taskInfoDTO : toSendTaskInfo) {
                taskService.submitTask(taskInfoDTO);
            }
            JobLogUtil.info(log,"refSystemId: {} submitTask: {} ref event_pool: {}",
                    refSystemId,
                    toSendTaskInfo,
                    toUpdatedTrfEventPool);
        }
    }

    public @NotNull List<TrfCtx> prepareCollectDataBy(List<TrfCtx> trfCtxList, SubscribeCtx subscribeCtx) {
        return trfCtxList.stream()
                .map(trfCtx -> collectTrfData(trfCtx, subscribeCtx))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<TrfCtx> collectTrfData(TrfCtx trfCtx, SubscribeCtx subscribeCtx) {
        EventSubscribeDTO subscribe = subscribeCtx.getSubscribe();
        SystemApiDTO systemApi = subscribeCtx.getSystemApi();
        ObjectEvent trfEvent = trfCtx.getLatestTrfEventPool().getTrfEvent();
        TrfCtx.TrfCtxBuilder newTrfCtxBuilder = trfCtx.toBuilder()
                .subscribe(subscribe)
                .systemApi(systemApi);
        try {
            HandleContextHolder.setTrfEvent(trfEvent); // for data collect
            // 根据订阅者配置执行相应系统操作
            DataSyncExtra extraData = DataSyncExtraUtils.createFrom(trfEvent);
            HandleContextHolder.setDataSyncExtra(extraData);  // for data collect
            // 1. 根据TRF收集数据
            HandleContextHolder.setTrfEventSubscribe(subscribe);
            DataCollectorContextHolder.setInScheduled(true);
            CollectedData collectedData = dataCollectService.collect(
                    trfEvent.getRefSystemId(),
                    trfEvent.getTrfNo(),
                    subscribe.getNotifyData(),
                    extraData);

            if( CollectionUtils.isEmpty(DataCollectorContextHolder.getDeliveryReportNos())) {
                JobLogUtil.info(log,"RefSystemId : {}, API Id : {}, TRF No: {} collected report is empty!",
                        subscribe.getRefSystemId(), subscribe.getApiId(), trfEvent.getTrfNo());
                return Optional.empty();
            }

            // 2. 数据收集完整后，预处理扩展
            /**
             * 2.1 Sgsmart.去除无效的TestLine （无效类型、无效状态）
             * 2.2 AF-Yili.处理TestResultValue.merge
             * 2.3 优衣库.LabCode\TestLine.MappingToCustomer
             */
            collectedData = dataPreConvertService.preHandler(subscribe, trfEvent, collectedData);

            trfDomainService.markDeliveryReport(
                    subscribe.getId(),
                    subscribe.getSubscriber(),
                    subscribe.getApiId(),
                    DataCollectorContextHolder.getDeliveryReportNos()
            );

            //按条件计算结果分组，ture为需要发送，false为不需要发送
            Map<Boolean, List<CollectedData>> groupByCondition = splitCollectedData(subscribe, trfEvent, systemApi, collectedData).stream()
                    .collect(Collectors.partitioningBy(splitCollectedData -> SubscriberConditionUtils.subscriberConditionEval(subscribe, splitCollectedData)));

            //如果为false表示被条件阻挡，需要更新一下DeliveryFlag
            List<CollectedData> toMarkConditionList = groupByCondition.getOrDefault(false, Collections.emptyList());
            if(!CollectionUtils.isEmpty(toMarkConditionList)) {
                List<String> rejectedReportNoList = toMarkConditionList.stream()
                        .flatMap(cd -> CollectDataUtil.fetchReportNo(cd).stream())
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                JobLogUtil.info(log,"RefSystemId : {}, API Id : {}, TRF No:{} Report Nos: {} rejected by Condition",
                        subscribe.getRefSystemId(), subscribe.getApiId(), trfEvent.getTrfNo(), rejectedReportNoList);
                trfDomainService.conditionDeliveryReport(
                        rejectedReportNoList,
                        subscribe.getApiId()
                );
            }

            //如果为true表示需要发送
            List<CollectedData> filteredConditionCollectedDataList = groupByCondition.getOrDefault(true, Collections.emptyList());
            if(CollectionUtils.isEmpty(filteredConditionCollectedDataList)) {
                return emptyWhenSubscriberCondition(trfCtx, subscribe, trfEvent, DataCollectorContextHolder.getDeliveryReportNos(), DataCollectorContextHolder.getDeliveryReportNos());
            }

            Map<Boolean, List<CollectedData>> groupByRepeated = filteredConditionCollectedDataList.stream()
                    .collect(Collectors.partitioningBy(splitCollectedData -> {
                        Object convertedData = jsonDataConvertor.convert(splitCollectedData.toJSONString(), systemApi.getRequestBodyTemplate());
                        String extId = getExtId(splitCollectedData, systemApi, trfEvent);
                        TaskInfoDTO preTask = TaskInfoDTOUtils.create(idService.nextId(), idService.nextId(), extId, subscribe, trfEvent, convertedData, systemApi);
                        preTask.setTaskStatus(TaskStatusEnum.PRE.getCode());
                        if (taskService.checkSubmitTask(preTask, IgnoreAttrNameMap.getIgnoreAttrNames(systemApi.getId()))) {
                            taskService.submitTask(preTask);
                            return true;
                        }
                        return false;
                    }));

            List<CollectedData> toMarkRepeatedList = groupByRepeated.getOrDefault(false, Collections.emptyList());
            if(!CollectionUtils.isEmpty(toMarkRepeatedList)) {
                List<String> repeatedReportNoList = toMarkRepeatedList.stream()
                        .flatMap(cd -> CollectDataUtil.fetchReportNo(cd).stream())
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                JobLogUtil.info(log,"RefSystemId : {}, API Id : {}, TRF No:{} Report Nos: {} rejected by Repeated",
                        subscribe.getRefSystemId(), subscribe.getApiId(), trfEvent.getTrfNo(), repeatedReportNoList);
                trfDomainService.repeatedDeliveryReport(
                        repeatedReportNoList,
                        subscribe.getApiId()
                );
            }

            List<CollectedData> filteredRepetedCollectedDataList = groupByRepeated.getOrDefault(true, Collections.emptyList());
            if(CollectionUtils.isEmpty(filteredRepetedCollectedDataList)) {
                return emptyWhenConvertNothing(trfCtx, subscribe, trfEvent, DataCollectorContextHolder.getDeliveryReportNos());
            }

            newTrfCtxBuilder.collectedDataList(filteredRepetedCollectedDataList);
            TrfCtx newCtx = newTrfCtxBuilder.build();
            return Optional.of(newCtx);

        } catch (Throwable e) {
            try {
                String refSystemId = Optional.ofNullable(subscribe.getRefSystemId()).map(Object::toString).orElse("");
                String trfNo = Optional.ofNullable(trfEvent.getTrfNo()).orElse("");
                String errorMessage = "RefSystemId : " + refSystemId + ",  TRF No: " + trfNo + " collect data got an error! skip send!  message is :" + e.getMessage();
                JobLogUtil.error(log, new ScheduledCollectDataException(errorMessage, e), errorMessage);
                alertEmail(e, refSystemId, trfNo);
            } catch (Throwable t) {
                //ignore
            }
            return Optional.empty();
        }finally {
            HandleContextHolder.removeDataSyncExtra();
            HandleContextHolder.clean();
            DataCollectorContextHolder.clearReportNos();
        }
    }

    private void alertEmail(Throwable e, String refSystemId, String trfNo) {
        LinkedList<String> contentList = Lists.newLinkedList();
        contentList.add(String.format("RefSystemId: %s", refSystemId));
        contentList.add(String.format("Skip TrfNo: %s", trfNo));
        contentList.add(String.format("Error Time: %s，", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        contentList.add(String.format("Error Message: %s", e.getMessage()));
        String mailContent = StringUtils.join(contentList, "<br/>");

        EMailRequest eMailRequest = EMailRequest.builder()
                .mailSubject(String.format("【%s】Scheduled Job Collect Warning", System.getProperty("spring.profiles.active")))
                .mailTo(emailRecipientConfig.getRecipients().getScheduledReportRecipients())
                .mailText(mailContent)
                .build();
        TempSendEmailExecutor.asyncExecute(() -> emailClient.sendEmail(eMailRequest));
    }

    private static String getExtId(CollectedData splitCollectedData, SystemApiDTO systemApi, ObjectEvent trfEvent) {
        return systemApi.splitByReport() ? CollectDataUtil.fetchFirstReportNo(splitCollectedData) : trfEvent.getTrfNo();
    }

    private @NotNull Optional<TrfCtx> emptyWhenConvertNothing(TrfCtx trfCtx, EventSubscribeDTO subscribe, ObjectEvent trfEvent, List<String> reportNos) {
        JobLogUtil.info(log,"RefSystemId : {}, API Id : {}, TRF No: {} Report Nos: {} data has been sent!",
                subscribe.getRefSystemId(), subscribe.getApiId(), trfEvent.getTrfNo(), reportNos);
        List<TrfEventPoolPO> toUpdated = trfCtx.getOrderedTrfEventPoolList().stream()
                .map(eventPool -> TrfEventPoolService.createToUpdateStatus(eventPool.getId(), EventPoolStatus.Repet.getCode()))
                .collect(Collectors.toList());
        trfEventPoolService.batchUpdate(toUpdated);
        return Optional.empty();
    }

    private @NotNull Optional<TrfCtx> emptyWhenSubscriberCondition(TrfCtx trfCtx, EventSubscribeDTO subscribe, ObjectEvent trfEvent, List<String> reportNos, Object reportNosAfterRd) {
        JobLogUtil.info(log,"RefSystemId : {}, API Id : {}, TRF No:{} Report Nos: {} RD reportNos:{} Condition Fail or split nothing.",
                subscribe.getRefSystemId(), subscribe.getApiId(), trfEvent.getTrfNo(), reportNos, reportNosAfterRd);
        List<TrfEventPoolPO> toUpdated = trfCtx.getOrderedTrfEventPoolList().stream()
                .map(eventPool -> TrfEventPoolService.createToUpdateStatus(eventPool.getId(), EventPoolStatus.Condition.getCode()))
                .collect(Collectors.toList());
        trfEventPoolService.batchUpdate(toUpdated);
        return Optional.empty();
    }



    @NotNull
    public SubscribeCtx prepareSubscribeCtxList(Integer refSystemId, Optional<Long> apiId) {
        // 这里可以看做是subscriber
        List<EventSubscribeDTO> eventSubscribeList = eventSubscribeService.findBySubscriberAndEventCode(
                apiId,
                refSystemId,
                TrfEventCodeMapping.TRF_COMPLETED_EVENT)
                .stream()
                .filter(subscribe -> subscribe.getSubscriber().equals(refSystemId))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(eventSubscribeList)) {
            throw new IllegalArgumentException("event subscriber is empty. subscriber: " + refSystemId);
        }

        EventSubscribeDTO subscribe = eventSubscribeList.get(0);
        SystemApiDTO api = configClient.getSystemApiInfo(subscribe.getSubscriber(), subscribe.getApiId());
        Assert.notNull(api, "systemId: "+refSystemId+" ,api: "+subscribe.getApiId()+" not existed.");
        return SubscribeCtx.builder()
                .subscribe(subscribe)
                .systemApi(api)
                .build();
    }

    @NotNull
    public List<TrfCtx> prepareTrfCtxList(List<TrfEventPoolDTO> trfEventPoolPOList, Date endDateTime, Integer refSystemId) {

        List<String> trfNoList = trfEventPoolPOList.stream()
                .map(TrfEventPoolDTO::getTrfNo)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(trfNoList)) {
            return Collections.emptyList();
        }
        List<TrfInfoPO> trfInfoPOS = trfDomainService.selectByTrfNos(trfNoList);
        Map<String, List<TrfEventPoolDTO>> groupedByTrfNo = trfEventPoolPOList.stream()
                .collect(Collectors.groupingBy(TrfEventPoolDTO::getTrfNo));
        return trfInfoPOS.stream()
                // 防御编程，通过taskInfoPO如果没有查询到trf信息
                .filter(trfInfo -> groupedByTrfNo.containsKey(trfInfo.getTrfNo()))
                .map(trfInfo -> {
                    List<TrfEventPoolDTO> taskInfoListByTrfNo = groupedByTrfNo.get(trfInfo.getTrfNo()).stream()
                            .sorted(Comparator
                                    .comparing(TrfEventPoolDTO::getCreatedDate)
                                    .reversed())
                            .collect(Collectors.toList());
                    return TrfCtx.prepare(trfInfo, taskInfoListByTrfNo);
                })
                .filter(trfCtx ->
                        endDateTime.compareTo(trfCtx.getOrderedTrfEventPoolList().get(0).getCreatedDate())  >= 0
                )
                .collect(Collectors.toList());
    }

    @NotNull
    public List<Pair<List<TaskInfoDTO>, List<TrfCtx>>> packageTaskInfoBy(boolean isEmpty, List<TrfCtx> prepareTaskInfoList, ScheduledSendReportRequest send) {
        Assert.notNull(prepareTaskInfoList, "");
        SendReportScheduled.SendMode sendMode = Optional.ofNullable(send)
                .map(ScheduledSendReportRequest::getSendMode)
                .orElseThrow(() -> new IllegalArgumentException("Unexpected value: " + send));
        switch (sendMode) {
            case Batch:
                int batchMaxSize = send.getBatchMaxSize();
                List<List<TrfCtx>> pages = prepareTaskInfoList.stream().collect(PageCollector.of(batchMaxSize));
                return pages.stream()
                        .map(trfCtxList -> Pair.<List<TaskInfoDTO>, List<TrfCtx>>of(ImmutableList.of(invokeByList(isEmpty, trfCtxList, send.getRefSystemId())), trfCtxList))
                        .collect(Collectors.toList());
            case OneByOne:
                return prepareTaskInfoList.stream()
                        .map(ctx -> Pair.<List<TaskInfoDTO>, List<TrfCtx>>of(invoke(isEmpty, ctx, send.getRefSystemId()), ImmutableList.of(ctx)))
                        .collect(Collectors.toList());
            default:
                throw new IllegalArgumentException("Unexpected value: " + send.getSendMode());
        }
    }

    public List<CollectedData> splitCollectedData(EventSubscribeDTO subscribe, ObjectEvent event, SystemApiDTO systemApi, CollectedData collectedData) {
        if(systemApi.splitByReport()) {
            List<CollectedData> splitCollectedData = CollectDataUtil.splitByReport(collectedData);
            for(CollectedData splitCollectedDataItem : splitCollectedData) {
                dataAfterSplitService.afterSplit(subscribe, event, splitCollectedDataItem);
            }
            return splitCollectedData;
        }
        return ImmutableList.of(collectedData);
    }

    public TaskInfoDTO invokeByList(boolean isEmpty, List<TrfCtx> trfCtxList, Integer refSystemId) {
        TrfCtx first = trfCtxList.get(0);
        EventSubscribeDTO subscribe = first.getSubscribe();
        SystemApiDTO systemApi = first.getSystemApi();


        List<JSON> convertedDataList = trfCtxList.stream()
                .flatMap(trfCtx -> trfCtx.getCollectedDataList().stream())
                .map(collectedData ->
                        jsonDataConvertor.convert(collectedData.toJSONString(), systemApi.getRequestBodyTemplate())
                ).collect(Collectors.toList());
        List<JSON> datas = convertedDataList.stream()
                .map(JSONAware::toJSONString)
                .map(dataStr -> JSON.parseObject(dataStr, SendLocalILayer.class))
                .map(SendLocalILayer::getBody)
                .collect(Collectors.toList());

        Long taskId = idService.nextId();
        SendLocalILayerBodyMerger merger = Optional.ofNullable(mergerMap.get(systemApi.getId()))
                .orElseThrow(() -> new IllegalStateException("merger not found by refSystemId:" + refSystemId));
        String firstBu = first.getOrderedTrfEventPoolList().stream().findFirst()
                .map(TrfEventPoolDTO::getTrfEvent)
                .map(ObjectEvent::getProductLineCode)
                .orElse("SL");
        //默认是空的因为target会有一个如果为空也要发消息的逻辑
        SendLocalILayer merged = SendLocalILayer.createSyncReportWithoutBody(refSystemId, firstBu, taskId, merger.merge(datas));
        if(isEmpty) {
            return TaskInfoDTOUtils.createEmpty(
                    idService.nextId(),
                    taskId,
                    refSystemId,
                    subscribe,
                    merged,
                    systemApi);
        }
        TaskInfoDTO toSendTask = TaskInfoDTOUtils.create(
                idService.nextId(),
                taskId,
                String.valueOf(taskId),
                subscribe,
                first.getLatestTrfEventPool().getTrfEvent(),
                merged,
                systemApi);
        List<TaskObjectRelDTO> taskReportRelList = trfCtxList.stream()
                .flatMap(ctx ->
                        ctx.getDeliveryReportNos().stream()
                                .map(deliveryReportNo -> {
                                    TaskObjectRelDTO taskParamDTO = new TaskObjectRelDTO();
                                    taskParamDTO.setObjectType(TaskParamObjectTypeEnum.Report.getCode());
                                    taskParamDTO.setObjectNo(deliveryReportNo);
                                    return taskParamDTO;
                                })
                )
                .collect(Collectors.toList());
        toSendTask.setTaskObjectRelList(taskReportRelList);
        return toSendTask;
    }


    public List<TaskInfoDTO> invoke(boolean isEmpty, TrfCtx trfCtx, Integer refSystemId) {
        EventSubscribeDTO subscribe = trfCtx.getSubscribe();
        SystemApiDTO systemApi = trfCtx.getSystemApi();
        ObjectEvent trfEvent = trfCtx.getLatestTrfEventPool().getTrfEvent();
        List<CollectedData> convertedDataList = trfCtx.getCollectedDataList();
        return convertedDataList.stream()
                .map(collectedData -> {
                    //生成taskId aka msgId
                    Long taskId = IdUtil.snowflakeId();
                    ((DataSyncExtra) collectedData.getExtraData()).setRandomSequence(taskId);
                    Object convertedData = jsonDataConvertor.convert(collectedData.toJSONString(), systemApi.getRequestBodyTemplate());

                    if(isEmpty) {
                        return TaskInfoDTOUtils.createEmpty(
                                idService.nextId(),
                                taskId,
                                refSystemId,
                                subscribe,
                                convertedData,
                                systemApi);
                    }

                    TaskInfoDTO toSendTask = TaskInfoDTOUtils.create(
                            idService.nextId(),
                            taskId,
                            getExtId(collectedData, systemApi, trfEvent),
                            subscribe,
                            trfEvent,
                            convertedData,
                            systemApi);

                    // 设置task关联report
                    List<TaskObjectRelDTO> taskReportRelList = CollectDataUtil.fetchReportNo(collectedData).stream()
                            .map(deliveryReportNo -> {
                                TaskObjectRelDTO taskParamDTO = new TaskObjectRelDTO();
                                taskParamDTO.setObjectType(TaskParamObjectTypeEnum.Report.getCode());
                                taskParamDTO.setObjectNo(deliveryReportNo);
                                return taskParamDTO;
                            })
                            .collect(Collectors.toList());
                    toSendTask.setTaskObjectRelList(taskReportRelList);
                    return toSendTask;
                })
                .collect(Collectors.toList());
    }

}
