package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.DataConvertFn;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrLangDTO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductSampleAttrDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;


@Slf4j
@Component
public class GetDffFormValueFn implements DataConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }
        Object dffAttrStr = args[0];
        Object key = args[1];
        RdProductDTO productDTO = JSONObject.parseObject(dffAttrStr.toString(), RdProductDTO.class);
        if (Func.isEmpty(productDTO) || Func.isEmpty(productDTO.getProductAttrList())) {
            return null;
        }
        return productDTO.getProductAttrList().stream()
                .filter(l -> Objects.equals(l.getLabelCode(), key.toString()))
                .map(RdProductSampleAttrDTO::getValue)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse("");
    }

    @Override
    public String getName() {
        return "getDffFormValueFn";
    }

    @Override
    public String desc() {
        return "get Dff Label Value from ProductAttrs or SampleAttrs by labelCode and languageId";
    }
}
