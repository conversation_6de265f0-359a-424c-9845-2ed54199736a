package com.sgs.customerbiz.biz.service.inspectorio;

import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.InspectorioTestLineInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO;
import com.sgs.customerbiz.integration.dto.inspectorio.RevisableData;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * TestLineInfo的Mapper管理器
 */
@Component
public class TestLineMapperMgr implements InspectorioMapper<InspectorioTestLineInfoPO, InspectorioTestLineInfoExample> {

    private final InspectorioTestLineInfoMapper testLineInfoMapper;

    public TestLineMapperMgr(InspectorioTestLineInfoMapper testLineInfoMapper) {
        this.testLineInfoMapper = testLineInfoMapper;
    }

    @Override
    public int countByExample(InspectorioTestLineInfoExample example) {
        return testLineInfoMapper.countByExample(example);
    }

    @Override
    public List<InspectorioTestLineInfoPO> page(int offset, int limit) {
        return testLineInfoMapper.page(offset, limit);
    }

    @Override
    public int batchInsert(List<InspectorioTestLineInfoPO> list) {
        return testLineInfoMapper.batchInsert(list);
    }

    @Override
    public int batchUpdate(List<InspectorioTestLineInfoPO> list) {
        return testLineInfoMapper.batchUpdate(list);
    }

    @Override
    public String getExternalId(InspectorioTestLineInfoPO record) {
        return record.getExternalId();
    }

    @Override
    public String getData(InspectorioTestLineInfoPO record) {
        return record.getData();
    }

    @Override
    public Integer getActiveIndicator(InspectorioTestLineInfoPO record) {
        return record.getActiveIndicator();
    }

    @Override
    public Date getCreatedDate(InspectorioTestLineInfoPO record) {
        return record.getCreatedDate();
    }

    @Override
    public InspectorioTestLineInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum) {
        return data.toTestLine(refSystemIdEnum);
    }

    @Override
    public InspectorioTestLineInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, String datePattern) {
        return data.toTestLine(refSystemIdEnum, datePattern);
    }

    @Override
    public InspectorioTestLineInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        return data.toTestLine(refSystemIdEnum, datePatterns);
    }

    @Override
    public InspectorioTestLineInfoPO createCopyForUpdate(InspectorioTestLineInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum) {
        InspectorioTestLineInfoPO updateRecord = newData.toTestLine(refSystemIdEnum);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioTestLineInfoPO createCopyForUpdate(InspectorioTestLineInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, String datePattern) {
        InspectorioTestLineInfoPO updateRecord = newData.toTestLine(refSystemIdEnum, datePattern);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioTestLineInfoPO createCopyForUpdate(InspectorioTestLineInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        InspectorioTestLineInfoPO updateRecord = newData.toTestLine(refSystemIdEnum, datePatterns);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioTestLineInfoPO createCopyForDelete(InspectorioTestLineInfoPO original) {
        InspectorioTestLineInfoPO deleteRecord = new InspectorioTestLineInfoPO();
        deleteRecord.setId(original.getId());
        deleteRecord.setRefSystemId(original.getRefSystemId());
        deleteRecord.setExternalId(original.getExternalId());
        deleteRecord.setExternalName(original.getExternalName());
        deleteRecord.setRevision(original.getRevision());
        deleteRecord.setData(original.getData());
        deleteRecord.setCreatedDate(original.getCreatedDate());
        deleteRecord.setActiveIndicator(0);
        deleteRecord.setModifiedDate(new Date());
        return deleteRecord;
    }
} 