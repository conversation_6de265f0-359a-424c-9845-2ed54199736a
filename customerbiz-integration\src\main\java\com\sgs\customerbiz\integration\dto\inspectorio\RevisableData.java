package com.sgs.customerbiz.integration.dto.inspectorio;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO;
import com.sgs.framework.model.enums.RefSystemIdEnum;

public class RevisableData  extends JSONObject implements Revisable{


    @Override
    public String getRevision() {
        return getString("revision");
    }

    @Override
    public String getId() {
        return getString("id");
    }

    /**
     * 获取activeIndicator状态
     * @return activeIndicator值，默认为1（激活状态）
     */
    public Integer getActiveIndicator() {
        Integer indicator = getInteger("activeIndicator");
        return indicator != null ? indicator : 1;
    }

    /**
     * 设置activeIndicator状态
     * @param activeIndicator 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        put("activeIndicator", activeIndicator);
    }

    public InspectorioPackageInfoPO toPackage(RefSystemIdEnum refSystemIdEnum) {
        return toPackage(refSystemIdEnum, "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");
    }

    public InspectorioPackageInfoPO toPackage(RefSystemIdEnum refSystemIdEnum, String datePattern) {
        InspectorioPackageInfoPO packageInfoPO = new InspectorioPackageInfoPO();
        packageInfoPO.setRefSystemId(refSystemIdEnum.getRefSystemId());
        packageInfoPO.setExternalId(getId());
        packageInfoPO.setExternalName(getString("name"));
        packageInfoPO.setRevision(getRevision());
        packageInfoPO.setData(JSONObject.toJSONString(this));
        packageInfoPO.setActiveIndicator(getActiveIndicator());
        packageInfoPO.setCreatedDate(parseDate(getString("createdAt"), datePattern));
        packageInfoPO.setModifiedDate(parseDate(getString("updatedAt"), datePattern));
        return packageInfoPO;
    }

    public InspectorioPackageInfoPO toPackage(RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        InspectorioPackageInfoPO packageInfoPO = new InspectorioPackageInfoPO();
        packageInfoPO.setRefSystemId(refSystemIdEnum.getRefSystemId());
        packageInfoPO.setExternalId(getId());
        packageInfoPO.setExternalName(getString("name"));
        packageInfoPO.setRevision(getRevision());
        packageInfoPO.setData(JSONObject.toJSONString(this));
        packageInfoPO.setActiveIndicator(getActiveIndicator());
        packageInfoPO.setCreatedDate(parseDate(getString("createdAt"), datePatterns));
        packageInfoPO.setModifiedDate(parseDate(getString("updatedAt"), datePatterns));
        return packageInfoPO;
    }

    public InspectorioTestLineInfoPO toTestLine(RefSystemIdEnum refSystemIdEnum) {
        return toTestLine(refSystemIdEnum, "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");
    }

    public InspectorioTestLineInfoPO toTestLine(RefSystemIdEnum refSystemIdEnum, String datePattern) {
        InspectorioTestLineInfoPO testLineInfoPO = new InspectorioTestLineInfoPO();
        testLineInfoPO.setRefSystemId(refSystemIdEnum.getRefSystemId());
        testLineInfoPO.setExternalId(getId());
        testLineInfoPO.setExternalName(getString("name"));
        testLineInfoPO.setRevision(getRevision());
        testLineInfoPO.setData(JSONObject.toJSONString(this));
        testLineInfoPO.setActiveIndicator(1);
        testLineInfoPO.setCreatedDate(parseDate(getString("createdAt"), datePattern));
        testLineInfoPO.setModifiedDate(parseDate(getString("updatedAt"), datePattern));
        return testLineInfoPO;
    }

    public InspectorioTestLineInfoPO toTestLine(RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        InspectorioTestLineInfoPO testLineInfoPO = new InspectorioTestLineInfoPO();
        testLineInfoPO.setRefSystemId(refSystemIdEnum.getRefSystemId());
        testLineInfoPO.setExternalId(getId());
        testLineInfoPO.setExternalName(getString("name"));
        testLineInfoPO.setRevision(getRevision());
        testLineInfoPO.setData(JSONObject.toJSONString(this));
        testLineInfoPO.setActiveIndicator(1);
        testLineInfoPO.setCreatedDate(parseDate(getString("createdAt"), datePatterns));
        testLineInfoPO.setModifiedDate(parseDate(getString("updatedAt"), datePatterns));
        return testLineInfoPO;
    }

    private Date parseDate(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("Failed to parse date: " + dateStr, e);
        }
    }

    private Date parseDate(String dateStr, String pattern) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("Failed to parse date: " + dateStr + " with pattern: " + pattern, e);
        }
    }

    private Date parseDate(String dateStr, java.util.List<String> patterns) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // 继续尝试下一个pattern
                continue;
            }
        }
        
        // 如果所有pattern都失败，抛出异常
        throw new RuntimeException("Failed to parse date: " + dateStr + " with all patterns: " + patterns);
    }
}
