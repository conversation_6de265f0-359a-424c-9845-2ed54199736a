package com.sgs.customerbiz.biz.service.inspectorio;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.service.SystemAPIConfigService;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.IlayerBody;
import com.sgs.framework.core.base.BaseResponse;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Service
public class InspectorioBizService {

    private final SystemAPIConfigService systemAPIConfigService;
    private final LocalILayerClient iLayerClient;
    private final JsonDataConvertor jsonDataConvertor;

    public InspectorioBizService(SystemAPIConfigService systemAPIConfigService, LocalILayerClient iLayerClient, JsonDataConvertor jsonDataConvertor) {
        this.systemAPIConfigService = systemAPIConfigService;
        this.iLayerClient = iLayerClient;
        this.jsonDataConvertor = jsonDataConvertor;
    }

    public JSONObject getCustomerTrfDetail(Integer refSystemId, String productLineCode, String uuid) {
        SystemApiDTO importApiInfo = Optional.ofNullable(systemAPIConfigService.getImportApiInfo(refSystemId)).orElseThrow(() -> new IllegalStateException("没有找到获取详情配置"));
        String requestBodyTemplate = importApiInfo.getRequestBodyTemplate();
        Map<String, String> reqPramas = ImmutableMap.of(
                "trfNo", uuid,
                "productLineCode", productLineCode
        );
        BaseResponse baseResponse = iLayerClient.syncGetInfo(jsonDataConvertor.convert(JSONObject.toJSONString(reqPramas), requestBodyTemplate));
        if (!baseResponse.isSuccess()) {
            throw new IllegalStateException("获取详情失败 : " + baseResponse.getMessage());
        }
        return (JSONObject) baseResponse.getData();
    }

    public void reject(String mappingCode, String buCode, String uuid, String reason) {
        IlayerBody ilayerBody = new IlayerBody();
        ilayerBody.setObjectNumber(uuid);
        ilayerBody.setApplicationMappingCode(mappingCode);
        ilayerBody.setBu(buCode);
        Map<String, Object> body = ImmutableMap.of(
                "data", ImmutableList.of(
                        ImmutableMap.of(
                                "labTestUuid", uuid,
                                "reason", reason
                        )
                )
        );
        ilayerBody.setBody(body);
        iLayerClient.rejectInspectorio(ilayerBody);
    }

}
