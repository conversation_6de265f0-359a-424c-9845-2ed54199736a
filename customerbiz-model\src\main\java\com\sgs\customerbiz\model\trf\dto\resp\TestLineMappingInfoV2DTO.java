package com.sgs.customerbiz.model.trf.dto.resp;

import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingKey;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingRsp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TestLineMappingInfoV2DTO  extends CheckTestLineMappingRsp implements TestMappingKey {

    public static final String MAPPING_LEVEL_TL = "TL";

    public static final String MAPPING_LEVEL_TL_PLUS_C = "TL+Condition";

    private String itemName;
    private String packageNo;
    private String packageName;
    private String mappingLevel;
    private String checkMethod;
    private String checkMethodCode;

    private List<Condition> conditionList;

    @Data
    public static class Condition {
        private String conditionId;
        private String conditionName;
        private String conditionTypeId;
    }
}
