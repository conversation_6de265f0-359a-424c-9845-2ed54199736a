package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.facade.model.req.QueryTestLineMappingReq;
import com.sgs.customerbiz.facade.model.rsp.QueryTestLineMappingRsp;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingRsp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CustomerTestLineService {


    private final CustomerEntityConverter customerTestLineConverter;

    private final LocalILayerClient iLayerClient;


    public CustomerTestLineService(CustomerEntityConverter customerTestLineConverter, LocalILayerClient iLayerClient) {
        this.customerTestLineConverter = customerTestLineConverter;
        this.iLayerClient = iLayerClient;
    }

    public List<CustomerTestLine> testLines(String customerGroupCode, String buCode, String template, Collection<CustomerTrfInfoRsp> customerTrfInfoList) {

        return customerTrfInfoList.stream().flatMap(trfInfo -> this.convertTestLine(customerGroupCode, buCode,template, trfInfo)).collect(Collectors.toList());

    }

    private Stream<CustomerTestLine> convertTestLine(String customerGroupCode, String buCode, String template, CustomerTrfInfoRsp customerTrfInfo) {

        List<CustomerTestLineJSONObject> customerTestLineJSONObjects = customerTestLineConverter.customerTestLine(template, customerTrfInfo);
        List<String> testCodes = customerTestLineJSONObjects.stream()
                .filter(Objects::nonNull)
                .map(CustomerTestLineJSONObject::getItemNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<CustomerTestLineKey, List<TestLineMappingInfoV2DTO>> byCode = queryTestLineMapping(customerGroupCode, buCode, testCodes).stream()
                .collect(Collectors.groupingBy(respItem -> new CustomerTestLineKey(respItem.getItemCode(), respItem.getPackageNo())));

        return customerTestLineJSONObjects.stream()
                .flatMap(customerTestLineJSONObject -> {
                    List<TestLineMappingInfoV2DTO> testLineMappingInfoV2DTOS = Optional.ofNullable(customerTestLineJSONObject)
                            .filter(customerTestLine -> StringUtils.isNotBlank(customerTestLine.getItemNo()) && StringUtils.isNotBlank(customerTestLine.getPackageNo()))
                            .map(customerTestLine -> new CustomerTestLineKey(customerTestLine.getItemNo(), customerTestLine.getPackageNo()))
                            .map(byCode::get)
                            .orElse(Collections.emptyList());
                    if (CollectionUtils.isEmpty(testLineMappingInfoV2DTOS)) {
                        CustomerTestLine onlyCustomerTestLine = new CustomerTestLine(customerTestLineJSONObject, null);
                        return Stream.of(onlyCustomerTestLine);
                    }
                    return testLineMappingInfoV2DTOS.stream().map(mapping -> new CustomerTestLine(customerTestLineJSONObject, mapping));
                });
    }

    private List<TestLineMappingInfoV2DTO> queryTestLineMapping(String customerGroupCode, String buCode, List<String> testCodes) {
        QueryTestLineMappingReq checkTestLineMappingReq = new QueryTestLineMappingReq();
        checkTestLineMappingReq.setCustomerGroupCode(customerGroupCode);
        checkTestLineMappingReq.setProductLineCode(buCode);
        checkTestLineMappingReq.setPageIndex(1);
        checkTestLineMappingReq.setPageSize(1000);
        checkTestLineMappingReq.setTestCodes(testCodes);
        CustomResult<QueryTestLineMappingRsp> queryTestLineMappingRspCustomResult = iLayerClient.queryTestLineMapping(checkTestLineMappingReq);
        if (Func.isNotEmpty(queryTestLineMappingRspCustomResult) && Func.isNotEmpty(queryTestLineMappingRspCustomResult.getData()) && queryTestLineMappingRspCustomResult.isSuccess()) {
            QueryTestLineMappingRsp data = queryTestLineMappingRspCustomResult.getData();
            return data.getReturnList().stream()
                    .map(resp -> {
                        TestLineMappingInfoV2DTO testLineMappingInfoV2DTO = new TestLineMappingInfoV2DTO();
                        testLineMappingInfoV2DTO.setItemCode(resp.getTestCode());
                        testLineMappingInfoV2DTO.setItemName(resp.getTestName());
                        testLineMappingInfoV2DTO.setPackageNo(resp.getPackageNo());
                        testLineMappingInfoV2DTO.setPackageName(resp.getPackageName());
                        testLineMappingInfoV2DTO.setTestLineId(Integer.parseInt(resp.getTestlineId()));
                        testLineMappingInfoV2DTO.setPpNo(Integer.parseInt(resp.getPpNo()));
                        testLineMappingInfoV2DTO.setCitationId(Integer.parseInt(resp.getCitationId()));
                        testLineMappingInfoV2DTO.setCitationType(Integer.parseInt(resp.getCitationType()));
                        return testLineMappingInfoV2DTO;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
