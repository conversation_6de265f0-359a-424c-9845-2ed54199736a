# 目标

你的任务是根据Import2TRF这个流程的定义。
逐步解析每一个Component的代码。
分析每个Component内调用的其它类的方法。
分析的结果写入到docs/Import2TRF.md。

# 约束

    - 按Component的顺序书写
    - 先分析Component调用了哪些其它的类的方法，再一步一步的分析每个类调用的其它类
    - 分析调用要递归,直到碰到*Mapper的调用为止
    - 不要遗漏任何一个Component
    - 总结的逻辑要分1,2,3,4步骤

# 输出示例

```md
# Import2TRF

## importToTRFValidateComponent

### 调用的类和方法

- `TrfDomainService.selectActiveAndNotCancelByTrfNo(String trfNo)`
- `TrfDomainService.selectActiveAndNotCancelByTrfNos(List<String> trfNo)`
- `TrfDomainService.selectByTrfNos(List<String> trfNo)`
    1. 首先判断trfNo是否为空
    2. 构造查询条件
    3. 执行查询
    4. 将结果转换为TrfDTO
```
