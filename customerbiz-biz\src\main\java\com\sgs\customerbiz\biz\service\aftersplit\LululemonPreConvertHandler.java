package com.sgs.customerbiz.biz.service.aftersplit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.preview.PreviewSyncReportService;
import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfCompletedEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfReviewConclusionEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfUpdateInfoEvent;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.resp.TestLinePreviewDTO;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class LululemonPreConvertHandler  implements DataAfterSplitHandler {

    private final PreviewSyncReportService previewSyncReportService;

    public LululemonPreConvertHandler(PreviewSyncReportService previewSyncReportService) {
        this.previewSyncReportService = previewSyncReportService;
    }

    @Override
    public boolean acceptSubscriber(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        return Objects.equals(subscribe.getSubscriber(), RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
    }

    @Override
    public void processData(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        if( !(trfEvent instanceof TrfCompletedEvent) && !(trfEvent instanceof TrfUpdateInfoEvent) && !(trfEvent instanceof TrfReviewConclusionEvent)) {
            return;
        }

        TrfFullDTO data = JSONObject.parseObject(collectedData.toString(), TrfFullDTO.class);
        data.setProductLineCode(trfEvent.getProductLineCode());
        data.setBuCode(trfEvent.getProductLineCode());
        List<TestLinePreviewDTO> testLinePreviewDTOList = previewSyncReportService.preview(data);
        JSONArray testPackages = new JSONArray();
        JSONArray testProperties = new JSONArray();
        JSONArray testPropertyResults = new JSONArray();
        for (TestLinePreviewDTO preview : testLinePreviewDTOList) {
            if(preview.isNotMatched()) {
                continue;
            }
            createPackageObject(preview).ifPresent(testPackages::add);
            createTestLineObject(preview).ifPresent(testProperties::add);
            createTestLineResultObject(preview).ifPresent(testPropertyResults::add);
        }
        collectedData.put("testPackages", testPackages);
        collectedData.put("testProperties", testProperties);
        collectedData.put("testPropertyResults", testPropertyResults);
    }

    @NotNull
    private Optional<JSONObject> createPackageObject(TestLinePreviewDTO preview) {
        return AF.ap2(AF.lift2(this::createPackageObject),
                Optional.ofNullable(preview.getCustomerTestPackageId()),
                Optional.ofNullable(preview.getCustomerTestPackageName()));
    }

    private JSONObject createPackageObject(String testPackageId, String testPackageName) {
        JSONObject lululemonPackage = new JSONObject();
        lululemonPackage.put("id", testPackageId);
        lululemonPackage.put("name", testPackageName);
        return lululemonPackage;
    }

    @NotNull
    private Optional<JSONObject> createTestLineObject(TestLinePreviewDTO preview) {
        return AF.ap5(AF.lift5(this::createTestLineObject),
                Optional.ofNullable(preview.getCustomerTestPropertyId()),
                Optional.ofNullable(preview.getCustomerTestPropertyName()),
                Optional.ofNullable(preview.getCustomerCategoryId()),
                Optional.ofNullable(preview.getCustomerSubcategoryId()),
                Optional.ofNullable(preview.getCustomerTestPackageId()));
    }

    @NotNull
    private JSONObject createTestLineObject(String id, String name, String testCategoryId, String testSubCategoryId, String testPackageId) {
        JSONObject lululemonTestLine = new JSONObject();
        lululemonTestLine.put("id", id);
        lululemonTestLine.put("name", name);
        lululemonTestLine.put("testCategoryId", testCategoryId);
        lululemonTestLine.put("testSubCategoryId", testSubCategoryId);
        lululemonTestLine.put("testPackageId", testPackageId);
        return lululemonTestLine;
    }

    @NotNull
    private Optional<JSONObject> createTestLineResultObject(TestLinePreviewDTO preview) {
        return AF.ap2(AF.lift2(this::createTestLineResultObject),
                Optional.ofNullable(preview.getCustomerTestPropertyId()),
                Optional.ofNullable(preview.getResult()));
    }

    @NotNull
    private JSONObject createTestLineResultObject(String testPropertyId, String result) {
        JSONObject lululemonTestLine = new JSONObject();
        lululemonTestLine.put("testPropertyId", testPropertyId);
        lululemonTestLine.put("result", result);
        return lululemonTestLine;
    }

}
