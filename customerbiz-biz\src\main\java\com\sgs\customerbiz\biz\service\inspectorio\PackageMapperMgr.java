package com.sgs.customerbiz.biz.service.inspectorio;

import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.InspectorioPackageInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoPO;
import com.sgs.customerbiz.integration.dto.inspectorio.RevisableData;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * PackageInfo的Mapper管理器
 */
@Component
public class PackageMapperMgr implements InspectorioMapper<InspectorioPackageInfoPO, InspectorioPackageInfoExample> {

    private final InspectorioPackageInfoMapper packageInfoMapper;

    public PackageMapperMgr(InspectorioPackageInfoMapper packageInfoMapper) {
        this.packageInfoMapper = packageInfoMapper;
    }

    @Override
    public int countByExample(InspectorioPackageInfoExample example) {
        return packageInfoMapper.countByExample(example);
    }

    @Override
    public List<InspectorioPackageInfoPO> page(int offset, int limit) {
        return packageInfoMapper.page(offset, limit);
    }

    @Override
    public int batchInsert(List<InspectorioPackageInfoPO> list) {
        return packageInfoMapper.batchInsert(list);
    }

    @Override
    public int batchUpdate(List<InspectorioPackageInfoPO> list) {
        return packageInfoMapper.batchUpdate(list);
    }

    @Override
    public String getExternalId(InspectorioPackageInfoPO record) {
        return record.getExternalId();
    }

    @Override
    public String getData(InspectorioPackageInfoPO record) {
        return record.getData();
    }

    @Override
    public Integer getActiveIndicator(InspectorioPackageInfoPO record) {
        return record.getActiveIndicator();
    }

    @Override
    public Date getCreatedDate(InspectorioPackageInfoPO record) {
        return record.getCreatedDate();
    }

    @Override
    public InspectorioPackageInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum) {
        return data.toPackage(refSystemIdEnum);
    }

    @Override
    public InspectorioPackageInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, String datePattern) {
        return data.toPackage(refSystemIdEnum, datePattern);
    }

    @Override
    public InspectorioPackageInfoPO convertFromRevisableData(RevisableData data, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        return data.toPackage(refSystemIdEnum, datePatterns);
    }

    @Override
    public InspectorioPackageInfoPO createCopyForUpdate(InspectorioPackageInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum) {
        InspectorioPackageInfoPO updateRecord = newData.toPackage(refSystemIdEnum);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioPackageInfoPO createCopyForUpdate(InspectorioPackageInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, String datePattern) {
        InspectorioPackageInfoPO updateRecord = newData.toPackage(refSystemIdEnum, datePattern);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioPackageInfoPO createCopyForUpdate(InspectorioPackageInfoPO original, RevisableData newData, RefSystemIdEnum refSystemIdEnum, java.util.List<String> datePatterns) {
        InspectorioPackageInfoPO updateRecord = newData.toPackage(refSystemIdEnum, datePatterns);
        updateRecord.setId(original.getId());
        updateRecord.setCreatedDate(original.getCreatedDate());
        updateRecord.setModifiedDate(new Date());
        return updateRecord;
    }

    @Override
    public InspectorioPackageInfoPO createCopyForDelete(InspectorioPackageInfoPO original) {
        InspectorioPackageInfoPO deleteRecord = new InspectorioPackageInfoPO();
        deleteRecord.setId(original.getId());
        deleteRecord.setRefSystemId(original.getRefSystemId());
        deleteRecord.setExternalId(original.getExternalId());
        deleteRecord.setExternalName(original.getExternalName());
        deleteRecord.setRevision(original.getRevision());
        deleteRecord.setData(original.getData());
        deleteRecord.setCreatedDate(original.getCreatedDate());
        deleteRecord.setActiveIndicator(0);
        deleteRecord.setModifiedDate(new Date());
        return deleteRecord;
    }
} 