package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

@Component
public class UNIQLOGarmentFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3) {
        if(Func.isEmpty(arg1)) {
            return arg3;
        }
        return arg1.toString().equalsIgnoreCase("1:Garment") ? arg2: arg3;
    }

    @Override
    public String getName() {
        return "uniqloGarment";
    }

    @Override
    public String desc() {
        return "uniqloGarment";
    }
}
