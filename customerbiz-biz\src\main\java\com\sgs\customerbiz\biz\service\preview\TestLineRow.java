package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestLineRow {

    private boolean fromCustomerTrf;

    private boolean matched;

    private CustomerTestLineJSONObject customerTestLine;

    private TestLineMappingInfoV2DTO mappingInfo;

    private TrfTestLineDTO sgsTestLine;

    public static TestLineRow match(CustomerTestLine customerTestLine, SgsTestLine sgsTestLine) {
        return new TestLineRow(true, true, customerTestLine.getTestLine(), customerTestLine.getMapping(), sgsTestLine.getTestLine());
    }

    public static TestLineRow onlyCustomer(CustomerTestLine customerTestLine) {
        return new TestLineRow(true , false, customerTestLine.getTestLine(), customerTestLine.getMapping(), null);
    }

    public static TestLineRow onlySgs(SgsTestLine sgsTestLine) {
        Optional<String> itemNameOpt = Optional.ofNullable(sgsTestLine.getMapping()).map(TestLineMappingInfoV2DTO::getItemName);
        Optional<String> itemCodeOpt = Optional.ofNullable(sgsTestLine.getMapping()).map(TestLineMappingInfoV2DTO::getItemCode);
        CustomerTestLineJSONObject customerTestLineJSONObject = AF.ap2((itemName, itemCode) -> Optional.of(itemName + "（" + itemCode + "）"), itemNameOpt, itemCodeOpt)
                .map(CustomerTestLineJSONObject::ofTestProperty)
                .orElse(null);
        return new TestLineRow(false, false, customerTestLineJSONObject, sgsTestLine.getMapping(), sgsTestLine.getTestLine());
    }


}
