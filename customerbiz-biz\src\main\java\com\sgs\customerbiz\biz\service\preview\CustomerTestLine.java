package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.model.trf.dto.interfaces.TestMappingKey;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerTestLine implements TestMappingKey {

    private CustomerTestLineJSONObject testLine;

    private TestLineMappingInfoV2DTO mapping;

    @Override
    public Integer getTestLineId() {
        return mapping.getTestLineId();
    }

    @Override
    public Integer getPpNo() {
        return mapping.getPpNo();
    }

    @Override
    public Integer getCitationId() {
        return mapping.getCitationId();
    }

    @Override
    public Integer getCitationType() {
        return mapping.getCitationType();
    }

    public boolean hasMapping() {
        return mapping != null;
    }
}
