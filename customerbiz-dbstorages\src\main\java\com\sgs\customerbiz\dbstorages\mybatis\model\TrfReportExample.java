package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrfReportExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TrfReportExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNull() {
            addCriterion("trf_id is null");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNotNull() {
            addCriterion("trf_id is not null");
            return (Criteria) this;
        }

        public Criteria andTrfIdEqualTo(Long value) {
            addCriterion("trf_id =", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotEqualTo(Long value) {
            addCriterion("trf_id <>", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThan(Long value) {
            addCriterion("trf_id >", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThanOrEqualTo(Long value) {
            addCriterion("trf_id >=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThan(Long value) {
            addCriterion("trf_id <", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThanOrEqualTo(Long value) {
            addCriterion("trf_id <=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdIn(List<Long> values) {
            addCriterion("trf_id in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotIn(List<Long> values) {
            addCriterion("trf_id not in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdBetween(Long value1, Long value2) {
            addCriterion("trf_id between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotBetween(Long value1, Long value2) {
            addCriterion("trf_id not between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Integer value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Integer value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Integer value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Integer value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Integer value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Integer> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Integer> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Integer value1, Integer value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Integer value1, Integer value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Integer value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Integer value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Integer value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Integer value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Integer> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Integer> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(String value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(String value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(String value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(String value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(String value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(String value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLike(String value) {
            addCriterion("report_id like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotLike(String value) {
            addCriterion("report_id not like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<String> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<String> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(String value1, String value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(String value1, String value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoIsNull() {
            addCriterion("origin_report_no is null");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoIsNotNull() {
            addCriterion("origin_report_no is not null");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoEqualTo(String value) {
            addCriterion("origin_report_no =", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoNotEqualTo(String value) {
            addCriterion("origin_report_no <>", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoGreaterThan(String value) {
            addCriterion("origin_report_no >", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("origin_report_no >=", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoLessThan(String value) {
            addCriterion("origin_report_no <", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoLessThanOrEqualTo(String value) {
            addCriterion("origin_report_no <=", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoLike(String value) {
            addCriterion("origin_report_no like", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoNotLike(String value) {
            addCriterion("origin_report_no not like", value, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoIn(List<String> values) {
            addCriterion("origin_report_no in", values, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoNotIn(List<String> values) {
            addCriterion("origin_report_no not in", values, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoBetween(String value1, String value2) {
            addCriterion("origin_report_no between", value1, value2, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginReportNoNotBetween(String value1, String value2) {
            addCriterion("origin_report_no not between", value1, value2, "originReportNo");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNull() {
            addCriterion("report_status is null");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNotNull() {
            addCriterion("report_status is not null");
            return (Criteria) this;
        }

        public Criteria andReportStatusEqualTo(Integer value) {
            addCriterion("report_status =", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotEqualTo(Integer value) {
            addCriterion("report_status <>", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThan(Integer value) {
            addCriterion("report_status >", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_status >=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThan(Integer value) {
            addCriterion("report_status <", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThanOrEqualTo(Integer value) {
            addCriterion("report_status <=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusIn(List<Integer> values) {
            addCriterion("report_status in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotIn(List<Integer> values) {
            addCriterion("report_status not in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusBetween(Integer value1, Integer value2) {
            addCriterion("report_status between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("report_status not between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIsNull() {
            addCriterion("report_due_date is null");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIsNotNull() {
            addCriterion("report_due_date is not null");
            return (Criteria) this;
        }

        public Criteria andReportDueDateEqualTo(Date value) {
            addCriterion("report_due_date =", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotEqualTo(Date value) {
            addCriterion("report_due_date <>", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateGreaterThan(Date value) {
            addCriterion("report_due_date >", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("report_due_date >=", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateLessThan(Date value) {
            addCriterion("report_due_date <", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateLessThanOrEqualTo(Date value) {
            addCriterion("report_due_date <=", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIn(List<Date> values) {
            addCriterion("report_due_date in", values, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotIn(List<Date> values) {
            addCriterion("report_due_date not in", values, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateBetween(Date value1, Date value2) {
            addCriterion("report_due_date between", value1, value2, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotBetween(Date value1, Date value2) {
            addCriterion("report_due_date not between", value1, value2, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIsNull() {
            addCriterion("review_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIsNotNull() {
            addCriterion("review_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionEqualTo(String value) {
            addCriterion("review_conclusion =", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotEqualTo(String value) {
            addCriterion("review_conclusion <>", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionGreaterThan(String value) {
            addCriterion("review_conclusion >", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("review_conclusion >=", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLessThan(String value) {
            addCriterion("review_conclusion <", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLessThanOrEqualTo(String value) {
            addCriterion("review_conclusion <=", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLike(String value) {
            addCriterion("review_conclusion like", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotLike(String value) {
            addCriterion("review_conclusion not like", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIn(List<String> values) {
            addCriterion("review_conclusion in", values, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotIn(List<String> values) {
            addCriterion("review_conclusion not in", values, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionBetween(String value1, String value2) {
            addCriterion("review_conclusion between", value1, value2, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotBetween(String value1, String value2) {
            addCriterion("review_conclusion not between", value1, value2, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNull() {
            addCriterion("comments is null");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNotNull() {
            addCriterion("comments is not null");
            return (Criteria) this;
        }

        public Criteria andCommentsEqualTo(String value) {
            addCriterion("comments =", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotEqualTo(String value) {
            addCriterion("comments <>", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThan(String value) {
            addCriterion("comments >", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("comments >=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThan(String value) {
            addCriterion("comments <", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThanOrEqualTo(String value) {
            addCriterion("comments <=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLike(String value) {
            addCriterion("comments like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotLike(String value) {
            addCriterion("comments not like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsIn(List<String> values) {
            addCriterion("comments in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotIn(List<String> values) {
            addCriterion("comments not in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsBetween(String value1, String value2) {
            addCriterion("comments between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotBetween(String value1, String value2) {
            addCriterion("comments not between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagIsNull() {
            addCriterion("delivery_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagIsNotNull() {
            addCriterion("delivery_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagEqualTo(Integer value) {
            addCriterion("delivery_flag =", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagNotEqualTo(Integer value) {
            addCriterion("delivery_flag <>", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagGreaterThan(Integer value) {
            addCriterion("delivery_flag >", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_flag >=", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagLessThan(Integer value) {
            addCriterion("delivery_flag <", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_flag <=", value, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagIn(List<Integer> values) {
            addCriterion("delivery_flag in", values, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagNotIn(List<Integer> values) {
            addCriterion("delivery_flag not in", values, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagBetween(Integer value1, Integer value2) {
            addCriterion("delivery_flag between", value1, value2, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andDeliveryFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_flag not between", value1, value2, "deliveryFlag");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusIsNull() {
            addCriterion("customer_confirm_status is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusIsNotNull() {
            addCriterion("customer_confirm_status is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusEqualTo(String value) {
            addCriterion("customer_confirm_status =", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusNotEqualTo(String value) {
            addCriterion("customer_confirm_status <>", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusGreaterThan(String value) {
            addCriterion("customer_confirm_status >", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusGreaterThanOrEqualTo(String value) {
            addCriterion("customer_confirm_status >=", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusLessThan(String value) {
            addCriterion("customer_confirm_status <", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusLessThanOrEqualTo(String value) {
            addCriterion("customer_confirm_status <=", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusLike(String value) {
            addCriterion("customer_confirm_status like", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusNotLike(String value) {
            addCriterion("customer_confirm_status not like", value, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusIn(List<String> values) {
            addCriterion("customer_confirm_status in", values, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusNotIn(List<String> values) {
            addCriterion("customer_confirm_status not in", values, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusBetween(String value1, String value2) {
            addCriterion("customer_confirm_status between", value1, value2, "customerConfirmStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerConfirmStatusNotBetween(String value1, String value2) {
            addCriterion("customer_confirm_status not between", value1, value2, "customerConfirmStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}