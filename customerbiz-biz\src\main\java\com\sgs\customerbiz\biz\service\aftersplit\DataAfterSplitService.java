package com.sgs.customerbiz.biz.service.aftersplit;

import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DataAfterSplitService {

    private final List<DataAfterSplitHandler> afterSplitHandlers;


    public DataAfterSplitService(List<DataAfterSplitHandler> afterSplitHandlers) {
        this.afterSplitHandlers = afterSplitHandlers;
    }

    public void afterSplit(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        afterSplitHandlers.stream()
                .filter(handler -> handler.acceptSubscriber(subscribe, trfEvent, collectedData))
                .forEach(handler -> handler.processData(subscribe, trfEvent, collectedData));
    }
}
