import com.sgs.customerbiz.biz.service.inspectorio.GenericDatabaseOperations;
import com.sgs.customerbiz.biz.service.inspectorio.InspectorioDataBizService;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

/**
 * 测试InspectorioDataBizService的表格构建功能
 */
public class InspectorioDataBizServiceTest {

    @Test
    public void testBuildPackageUpdateTable() throws Exception {
        InspectorioDataBizService service = new InspectorioDataBizService();
        
        // 创建测试数据
        InspectorioPackageInfoPO updatePkg = new InspectorioPackageInfoPO();
        updatePkg.setExternalId("PKG001");
        updatePkg.setExternalName("Test Package 1");
        
        InspectorioPackageInfoPO deletePkg = new InspectorioPackageInfoPO();
        deletePkg.setExternalId("PKG002");
        deletePkg.setExternalName("Test Package 2");
        
        InspectorioPackageInfoPO insertPkg = new InspectorioPackageInfoPO();
        insertPkg.setExternalId("PKG003");
        insertPkg.setExternalName("Test Package 3");
        
        GenericDatabaseOperations<InspectorioPackageInfoPO> packageOps = 
            new GenericDatabaseOperations<>(
                Arrays.asList(insertPkg),
                Arrays.asList(updatePkg),
                Arrays.asList(deletePkg)
            );
        
        // 使用反射调用私有方法
        Method method = InspectorioDataBizService.class.getDeclaredMethod(
            "buildPackageUpdateTable", GenericDatabaseOperations.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(service, packageOps);
        
        System.out.println("Package Update Table:");
        System.out.println(result);
        
        // 验证结果包含预期内容
        assert result.contains("Package update list:");
        assert result.contains("PKG001\tTest Package 1\tUpdated");
        assert result.contains("PKG002\tTest Package 2\tDelete");
        assert result.contains("PKG003\tTest Package 3\tNew");
    }

    @Test
    public void testBuildPropertyUpdateTable() throws Exception {
        InspectorioDataBizService service = new InspectorioDataBizService();
        
        // 创建测试数据
        InspectorioTestLineInfoPO updateTestLine = new InspectorioTestLineInfoPO();
        updateTestLine.setExternalId("PROP001");
        updateTestLine.setExternalName("Test Property 1");
        
        InspectorioTestLineInfoPO deleteTestLine = new InspectorioTestLineInfoPO();
        deleteTestLine.setExternalId("PROP002");
        deleteTestLine.setExternalName("Test Property 2");
        
        InspectorioTestLineInfoPO insertTestLine = new InspectorioTestLineInfoPO();
        insertTestLine.setExternalId("PROP003");
        insertTestLine.setExternalName("Test Property 3");
        
        GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOps = 
            new GenericDatabaseOperations<>(
                Arrays.asList(insertTestLine),
                Arrays.asList(updateTestLine),
                Arrays.asList(deleteTestLine)
            );
        
        // 使用反射调用私有方法
        Method method = InspectorioDataBizService.class.getDeclaredMethod(
            "buildPropertyUpdateTable", GenericDatabaseOperations.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(service, testLineOps);
        
        System.out.println("Property Update Table:");
        System.out.println(result);
        
        // 验证结果包含预期内容
        assert result.contains("Property update list:");
        assert result.contains("PROP001\tTest Property 1\tUpdated");
        assert result.contains("PROP002\tTest Property 2\tDelete");
        assert result.contains("PROP003\tTest Property 3\tNew");
    }

    @Test
    public void testEmptyOperations() throws Exception {
        InspectorioDataBizService service = new InspectorioDataBizService();
        
        // 测试空操作
        GenericDatabaseOperations<InspectorioPackageInfoPO> emptyPackageOps = 
            new GenericDatabaseOperations<>(
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList()
            );
        
        Method packageMethod = InspectorioDataBizService.class.getDeclaredMethod(
            "buildPackageUpdateTable", GenericDatabaseOperations.class);
        packageMethod.setAccessible(true);
        
        String packageResult = (String) packageMethod.invoke(service, emptyPackageOps);
        
        System.out.println("Empty Package Table:");
        System.out.println(packageResult);
        
        // 验证空操作时的输出
        assert packageResult.contains("Package update list:");
        assert packageResult.contains(" \t \t ");
    }
}
