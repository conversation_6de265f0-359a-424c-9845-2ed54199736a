package com.sgs.customerbiz.biz.service.todolist;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.biz.service.inspectorio.InspectorioBizService;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.core.common.KafkaProducer;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.*;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.BoundTrfRelExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfOrderRelationshipExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.*;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.service.strategy.TRFCancelTrfService;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.RemoveCancelItemRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.RemoveCancelRsp;
import com.sgs.customerbiz.facade.model.trf.req.CustomerTrfInfoReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.PreOrderClient;
import com.sgs.customerbiz.model.trf.dto.req.TrfCancelReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfRemoveReq;
import com.sgs.customerbiz.model.trf.dto.resp.TrfCancelResult;
import com.sgs.customerbiz.model.trf.dto.resp.TrfReturnResult;
import com.sgs.customerbiz.model.trf.enums.IdentityEnum;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.order.SyncOrderStatusInfo;
import com.sgs.framework.tool.utils.Func;
import com.sgs.grus.kafka.client.MessageReq;
import com.sgs.preorder.facade.model.dto.order.DataDictionary;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.trf.CancelOrderDTO;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class TrfService {
    private static final Logger logger = LoggerFactory.getLogger(TrfService.class);
    @Resource
    private TrfInfoExtMapper trfInfoExtMapper;
    @Resource
    private BoundTrfRelExtMapper boundTrfRelExtMapper;
    @Autowired
    private KafkaProducer kafkaProducer;
    @Autowired
    private TokenUtils tokenUtils;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private TRFCancelTrfService trfCancelTrfService;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Resource
    private TrfInfoMapper trfInfoMapper;

    @Autowired
    private TrfUnDisplayMapper trfUnDisplayMapper;

    @Resource
    private TrfOrderRelationshipExtMapper orderRelationshipExtMapper;
    @Autowired
    private SciTrfBizService sciTrfBizService;

    @Autowired
    private InspectorioBizService inspectorioBizService;

    @Autowired
    private TrfDomainService trfDomainService;

    public CustomResult<?> rejectInspectorio(RejectInspectorio reject) {
        CustomResult<?> resp = new CustomResult<>(true);
        if(StringUtils.isNotBlank(reject.getTrfNo())){
            String trfNo = reject.getTrfNo();
            List<TrfInfoPO> selectByTrfNo = trfDomainService.selectActiveByTrfNo(trfNo);
            if(!CollectionUtils.isEmpty(selectByTrfNo)){
                return resp.fail("Cannot reject inspectorio, TRF is currently being processed in SGS");
            }
        }
        
        JSONObject customerTrfDetail = inspectorioBizService.getCustomerTrfDetail(reject.getRefSystemId(), reject.getProductLineCode(), reject.getUuid());
        String status = Optional.ofNullable(customerTrfDetail)
                .filter(detailResp -> detailResp.containsKey("detail"))
                .map(detailResp -> detailResp.getJSONObject("detail"))
                .filter(detail -> detail.containsKey("status"))
                .map(detail -> detail.getString("status"))
                .orElseThrow(() -> new IllegalStateException("details not contains 'status' From inspectorio"));

        switch (status) {
            case "confirmed":
               String rejectMappingCode = getRejectMappingCode(reject.getRefSystemId());
               inspectorioBizService.reject(rejectMappingCode, reject.getProductLineCode(), reject.getUuid(), reject.getReasonContent());
               cancelTrfIfTrfNoPresent(reject);
               return resp;
            case "in-progress":
               String abortMappingCode = getAbortMappingCode(reject.getRefSystemId());
               inspectorioBizService.reject(abortMappingCode, reject.getProductLineCode(), reject.getUuid(), reject.getReasonContent());
                cancelTrfIfTrfNoPresent(reject);
               return resp;
            default:
                return resp.fail("inspectorio status is not confirmed or in-progress");
        }
    }

    public void cancelTrfIfTrfNoPresent(RejectInspectorio reject) {
        if(StringUtils.isBlank(reject.getTrfNo())) {
            return;
        }
        TrfCancelReq trfCancelReq = new TrfCancelReq();
        trfCancelReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        trfCancelReq.setTrfNo(reject.getTrfNo());
        trfCancelReq.setRefSystemId(reject.getRefSystemId());
        trfCancelReq.setCancelReason(reject.getReasonContent());
        trfCancelReq.setCancelType(reject.getReasonType());
        trfCancelReq.setSystemId(SgsSystem.SgsMart.getSgsSystemId());
        sciTrfBizService.cancelTrf(trfCancelReq, IdentityEnum.PREORDER);
    }

    /**
     * 根据RefSystemId获取Reject操作的MappingCode
     * @param refSystemId 系统ID
     * @return mappingCode
     */
    private String getRejectMappingCode(Integer refSystemId) {
        if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO)) {
            return "RejectInspectorioOrder";
        } else if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.LULULEMON_INSPECTORIO)) { // TODO: 替换为RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId()当枚举可用时
            return "RejectLululemonOrder";
        } else {
            return "RejectInspectorioOrder"; // 默认值
        }
    }

    /**
     * 根据RefSystemId获取Abort操作的MappingCode
     * @param refSystemId 系统ID
     * @return mappingCode
     */
    private String getAbortMappingCode(Integer refSystemId) {
        if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO)) {
            return "AbortInspectorioOrder";
        } else if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.LULULEMON_INSPECTORIO)) { // TODO: 替换为RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId()当枚举可用时
            return "AbortLululemonOrder";
        } else {
            return "AbortInspectorioOrder"; // 默认值
        }
    }


    /**
     * Remove Trf
     *
     * @param reqObject
     * @return
     */
    public CustomResult returnTrf(RemoveTrfReq reqObject) {
        CustomResult rspResult = new CustomResult();

        CustomerTrfInfoRsp oldTrf = trfInfoExtMapper.getTrfInfoById(reqObject.getRefSystemId(), reqObject.getTrfId());
        if (oldTrf == null) {
            return rspResult.fail(String.format("未找到对应的TrfNo(%s)信息！", reqObject.getTrfId()));
        }

//        if (!TrfStatusEnum.check(oldTrf.getTrfStatus(), TrfStatusEnum.ToBeBound) && !isSwitchSCI) {
//            return rspResult.fail(String.format("当前TrfNo(%s)状态为%s，无法Remove.", oldTrf.getTrfNo(), TrfStatusEnum.getText(oldTrf.getTrfStatus())));
//        }
//        if (NumberUtil.equals(oldTrf.getActiveIndicator(), 0) && !isSwitchSCI) {
//            return rspResult.fail(String.format("当前TrfNo(%s)状态已Remove.", oldTrf.getTrfNo()));
//        }
        CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
        trf.setId(oldTrf.getId());
        // 0: inactive, 1: active
        trf.setActiveIndicator(0);
        trf.setModifiedBy(UserHelper.getRegionAccount());
        trf.setModifiedDate(DateUtils.getNow());

        // SCI  remove
        TrfRemoveReq trfRemoveReq = new TrfRemoveReq();
        trfRemoveReq.setTrfNo(oldTrf.getTrfNo());
        trfRemoveReq.setRefSystemId(reqObject.getRefSystemId());
        trfRemoveReq.setReturnReason(reqObject.getReasonContent());
        trfRemoveReq.setReturnType(reqObject.getReasonType());
        trfRemoveReq.setLabCode(oldTrf.getLabCode());
        trfRemoveReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        trfRemoveReq.setSystemId(ProductLineType.check(ProductLineContextHolder.getProductLineId(), ProductLineType.SL) ?
                SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());

        Integer execute = transactionTemplate.execute(transactionStatus -> {

            TrfReturnResult trfReturnResult = sciTrfBizService.returnTrf(trfRemoveReq, IdentityEnum.PREORDER);
//            int i = trfInfoExtMapper.updateActiveIndicator(trf);

//            i = i | customerTrfReasonRelationshipInfoMapper.insert(relationshipInfoPO);

//            transactionStatus.setRollbackOnly();
            return 1;
        });
        if (execute >= 0) {
            trfCancelTrfService.sendMQToGPO(reqObject);
        }
        //组装MQ消息体 给到GPO
        rspResult.setSuccess(execute >= 0);
        return rspResult;
    }

    /**
     * Cancel Trf
     *
     * @param reqObject
     * @return
     */
    public CustomResult cancelTrf(CancelTrfReq reqObject) {
        CustomResult rspResult = new CustomResult();

        // TODO 每迁移一个客户就要check一下
        boolean isSwitchSCI = RefSystemIdEnum.check(reqObject.getRefSystemId(),
                RefSystemIdEnum.TIC, RefSystemIdEnum.Shein, RefSystemIdEnum.SGSMart,RefSystemIdEnum.Septwolves,RefSystemIdEnum.UNIQLO,RefSystemIdEnum.F21,
                RefSystemIdEnum.JO_ANN, RefSystemIdEnum.BigLots, RefSystemIdEnum.Walmart,RefSystemIdEnum.Walmart_Group,
                RefSystemIdEnum.DollarTree,RefSystemIdEnum.Veyer,RefSystemIdEnum.TARGET_INSPECTORIO,RefSystemIdEnum.LULULEMON_INSPECTORIO);

        String reason = reqObject.getReason();
        String reasonContent = reqObject.getReasonContent();
        if (Func.isEmpty(reason) || Func.isEmpty(reasonContent)) {
            return rspResult.fail("Cancel TRF Reason cannot be null!");
        }

        if (StringUtil.checkLen(reqObject.getReasonContent(), 250)) {
            return rspResult.fail("取消原因不能超过限制250字符.");
        }
        CustomerTrfInfoRsp oldTrf = trfInfoExtMapper.getTrfInfoById(reqObject.getRefSystemId(), reqObject.getTrfId());
        if (oldTrf == null) {
            return rspResult.fail(String.format("未找到对应的TrfNo(%s)信息！", reqObject.getTrfId()));
        }
        if (TrfStatusEnum.check(oldTrf.getTrfStatus(), TrfStatusEnum.Canceled)) {
            return rspResult.fail(String.format("当前TrfNo(%s)状态已取消.", oldTrf.getTrfNo()));
        }
        if (!TrfStatusEnum.check(oldTrf.getTrfStatus(), TrfStatusEnum.ToBeTested)&& !isSwitchSCI) {
            return rspResult.fail(String.format("当前TrfNo(%s)状态为%s，无法Cancel.", oldTrf.getTrfNo(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getTextEn(oldTrf.getTrfStatus())));
        }
        if (NumberUtil.equals(oldTrf.getActiveIndicator(), 0)&& !isSwitchSCI) {
            return rspResult.fail(String.format("当前TrfNo(%s)状态已Remove.", oldTrf.getTrfNo()));
        }
        String trfNo = oldTrf.getTrfNo();
        String regionAccount = UserHelper.getRegionAccount();
        CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
        trf.setId(oldTrf.getId());
        trf.setRefSystemId(oldTrf.getRefSystemId());
        trf.setProductLineCode(oldTrf.getProductLineCode());
        trf.setLabCode(oldTrf.getLabCode());
        trf.setTrfNo(trfNo);
        trf.setCancelReason(reqObject.getReasonContent());
        trf.setTrfStatus(TrfStatusEnum.Canceled.getStatus());
        trf.setModifiedBy(regionAccount);
        trf.setModifiedDate(DateUtils.getNow());

        /*// 解绑
        BoundTrfRelationshipPO unBindTrf = new BoundTrfRelationshipPO();
        unBindTrf.setRefSystemId(reqObject.getRefSystemId());
        unBindTrf.setTrfNo(trfNo);
        unBindTrf.setBoundStatus(BoundStatus.UnBound.getType());
        unBindTrf.setModifiedBy(UserHelper.getRegionAccount());
        unBindTrf.setModifiedDate(DateUtils.getNow());*/

        List<String> trfNos = Lists.newArrayList();
        trfNos.add(trfNo);


        List<String> orderIdList = null;
//        if (isSwitchSCI) {
        List<BoundTrfRelDTO> orderInfos = orderRelationshipExtMapper.getBoundTrfInfoList(trfNos, BoundStatus.BoundHasOrder.getType());
        if (!CollectionUtils.isEmpty(orderInfos)) {
            orderIdList = orderInfos.stream().map(BoundTrfRelDTO::getOrderId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }
//        } else {
//            List<BoundTrfRelationshipPO> boundTrfs = boundTrfRelExtMapper.getBoundTrfInfoList(trfNos, BoundStatus.BoundHasOrder.getType());
//            if (boundTrfs == null || boundTrfs.isEmpty()) {
//                return rspResult.fail("Trf 未绑定有订单.");
//            }
//            orderId = boundTrfs.stream().findFirst().get().getOrderId();
//        }

        List<OrderInfoDto> orderInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderIdList)) {
            for (String orderId : orderIdList) {
                OrderInfoDto orderInfo = preOrderClient.getOrderInfo(orderId);
                if (orderInfo == null) {
                    logger.warn("Trf绑定的订单不存在. TrfNo: {}, OrderId: {}", trfNo, orderId);
                    continue;
//                    return rspResult.fail("Trf 绑定的订单不存在.");
                }
                Integer orderStatus = orderInfo.getOrderStatus();
                if (OrderStatusEnum.checkStatus(orderStatus, OrderStatusEnum.Completed, OrderStatusEnum.Closed)) {
                    logger.warn("Order Status [{}] was not allowed cancellation. OrderId: {}", OrderStatusEnum.getMessage(orderStatus), orderId);
                    continue;
//                    return rspResult.fail(String.format("Order Status cannot be %s!", OrderStatusEnum.getMessage(orderStatus)));
                }
                orderInfoList.add(orderInfo);
            }
            if (CollUtil.isEmpty(orderInfoList)) {
                logger.error("Trf不存在可操作取消的订单. TrfNo: {}, OrderIdList: {}", trfNo, JSON.toJSONString(orderIdList));
                return rspResult.fail("Trf不存在可操作取消的订单.");
            }
        }

        List<CancelOrderDTO> cancelOrderDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderInfoList)) {
            if (isSwitchSCI) {
                for (OrderInfoDto orderInfo : orderInfoList) {
                    CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                    cancelOrderDTO.setId(orderInfo.getID());
                    cancelOrderDTO.setOrderNo(orderInfo.getOrderNo());
                    cancelOrderDTO.setOrderStatus(OrderStatusEnum.Cancelled.getStatus());
                    cancelOrderDTO.setReasonType(String.valueOf(reqObject.getReasonType()));
                    cancelOrderDTO.setRemark(reqObject.getReasonContent());
                    cancelOrderDTO.setEventType(EventTypeEnum.CancelOrder.getType());
                    cancelOrderDTO.setSgsToken(tokenUtils.getToken());
                    // 需要调用 SODA的 cancel Order 接口
                    if (OrderStatusEnum.checkStatus(orderInfo.getOrderStatus(), OrderStatusEnum.Cancelled)) {
                        logger.warn("Order has already been cancelled. OrderNo: {}", orderInfo.getOrderNo());
                        continue;
//                    return rspResult.fail(String.format("Order Status cannot be %s!", OrderStatusEnum.getMessage(orderInfo.getOrderStatus())));
                    }
                    cancelOrderDTOList.add(cancelOrderDTO);
                }
                if (CollUtil.isEmpty(cancelOrderDTOList)) {
                    logger.error("Trf不存在可操作取消的订单. TrfNo: {}, OrderIdList: {}", trfNo, JSON.toJSONString(orderIdList));
                    return rspResult.fail("Trf不存在可操作取消的订单.");
                }
            }
        }

//        CustomerTrfReasonRelationshipInfoPO relationshipInfoPO = new CustomerTrfReasonRelationshipInfoPO();
//        relationshipInfoPO.setTrfInfoId(reqObject.getTrfId());
//        relationshipInfoPO.setReason(reqObject.getReason());
//        relationshipInfoPO.setReasonContent(reqObject.getReasonContent());
//        relationshipInfoPO.setReasonType(2);
//        relationshipInfoPO.setCreatedBy(UserHelper.getRegionAccount());
//        relationshipInfoPO.setCreatedDate(DateUtils.getNow());

        // TIC cancel
        TrfCancelReq trfCancelReq = new TrfCancelReq();
        trfCancelReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        trfCancelReq.setTrfNo(oldTrf.getTrfNo());
        trfCancelReq.setRefSystemId(reqObject.getRefSystemId());
        trfCancelReq.setCancelReason(reqObject.getReasonContent());
        trfCancelReq.setCancelType(reqObject.getReasonType());
        trfCancelReq.setLabCode(oldTrf.getLabCode());
        trfCancelReq.setSystemId(ProductLineType.check(ProductLineContextHolder.getProductLineId(), ProductLineType.SL) ?
                SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());
        trfCancelReq.setSamplePhotos(reqObject.getSamplePhotos());
        trfCancelReq.setMaterialItem(reqObject.getMaterialItem());
        TrfCancelResult trfCancelResult = sciTrfBizService.cancelTrf(trfCancelReq, IdentityEnum.PREORDER);

        if (isSwitchSCI && CollUtil.isNotEmpty(cancelOrderDTOList)) {
            // 需要调用 SODA的 cancel Order 接口
            // TODO 走一次性批量cancel
            for (CancelOrderDTO cancelOrderDTO : cancelOrderDTOList) {
                boolean result = preOrderClient.cancelOrder(cancelOrderDTO);
                if (!result) {
                    logger.error("订单Cancel 失败，OrderNo{}", cancelOrderDTO.getOrderNo());
                }
            }
        }

        Integer execute = transactionTemplate.execute(transactionStatus -> {
            int i = trfInfoExtMapper.updateTrfStatus(trf);
            // 页面要显示cancel的
            // i = i | boundTrfRelExtMapper.unBindTrf(unBindTrf);
//            i = i | customerTrfReasonRelationshipInfoMapper.insert(relationshipInfoPO);

            if (i < 0) {
                transactionStatus.setRollbackOnly();
            }

            return i;
        });
        if (execute >= 0) {
            this.doSend(trf, regionAccount);
            //组装MQ消息体 给到GPO
            trfCancelTrfService.sendMQToGPO(reqObject);
        }
        rspResult.setSuccess(execute >= 0);
        return rspResult;
    }

    public CustomResult<?> unDisplayTrf(TrfDisplayReq reqObject) {
        TrfUnDisplayPO unDisplayPO = new TrfUnDisplayPO();
        unDisplayPO.setTrfId(reqObject.getTrfId());
        unDisplayPO.setSystemId(reqObject.getSystemId());
        try {
            trfUnDisplayMapper.insert(unDisplayPO);
        } catch (DuplicateKeyException e) {
            // ignore
            logger.warn("trf was unDisplayed : {}, systemId: {}", reqObject.getTrfId(), reqObject.getSystemId());
        }
        return CustomResult.newSuccessInstance().data(reqObject);
    }

    public CustomResult<?> displayTrf(TrfDisplayReq reqObject) {
        trfUnDisplayMapper.deleteByPrimaryKey(reqObject.getTrfId(), reqObject.getSystemId());
        return CustomResult.newSuccessInstance().data(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult<List<CustomerTrfInfoRsp>> getTrfInfoList(CustomerTrfInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();

        List<CustomerTrfInfoRsp> trfs = trfInfoExtMapper.getTrfInfoList(reqObject.getTrfNos(), false, null);
//        Map<Integer, String> trfReasonMaps = Maps.newHashMap();
//        if (!trfs.isEmpty()) {
//            Set<Integer> trfIds = trfs.stream().map(trf -> trf.getId()).collect(Collectors.toSet());
//            trfInfoExtMapper.getTrfReasonInfoList(trfIds).forEach(trf -> {
//                if (trfReasonMaps.containsKey(trf.getTrfInfoId())) {
//                    return;
//                }
//                trfReasonMaps.put(trf.getTrfInfoId(), trf.getReasonContent());
//            });
//        }
//        trfs.forEach(trf -> {
//            trf.setCancelReason(trfReasonMaps.get(trf.getId()));
//        });
        rspResult.setSuccess(!trfs.isEmpty());
        rspResult.setData(trfs);
        return rspResult;
    }

    public List<CustomerTrfInfoRsp> getCustomerTrfInfoList(List<String> trfNos, List<String> packageBarcodes) {
        if (CollectionUtils.isEmpty(trfNos) && CollectionUtils.isEmpty(packageBarcodes)) {
            return Lists.newArrayList();
        }
        return trfInfoExtMapper.getTrfInfoList(trfNos, true, packageBarcodes);
    }

    /**
     * @param trf
     * @param regionAccount
     */
    private void doSend(CustomerTrfInfoPO trf, String regionAccount) {
        if (trf == null) {
            return;
        }
        Set<String> orderIds = boundTrfRelExtMapper.getBoundTrfByOrderIds(trf.getRefSystemId(), trf.getTrfNo());
        if (orderIds == null || orderIds.isEmpty()) {
            return;
        }
        MessageReq reqObject = new MessageReq();
        reqObject.setAction(EventTypeEnum.CancelTrf.toString());
        reqObject.setProductLineCode(trf.getProductLineCode());
        reqObject.setLabCode(trf.getLabCode());
        reqObject.setUserName(regionAccount);
        reqObject.setSgsToken(tokenUtils.getToken());

        SyncOrderStatusInfo syncOrder = new SyncOrderStatusInfo();
        syncOrder.setOrderIds(orderIds);
        syncOrder.setRefSystemId(trf.getRefSystemId());
        syncOrder.setTrfNo(trf.getTrfNo());
        syncOrder.setOrderStatus(OrderStatusEnum.Cancelled.getStatus());
        syncOrder.setRemark(trf.getCancelReason());
        syncOrder.setReasonType(ReasonTypeEnum.ClientRequired.getKey());
        reqObject.setData(JSON.toJSONString(syncOrder));

        kafkaProducer.doSend(trf.getTrfNo(), reqObject);
    }

    public CustomResult<RemoveCancelRsp> getRemoveCancelData(RemoveCancelReq reqObject) {
        CustomResult<RemoveCancelRsp> result = new CustomResult<>();
        if (reqObject == null || NumberUtil.toLong(reqObject.getTrfId()) == 0) {
            return result.fail("Parameter error");
        }
        CustomerTrfInfoRsp trfInfoById = trfInfoExtMapper.getTrfInfoByTrfId(reqObject.getTrfId());
        if (trfInfoById == null) {
            return result.fail("Parameter error");
        }
        String trfNo = trfInfoById.getTrfNo();
        Integer refSystemId = trfInfoById.getRefSystemId();
        boolean isSwitchSCI = RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TIC);

        RemoveCancelRsp removeCancelRsp = new RemoveCancelRsp();
        String type = reqObject.getType();

        if ("cancel".equalsIgnoreCase(type)) {

            String orderNo = StringUtils.EMPTY;
//            if (!isSwitchSCI) {
//                SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
//                searchTrfInfoDTO.setTrfId(reqObject.getTrfId());
//                BoundTrfRelDTO relDTO = boundTrfRelExtMapper.queryBoundTrfInfoByTrfId(searchTrfInfoDTO);
//                if (relDTO != null && StringUtils.isNotBlank(relDTO.getOrderNo())) {
//                    orderNo = relDTO.getOrderNo();
//                }
//            } else {
            // TIC 暂没有一对多的场景  先只canncel 一条
            List<BoundTrfRelDTO> orderInfo = orderRelationshipExtMapper.getBoundTrfInfoList(Lists.newArrayList(trfNo), BoundStatus.BoundHasOrder.getType());
            if (!CollectionUtils.isEmpty(orderInfo)) {
                BoundTrfRelDTO trfRelDTO = orderInfo.stream().findFirst().orElse(null);
                if (trfRelDTO != null) {
                    orderNo = trfRelDTO.getOrderNo();
                }
            }
//            }
            if (StringUtils.isNotBlank(orderNo)) {
                removeCancelRsp.setOrderNo(orderNo);
                OrderSimplifyInfoRsp rsp = preOrderClient.getPreorderInfo(orderNo);
                String statusText = OrderStatusEnum.getMessage(rsp.getOrderStatus());
                removeCancelRsp.setOrderStatus(statusText);
            }
        }
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        //获取原因下拉框
        removeCancelRsp.setTrfId(reqObject.getTrfId());
        //调用comonservice接口 获取reason相关数据
        List<DataDictionary> list = frameWorkClient.getDataDictionaryList(Constants.TODO_List_Reason_List, productLineCode);
        List<RemoveCancelItemRsp> rl = Lists.newArrayList();
        list.forEach(l -> {
            RemoveCancelItemRsp itemRsp = new RemoveCancelItemRsp();
            itemRsp.setReasonType(NumberUtil.toInt(l.getSysKey()));
            itemRsp.setReason(l.getSysValue());
            rl.add(itemRsp);
        });
        removeCancelRsp.setReasonList(rl);
        result.setSuccess(true);
        result.setData(removeCancelRsp);
        return result;
    }

//
//    /**
//     * 创建TRF
//     *
//     * @param reqObject
//     * @return
//     */
//    public TrfDO createTrf(
////            @Validated(TrfCreateGroup.class)
//            TrfDO reqObject) {
//        TrfInfoPO trfInfoPO = getTrfInfo(reqObject.getTrfNo(), reqObject.getRefSystemId());
//        if (Func.isNotEmpty(trfInfoPO)) {
//            // 当前如果状态为NEW将trf标准结构返回，如果是其他状态直接报错
//            if (TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.ToBeBound)) {
//                return getTrfDetail(trfInfoPO.getTrfNo(),reqObject.getRefSystemId());
//            } else {
//                throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "当前TRF NO：" + trfInfoPO.getTrfNo() + "已开单，无法操作！");
//            }
//        }
//        // ID生成
//        long trfId = idService.nextId();
//        reqObject.setTrfId(trfId);
//        // 组装数据
//        TrfCreateBean createBean = trfAssembler.assemblerCreated(reqObject);
//        // 执行统一DB操作
//        transactionTemplate.execute((trans) -> {
//            // 暂时需将原始数据存到customerTrf表中
//            this.saveCustomerTrfInfo(reqObject);
//
//            productService.saveProductList(trfId, reqObject.getProductList(), reqObject.getSampleList());
//
//            trfServiceRequirementService.saveServiceRequirement(trfId, reqObject.getServiceRequirement());
//
//            trfTestItemService.saveTestItemList(trfId, reqObject.getTestItemList());
//
//            trfCareLabelService.saveCareLabelList(trfId, reqObject.getCareLabelList());
//
//            trfFileService.saveFileList(trfId, FileTypeEnum.Attachment.getType(), reqObject.getAttachmentList());
//
//            trfCustomerService.saveCustomerList(trfId, reqObject.getCustomerList());
//            // 保存创建
//            this.saveCreateTrfInfo(createBean);
//            return true;
//        });
//        return trfAssembler.assemblerResultTrfDO(createBean);
//    }
//
//    /**
//     * Complete
//     *
//     * @param reqObject
//     * @return
//     */
//    public TrfDO complete(TrfDO reqObject) {
//        TrfInfoPO trfInfoPO = getTrfAndCheck(reqObject);
//        // 规则校验
////        if (!TrfStatusNodes.trfCompletedNodeChecked(trfInfoPO)) {
////            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "当前TRF NO为：" + reqObject.getTrfNo() + "无法扭转状态！");
////        }
//        // 执行DB操作
//        transactionTemplate.execute((trans) -> {
//            reportService.saveReportList(trfInfoPO.getId(), reqObject.getReportList());
//            // Quotation
//            quotationService.batchSave(trfInfoPO.getId(), reqObject.getQuotationList());
//            // 变更trf状态为complete
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.Detected);
//            return true;
//        });
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    public TrfDO close(
////            @Validated(TrfCloseGroup.class)
//            TrfDO reqObject) {
//        TrfInfoPO trfInfo = getTrfInfo(reqObject.getTrfNo(), reqObject.getRefSystemId());
//        if (trfInfo == null) {
//            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "当前TRF NO为：" + reqObject.getTrfNo() + "不存在！");
//        }
//        // 规则校验
//        if (!TrfStatusNodes.trfCloseRuleChecked(trfInfo)) {
//            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "当前TRF NO为：" + reqObject.getTrfNo() + "无法扭转状态！");
//        }
//        // 执行统一DB操作
//        transactionTemplate.execute((trans) -> {
//            //  保存invoice信息
//            invoiceService.saveInvoiceList(reqObject.getInvoiceList(), trfInfo.getId());
//            // trf状态更新
//            this.updateTrfStatus(trfInfo, TrfStatusEnum.Completed);
//            return true;
//        });
//        return getResultTrfDO(trfInfo);
//    }
//
//
//    /**
//     * 根据TrfNo 查询Trf详细信息
//     *
//     * @param trfNo
//     * @return
//     */
//    public TrfDO getTrfDetail(String trfNo,Integer refSystemId) {
//        TrfDO trfDO = new TrfDO();
//
//        TrfInfoPO trfInfoPO = getTrfInfoByTrfNo(trfNo,refSystemId);
//        if (Func.isEmpty(trfInfoPO)) {
//            return null;
//        }
//        trfDO.setTrfId(trfInfoPO.getId());
//        trfDO.setTrfStatus(trfInfoPO.getStatus());
//        BeanUtils.copyProperties(trfInfoPO, trfDO);
//        // 设置TrfLab
//        trfDO = trfAssembler.setTrfLabResultInfo(trfDO);
//
//        // 设置TrfCustomer
//        List<TrfCustomerDO> trfCustomerDOList = trfCustomerService.selectByTrfId(trfInfoPO.getId());
//        trfDO.setCustomerList(trfCustomerDOList);
//
//        // 设置Trf Product Sample 信息
//        trfDO = trfAssembler.setTrfProductSampleResultInfo(trfDO);
//
//        // 设置Trf TestItem 信息
//        trfDO = trfAssembler.setTestItemResultInfo(trfDO);
//
//        // 设置Trf File 信息
//        trfDO = trfAssembler.setTrfFileResultInfo(trfDO);
//
//        // 设置Trf ServiceRequirement 信息
//        TrfServiceRequirementDO trfServiceRequirementDO = trfServiceRequirementService.selectByTrfId(trfInfoPO.getId());
//        trfDO.setServiceRequirement(trfServiceRequirementDO);;
////        trfDO = trfAssembler.setTrfServiceRequirementResultInfo(trfDO);
//
//        return trfDO;
//    }
//
//    /**
//     * 通过trfNo和refSystemId查询trf信息
//     *
//     * @param trfNo
//     * @return
//     */
//    public TrfInfoPO getTrfInfoByTrfNo(String trfNo,Integer refSystemId) {
//        TrfInfoExample trfInfoExample = new TrfInfoExample();
//        trfInfoExample.createCriteria().andTrfNoEqualTo(trfNo).andRefSystemIdEqualTo(refSystemId);
//        List<TrfInfoPO> trfInfoPOS = trfInfoMapper.selectByExample(trfInfoExample);
//        if (Func.isEmpty(trfInfoPOS)) {
//            return null;
//        }
//        return trfInfoPOS.get(0);
//    }
//
//    /**
//     * 1.保存/更新 trf 绑定订单的关系
//     * 2.修改trf状态
//     *
//     * @param trfDO
//     * @return
//     */
//    public TrfDO inQuotation(TrfDO trfDO) {
//
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//
//        //业务校验 TrfStatus in （待开单，已开单，待检测，检测中）
//        Assert.isTrue(trfInfoPO != null && TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.ToBeBound),
//                ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//        Long trfId = trfInfoPO.getId();
//        Integer refSystemId = trfInfoPO.getRefSystemId();
//
//        // 数据保存
//        transactionTemplate.execute((trans) -> {
//
//            trfOrderRelationshipService.batchSave(trfId, refSystemId, trfDO.getOrderList());
//
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.ToPrice);
//            return true;
//        });
//        return getResultTrfDO(trfInfoPO);
//    }
//
//
//    /**
//     * 1.保存/更新 trf 绑定订单的关系
//     * 2.修改trf状态
//     *
//     * @param trfDO
//     * @return
//     */
//    public TrfDO accept(TrfDO trfDO) {
//
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//
//        //业务校验 TrfStatus in （待开单，报价中，已开单，待检测，检测中）
//        Assert.isTrue(trfInfoPO != null && TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.ToBeBound, TrfStatusEnum.ToPrice, TrfStatusEnum.Invoiced, TrfStatusEnum.ToBeTested),
//                ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//        Long trfId = trfInfoPO.getId();
//        Integer refSystemId = trfInfoPO.getRefSystemId();
//
//        // 数据保存
//        transactionTemplate.execute((trans) -> {
//
//            trfOrderRelationshipService.batchSave(trfId, refSystemId, trfDO.getOrderList());
//
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.Invoiced);
//            return true;
//        });
//
//
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    /**
//     * 1.保存/更新 trf 下的Sample testLine matrix 信息
//     * 2.修改trf 状态
//     *
//     * @param trfDO
//     * @return
//     */
//    public TrfDO confirm(TrfDO trfDO) {
//        //TODO 校验数据的完整性
//
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//        //业务验证
//        Assert.isTrue(trfInfoPO != null && TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.ToPrice, TrfStatusEnum.Invoiced, TrfStatusEnum.ToBeTested, TrfStatusEnum.Testing), ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//        Long trfId = trfInfoPO.getId();
//
//        QuotationDO quotationDO = new QuotationDO();
//        quotationDO.setTrfId(trfInfoPO.getId());
//        quotationDO.setQuotationList(trfDO.getQuotationList());
//
//        transactionTemplate.execute((trans) -> {
//            // Product
//            productService.saveProductList(trfId, trfDO.getProductList(), trfDO.getSampleList());
//
//            trfServiceRequirementService.saveServiceRequirement(trfId, trfDO.getServiceRequirement());
//
//            trfTestItemService.saveTestItemList(trfId, trfDO.getTestItemList());
//
//            trfCareLabelService.saveCareLabelList(trfId, trfDO.getCareLabelList());
//
//            trfFileService.saveFileList(trfId, FileTypeEnum.Attachment.getType(), trfDO.getAttachmentList());
//            // Quotation
//            quotationService.batchSave(trfId, trfDO.getQuotationList());
//            // status
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.ToBeTested);
//            return true;
//        });
//
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    /**
//     * 1.保存/更新 trf 下的Sample testLine matrix 信息
//     * 2.修改trf状态
//     *
//     * @param trfDO
//     * @return
//     */
//    public TrfDO testing(TrfDO trfDO) {
//        //TODO 校验数据的完整性
//
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//        Assert.isTrue(trfInfoPO != null && TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.ToBeTested, TrfStatusEnum.Testing), ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//        transactionTemplate.execute((trans) -> {
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.Testing);
//
//            return true;
//        });
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    /**
//     * @param trfDO
//     * @return
//     */
//    public TrfDO returnBack(TrfDO trfDO) {
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//        // TODO 校验
//        Assert.isTrue(trfInfoPO != null, ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//
//        transactionTemplate.execute((trans) -> {
//            List<String> orderIdList = null;
//            if (Func.isNotEmpty(trfDO.getOrderList())) {
//                orderIdList = trfDO.getOrderList().stream().map(TrfOrderRelationshipDO::getOrderId).collect(Collectors.toList());
//            }
//            trfOrderRelationshipService.deleteTrfRelationship(trfInfoPO.getId(), orderIdList);
//            this.updateTrfStatus(trfInfoPO, TrfStatusEnum.ToBeBound);
//            return true;
//        });
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    /**
//     * @param trfDO
//     * @return
//     */
//    public TrfDO pending(TrfDO trfDO) {
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDO);
//        // TODO 校验
//        Assert.isTrue(trfInfoPO != null, ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//
//        // 更新 Trf pending 字段
//        trfInfoPO.setPendingFlag(PendingFlagEnum.Pending.getType());
//        trfInfoMapper.updateByPrimaryKeySelective(trfInfoPO);
//        return getResultTrfDO(trfInfoPO);
//    }
//
//
//    public TrfDO unpending(TrfDO trfDOParam) {
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDOParam);
//        // TODO 校验
//        Assert.isTrue(trfInfoPO != null, ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//
//        // 更新 Trf pending 字段
//        trfInfoPO.setPendingFlag(PendingFlagEnum.UnPending.getType());
//        trfInfoMapper.updateByPrimaryKeySelective(trfInfoPO);
//
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    public TrfDO unbind(TrfDO trfDOParam) {
//        // 数据校验 并设置 获取TrfInfo
//        TrfInfoPO trfInfoPO = this.getTrfAndCheck(trfDOParam);
//        // TODO 校验
//        Assert.isTrue(trfInfoPO != null, ResponseCode.ILLEGAL_ARGUMENT, "当前TRF状态无法执行该操作");
//
//
//        transactionTemplate.execute((trans) -> {
//            List<String> orderIdList = null;
//            if (Func.isNotEmpty(trfDOParam.getOrderList())) {
//                orderIdList = trfDOParam.getOrderList().stream().map(TrfOrderRelationshipDO::getOrderId).collect(Collectors.toList());
//            }
//            trfOrderRelationshipService.deleteTrfRelationship(trfInfoPO.getId(), orderIdList);
//            trfInfoPO.setStatus(TrfStatusEnum.ToBeBound.getStatus());
//            trfInfoMapper.updateByPrimaryKeySelective(trfInfoPO);
//            return true;
//        });
//
//
//        return getResultTrfDO(trfInfoPO);
//    }
//
//    private TrfInfoPO getTrfAndCheck(TrfDO trfDO) {
//
//        TrfInfoPO trfInfo = this.getTrfInfo(trfDO.getTrfNo(), trfDO.getRefSystemId());
//
//        Assert.notNull(trfInfo, ResponseCode.ILLEGAL_ARGUMENT.getMessage());
//
//        return trfInfo;
//    }
//
//    private void saveCustomerTrfInfo(TrfDO reqObject) {
//        reqObject.setLabCode(HeaderHelper.getParamValue(TrfConstants.LAB_CODE));
//        reqObject.setProductLineCode(ProductLineContextHolder.getProductLineCode());
//        todoListService.importTrfInfoData(reqObject);
//        UserHelper.clear();
//    }
//
//    /**
//     * @param trfNo,refSystemId
//     * @return
//     */
//    private TrfInfoPO getTrfInfo(String trfNo, Integer refSystemId) {
//        // 判断是否已绑定
//        TrfInfoExample trfInfoExample = new TrfInfoExample();
//        trfInfoExample.createCriteria().andRefSystemIdEqualTo(refSystemId).andTrfNoEqualTo(trfNo);
//        List<TrfInfoPO> trfInfoPOS = trfInfoMapper.selectByExampleWithBLOBs(trfInfoExample);
//        if (CollectionUtils.isEmpty(trfInfoPOS)) {
//            return null;
//        }
//        return trfInfoPOS.get(0);
//    }
//
//    private TrfDO getResultTrfDO(TrfInfoPO trfInfoPO) {
//        TrfDO headerDTO = new TrfDO();
//        headerDTO.setTrfId(trfInfoPO.getId());
//        headerDTO.setTrfNo(trfInfoPO.getTrfNo());
//        headerDTO.setRefSystemId(trfInfoPO.getRefSystemId());
//        return headerDTO;
//    }
//
//    /**
//     * 更新Trf 状态
//     *
//     * @param trfInfoPO
//     */
//    private void updateTrfStatus(TrfInfoPO trfInfoPO, TrfStatusEnum statusEnum) {
//        // 判断当前状态  当前状态 > 推送的状态  则 不更新Trf状态
//        // 更新状态
//        trfInfoPO.setStatus(statusEnum.getStatus());
//        trfInfoPO.setModifiedTime(DateUtils.getNow());
//        trfInfoMapper.updateByPrimaryKeySelective(trfInfoPO);
//    }
//
//    private void saveCreateTrfInfo(TrfCreateBean trfInfoBean) {
//        trfInfoMapper.insert(trfInfoBean.getTrfInfoPO());
//        if (trfInfoBean.getTrfLabPO() != null) {
//            trfLabMapper.insert(trfInfoBean.getTrfLabPO());
//        }
//
//        if (CollectionUtils.isNotEmpty(trfInfoBean.getLabLangPOList())) {
//            trfLabLangMapper.batchInsert(trfInfoBean.getLabLangPOList());
//        }
//        // tb_trf_lab_contact 此处数据来源于CS
//        if (CollectionUtils.isNotEmpty(trfInfoBean.getContactList())) {
//            trfLabContactMapper.batchInsert(trfInfoBean.getContactList());
//        }
//
//    }

}
