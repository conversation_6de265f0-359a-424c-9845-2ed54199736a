package com.sgs.customerbiz.domain.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.util.SpringContexUtil;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainobject.v2.TrfReportDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.ReportDataClient;
import com.sgs.customerbiz.integration.dto.RdReportHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.NumberUtil;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.facade.model.req.rd.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportDataService {
    @Resource
    private ReportDataClient reportDataClient;

    @Resource
    private TrfDomainService trfDomainService;

    @Resource
    private FrameWorkClient frameWorkClient;

    @Resource
    private TrfReportDomainService trfReportDomainService;

    @Resource
    private ConfigClient configClient;

    public List<RdReportHeaderDTO> batchExportReportHeader(TrfFullDTO trfDOParam) {
        List<TrfReportDTO> reportList = trfDOParam.getReportList();
        if(CollectionUtils.isEmpty(reportList)) {
            return Collections.emptyList();
        }
        BatchExportReportDataReq exportReportDataTrfNoReq = new BatchExportReportDataReq();
        TrfDomainService trfDomainService = SpringContexUtil.getBean(TrfDomainService.class);
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfDOParam.getHeader().getRefSystemId(), trfDOParam.getHeader().getTrfNo());
        Assert.notNull(trfInfoPO, "trf " + trfDOParam.getHeader().getTrfNo() + " not found!");
        List<String> reportNos = reportList.stream().map(TrfReportDTO::getReportNo).collect(Collectors.toList());
        exportReportDataTrfNoReq.setReportNos(reportNos);
        exportReportDataTrfNoReq.setLabId(Func.isNotEmpty(trfInfoPO.getLabId()) ? Long.parseLong(String.valueOf(trfInfoPO.getLabId())) : null);
        exportReportDataTrfNoReq.setLabCode(trfInfoPO.getLabCode());
        exportReportDataTrfNoReq.setSystemId(Func.isNotEmpty(trfDOParam.getOrder().getSystemId()) ? Long.parseLong(String.valueOf(trfDOParam.getOrder().getSystemId())) : 1);
        BaseResponse<List<RdReportHeaderDTO>> listBaseResponse = reportDataClient.batchExportReportHeader(exportReportDataTrfNoReq);
        if (Func.isEmpty(listBaseResponse)) {
            throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, "got empty data when call rd export-report-header ");
        }
        return listBaseResponse.getData();
    }

    public void batchCancel(List<TrfReportDOV2> reportList, Integer systemId) {
        if (!reportList.isEmpty()) {
            // cancel sci report
            trfReportDomainService.batchCancel(reportList);

            // cancel RD report
            this.cancelRDReportIfUnusedRD(reportList, systemId);
        }
    }

    private void cancelRDReportIfUnusedRD(List<TrfReportDOV2> reportList, Integer systemId) {
        TrfReportDOV2 first = CollUtil.getFirst(reportList);
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfId(first.getTrfId());

        //SCI-1167
//        As Is:
//
//        GPO – cancelReport --> RD 200 OK
//        GPO – syncRevisedReport --> SCI
//        SCI --> cancelReport --> RD 500 Fail
//        SCI --> response 500 Fail --> GPO
//        To Be:
//
//        如果是GPO，在SyncRevisedReport这个动作，不调用RD的cancelReport。
        if (configClient.usedRD(systemId) || systemId == SgsSystem.GPO.getSgsSystemId()) {
            log.info("systemID:{} unused RD.", systemId);
            return;
        }

        CancelReportDataReq cancelReportDataReq = new CancelReportDataReq();
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(trfInfoPO.getLabCode());
        assembleRequestBaseModel(cancelReportDataReq, labInfo, systemId, trfInfoPO.getLabCode());

        reportList.forEach(report -> {
            cancelReportDataReq.setReportNo(report.getReportNo());
            //更新rd report
            BaseResponse<Void> voidBaseResponse = reportDataClient.cancelReport(cancelReportDataReq);
            Assert.isTrue(ResponseCode.SUCCESS.getCode() == voidBaseResponse.getStatus(), voidBaseResponse.getStatus(), voidBaseResponse.getMessage());
        });
    }

    public void saveReportData(TrfFullDTO trfFullDto) {
        Assert.isTrue(Func.isNotEmpty(trfFullDto.getReportList()), ResponseCode.ILLEGAL_ARGUMENT, "reportList不能为空");

        TrfHeaderDTO header = trfFullDto.getHeader();
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(header.getRefSystemId(), header.getTrfNo());
        Assert.notNull(trfInfoPO, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The trf {} was not found", header.getTrfNo()));
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCodeFromCache(trfInfoPO.getLabCode(), Func.toStr(trfFullDto.getSystemId(), null));

        ImportReportDataReq importReportDataReq = new ImportReportDataReq();

        List<RdReportDTO> reportList = Lists.newArrayList();
        importReportDataReq.setReportList(reportList);

        List<RdTestSampleDTO> testSampleDTOList = Lists.newArrayList();
        importReportDataReq.setTestSampleList(testSampleDTOList);

        List<RdTestLineDTO> rdTestLineList = Lists.newArrayList();
        importReportDataReq.setTestLineList(rdTestLineList);

        List<RdTestResultDTO> rdTestResultList = Lists.newArrayList();
        importReportDataReq.setTestResultList(rdTestResultList);

        List<RdReportConclusionDTO> reportConclusionDTOList = Lists.newArrayList();
        importReportDataReq.setReportConclusionList(reportConclusionDTOList);

        List<RdConditionGroupDTO> conditionGroupDTOList = Lists.newArrayList();
        importReportDataReq.setConditionGroupList(conditionGroupDTOList);

        List<RdTrfDTO> trfDTOList = Lists.newArrayList();
        importReportDataReq.setTrfList(trfDTOList);

        List<RdOrderDTO> orderDTOList = Lists.newArrayList();
        importReportDataReq.setOrderList(orderDTOList);

        //request base model
        assembleRequestBaseModel(importReportDataReq, labInfo, trfFullDto.getOrder().getSystemId(),trfInfoPO.getLabCode());

        // Process test samples, test lines and test results outside report loop
        assembleTestSample(trfFullDto, testSampleDTOList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getOrder().getSystemId());
        assembleReportTestline(trfFullDto, rdTestLineList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getOrder().getSystemId());
        assembleTestResultList(trfFullDto, rdTestResultList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getOrder().getSystemId());

        for (TrfReportDTO trfReportDTO : trfFullDto.getReportList()) {
            // report header
            RdReportDTO rdHeaderDTO = new RdReportDTO();
            reportList.add(rdHeaderDTO);
            rdHeaderDTO.setOrderNo(trfFullDto.getOrder().getOrderNo());
            rdHeaderDTO.setSystemId(trfFullDto.getOrder().getSystemId());
            rdHeaderDTO.setReportId(trfReportDTO.getReportId());
            rdHeaderDTO.setReportNo(trfReportDTO.getReportNo());
            rdHeaderDTO.setOriginalReportNo(trfReportDTO.getOriginalReportNo());
            rdHeaderDTO.setReportStatus(Func.isEmpty(trfReportDTO.getReportStatus()) ? ReportStatus.New.getCode() : trfReportDTO.getReportStatus());
            rdHeaderDTO.setReportDueDate(trfReportDTO.getReportDueDate());
            rdHeaderDTO.setApproveBy(trfReportDTO.getApproveBy());
            rdHeaderDTO.setApproveDate(trfReportDTO.getApproveDate());
            rdHeaderDTO.setSoftCopyDeliveryDate(trfReportDTO.getSoftCopyDeliveryDate());
            rdHeaderDTO.setCreateBy(trfReportDTO.getCreateBy());
            rdHeaderDTO.setCreateDate(trfReportDTO.getCreateDate());
            rdHeaderDTO.setCertificateName(trfReportDTO.getCertificateName());
            rdHeaderDTO.setReportRemark(trfReportDTO.getReportRemark());
            rdHeaderDTO.setRootReportNo(trfReportDTO.getRootReportNo());
            rdHeaderDTO.setOldReportNo(trfReportDTO.getOldReportNo());
            rdHeaderDTO.setReportVersion(trfReportDTO.getReportVersion());
            rdHeaderDTO.setRslstatus(trfReportDTO.getRslstatus());
            rdHeaderDTO.setFailCode(trfReportDTO.getFailCode());
            rdHeaderDTO.setRootReportNo(trfReportDTO.getRootReportNo());
            rdHeaderDTO.setReportVersion(trfReportDTO.getReportVersion());
            rdHeaderDTO.setReportSourceType(trfReportDTO.getReportSourceType());

            //SCI-1392
            RdEfilingDTO rdEfilingDTO = new RdEfilingDTO();
            rdHeaderDTO.setEFiling(rdEfilingDTO);
            Optional.ofNullable(trfReportDTO.getEFiling()).ifPresent(trfEfilingDTO -> {
                rdEfilingDTO.setExclusionCitationCode(trfEfilingDTO.getExclusionCitationCode());
                rdEfilingDTO.setRbsrCitationCode(trfEfilingDTO.getRbsrCitationCode());
                rdEfilingDTO.setDisclaimCode(trfEfilingDTO.getDisclaimCode());
                rdEfilingDTO.setCpscLabId(trfEfilingDTO.getCpscLabId());
                rdEfilingDTO.setLabType(trfEfilingDTO.getLabType());
            });

            Optional.ofNullable(trfReportDTO.getReportCertificateList())
                    .map(cl -> cl.stream()
                            .map(ReportDataService::createReportCertificateDTOForm)
                            .collect(Collectors.toList()))
                    .ifPresent(rdHeaderDTO::setReportCertificateList);

            //header lab
            RdLabDTO rdLabDTO = new RdLabDTO();
            rdHeaderDTO.setLab(rdLabDTO);
            //SCI-1392
            assembleReportHeaderLab(rdLabDTO, labInfo, trfReportDTO.getLab());
            //header conclusion
            assembleReportHeaderConclusion(rdHeaderDTO, trfReportDTO.getConclusion());

            //report matrix
            assembleReportMatrix(trfReportDTO, rdHeaderDTO);

            //report conclusion
            List<TrfReportConclusionDTO> reportConclusionList = trfReportDTO.getReportConclusionList();
            List<RdReportConclusionDTO> rdReportConclusionList = convertRdTrfReportConclusionList(reportConclusionList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getOrder().getSystemId());
            if (Func.isNotEmpty(rdReportConclusionList)) {
                reportConclusionDTOList.addAll(rdReportConclusionList);
            }

            //report file
            assembleReportFileList(trfReportDTO, rdHeaderDTO);

            //sub report
            List<RdSubReportDTO> subReportList = convertRdSubReportList(trfReportDTO.getSubReportList());
            rdHeaderDTO.setSubReportList(subReportList);

            //condition group
            assembleConditionGroup(trfReportDTO, conditionGroupDTOList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getOrder().getSystemId());

        }

        //order
        TrfOrderDTO trfOrderDTO = trfFullDto.getOrder();
        if (Func.isNotEmpty(trfOrderDTO)) {
            RdOrderDTO rdOrderDTO = new RdOrderDTO();
            assembleReportOrder(rdOrderDTO, trfOrderDTO);

            RdTrfRelDTO trfRelDTO = new RdTrfRelDTO();
            trfRelDTO.setRefSystemId(trfInfoPO.getRefSystemId());
            trfRelDTO.setTrfNo(trfInfoPO.getTrfNo());
            rdOrderDTO.setTrfList(Arrays.asList(trfRelDTO));
            orderDTOList.add(rdOrderDTO);
        }

        //trf
        RdTrfDTO rdTrfDTO = new RdTrfDTO();
        rdTrfDTO.setRefSystemId(trfInfoPO.getRefSystemId());
        rdTrfDTO.setTrfNo(trfInfoPO.getTrfNo());
        rdTrfDTO.setTrfTemplateOwner(null);
        rdTrfDTO.setTrfTemplateId(header.getTrfTemplateId());
        rdTrfDTO.setTrfTemplateName(header.getTrfTemplateName());
        rdTrfDTO.setTrfSubmissionDate(trfInfoPO.getCreatedDate());
        rdTrfDTO.setServiceEndDate(header.getServiceEndDate());
        rdTrfDTO.setServiceStartDate(header.getServiceStartDate());
        if (Func.isNotEmpty(header.getPurchaseOrderList())) {
            rdTrfDTO.setPurchaseOrderList(JSONObject.parseArray(JSON.toJSONString(header.getPurchaseOrderList()), RdPurchaseOrderDTO.class));
        }
        if (Func.isNotEmpty(header.getCustomerProductList())) {
            rdTrfDTO.setCustomerProductList(JSONObject.parseArray(JSON.toJSONString(header.getCustomerProductList()), RdCustomerProductDTO.class));
        }

        if (Func.isNotEmpty(trfFullDto.getHeader()) && Func.isNotEmpty(trfFullDto.getTrfList())) {
            Optional<TrfHeaderDTO> trfInfo = trfFullDto.getTrfList().stream().filter(trfParam -> trfParam.getTrfNo().equals(trfInfoPO.getTrfNo())).findAny();
            trfInfo.ifPresent(trf -> {
                RdTrfOtherDTO other = new RdTrfOtherDTO();
                TrfOtherDTO others = trf.getOthers();
                if (Func.isNotEmpty(others)) {
                    other.setRepeatService(others.getRepeatService());
                    rdTrfDTO.setOther(other);
                }
            });
        }

        RdOrderRelDTO orderRelDTO = new RdOrderRelDTO();
        orderRelDTO.setSystemId(Func.isNotEmpty(trfFullDto.getOrder().getSystemId()) ? trfFullDto.getOrder().getSystemId() : header.getSystemId());
        orderRelDTO.setOrderNo(trfFullDto.getOrder().getOrderNo());
        rdTrfDTO.setOrderList(Collections.singletonList(orderRelDTO));
        trfDTOList.add(rdTrfDTO);

        BaseResponse<Void> voidBaseResponse = reportDataClient.batchImportReportData(importReportDataReq);

        Assert.isTrue(ResponseCode.SUCCESS.getCode() == voidBaseResponse.getStatus(), voidBaseResponse.getStatus(), voidBaseResponse.getMessage());
    }

    @NotNull
    private static ReportCertificateDTO createReportCertificateDTOForm(TrfReportCertificateDTO c) {
        ReportCertificateDTO cert = new ReportCertificateDTO();
        cert.setCertificateId(c.getCertificateId());
        cert.setCertificateNo(c.getCertificateNo());
        cert.setCertificateType(c.getCertificateType());
        cert.setCertificateTypeDisplay(c.getCertificateTypeDisplay());
        cert.setExpireDate(c.getExpireDate());
        cert.setToCustomerFlag(c.getToCustomerFlag());
        cert.setDeliverToCustomerFlag(c.getDeliverToCustomerFlag());
        cert.setRemark(c.getRemark());
        cert.setStatus(c.getStatus());
        cert.setStatusDisplay(c.getStatusDisplay());
        cert.setIssueDate(c.getIssueDate());
        cert.setTestReportNumbersInCertificate(c.getTestReportNumbersInCertificate());
        return cert;
    }

    public void saveQuotation(TrfFullDTO trfFullDto) {
        if (CollectionUtils.isEmpty(trfFullDto.getQuotationList())) {
            return;
        }
        TrfHeaderDTO header = trfFullDto.getHeader();
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(header.getRefSystemId(), header.getTrfNo());
        ImportQuotationReq importQuotationReq = new ImportQuotationReq();
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(trfInfoPO.getLabCode());
        List<TrfReportPO> reportPOList = trfReportDomainService.selectByTrfId(trfInfoPO.getId());

        assembleRequestBaseModel(importQuotationReq, labInfo, header.getSystemId(),trfInfoPO.getLabCode());

//        RdOrderDTO rdOrderDTO = new RdOrderDTO();
//        importQuotationReq.setOrder(rdOrderDTO);
//        if(Func.isNotEmpty(trfFullDto.getOrder())) {
//            assembleOrderBase(rdOrderDTO, trfFullDto.getOrder());
//        }
        if (Func.isNotEmpty(reportPOList)) {
            TrfReportPO trfReportPO = CollUtil.get(reportPOList, 0);
            importQuotationReq.setReportNo(trfReportPO.getReportNo());
        }

        assembleQuotationList(trfFullDto, importQuotationReq);

        BaseResponse<Void> voidBaseResponse = reportDataClient.importQuotation(importQuotationReq);

        Assert.isTrue(ResponseCode.SUCCESS.getCode() == voidBaseResponse.getStatus(), voidBaseResponse.getStatus(), voidBaseResponse.getMessage());

    }

    public void saveInvoice(TrfFullDTO trfFullDto) {
        if (CollectionUtils.isEmpty(trfFullDto.getInvoiceList())) {
            return;
        }

//        Assert.isTrue(Func.isNotEmpty(trfFullDto.getInvoiceList()), ResponseCode.ILLEGAL_ARGUMENT, "InvoiceList");

        TrfHeaderDTO header = trfFullDto.getHeader();
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(header.getRefSystemId(), header.getTrfNo());
        ImportReportInvoiceReq importReportInvoiceReq = new ImportReportInvoiceReq();
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(trfInfoPO.getLabCode());
        List<TrfReportPO> reportPOList = trfReportDomainService.selectByTrfIdAndActive(trfInfoPO.getId());

        if(Boolean.FALSE.equals(checkReportExist(reportPOList, trfInfoPO, trfFullDto))){
            ErrorCode errorCode = new ErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.REPORTDATASERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATANOTFOUND);
            throw new CustomerBizException(errorCode,"report info cannot null！");
        }

        assembleRequestBaseModel(importReportInvoiceReq, labInfo, header.getSystemId(),trfInfoPO.getLabCode());

        RdOrderDTO rdOrderDTO = new RdOrderDTO();
        importReportInvoiceReq.setOrder(rdOrderDTO);
        if (Func.isNotEmpty(trfFullDto.getOrder())) {
            assembleOrderBase(rdOrderDTO, trfFullDto.getOrder());
        }
        if (Func.isNotEmpty(reportPOList)) {
            TrfReportPO trfReportPO = CollUtil.get(reportPOList, 0);
            importReportInvoiceReq.setReportNo(trfReportPO.getReportNo());
        }
        assembleRdInvoice(trfFullDto, importReportInvoiceReq);

        BaseResponse<Void> voidBaseResponse = reportDataClient.importInvoice(importReportInvoiceReq);

        Assert.isTrue(ResponseCode.SUCCESS.getCode() == voidBaseResponse.getStatus(), voidBaseResponse.getStatus(), voidBaseResponse.getMessage());

    }

    private Boolean checkReportExist(List<TrfReportPO> reportPOList,TrfInfoPO trfInfoPO ,TrfFullDTO trfFullDto){
        BatchExportReportDataReq exportReportDataTrfNoReq = new BatchExportReportDataReq();
        exportReportDataTrfNoReq.setReportNos(reportPOList.stream().map(TrfReportPO::getReportNo).collect(Collectors.toList()));
        exportReportDataTrfNoReq.setLabId(Func.isNotEmpty(trfInfoPO.getLabId()) ? Long.parseLong(String.valueOf(trfInfoPO.getLabId())) : null);
        exportReportDataTrfNoReq.setLabCode(trfInfoPO.getLabCode());
        exportReportDataTrfNoReq.setSystemId(Func.isNotEmpty(trfFullDto.getOrder().getSystemId()) ? Long.parseLong(String.valueOf(trfFullDto.getOrder().getSystemId())) : 1);
        // 从RD获取Report信息
        BaseResponse<List<ReportDataDTO>> listBaseResponse = SpringContexUtil.getBean(ReportDataClient.class).batchExportReportData(exportReportDataTrfNoReq);
        if (Func.isEmpty(listBaseResponse) || Func.isEmpty(listBaseResponse.getData())) {
            log.error("{} error 调用rd exportReportData接口异常！");
            return Boolean.FALSE;
        }
        List<ReportDataDTO> data = listBaseResponse.getData();
        if (Func.isEmpty(data)||data.size() != reportPOList.size()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void assembleConditionGroup(TrfReportDTO trfReportDTO, List<RdConditionGroupDTO> list, String orderNo, Integer systemId) {
        if (Func.isEmpty(trfReportDTO.getConditionGroupList())) {
            return;
        }
        List<RdConditionGroupDTO> conditionGroupList = trfReportDTO.getConditionGroupList().parallelStream().map(cg -> {
            RdConditionGroupDTO rdConditionGroupDTO = new RdConditionGroupDTO();
            rdConditionGroupDTO.setConditionGroupId(cg.getConditionGroupId());
            rdConditionGroupDTO.setCombinedConditionDescription(cg.getCombinedConditionDescription());
            rdConditionGroupDTO.setRequirement(cg.getRequirement());
            rdConditionGroupDTO.setPpConditionGroupList(convertRdPpConditionGroup(cg.getPpConditionGroupList()));
            rdConditionGroupDTO.setLanguageList(convertRdConditionGroupLanguageDTO(cg.getLanguageList()));
            rdConditionGroupDTO.setOrderNo(orderNo);
            rdConditionGroupDTO.setSystemId(systemId);
            return rdConditionGroupDTO;
        }).collect(Collectors.toList());
        list.addAll(conditionGroupList);
    }

    private List<RdConditionGroupLanguageDTO> convertRdConditionGroupLanguageDTO(List<TrfConditionGroupLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(l -> {
            RdConditionGroupLanguageDTO rdCgl = new RdConditionGroupLanguageDTO();
            rdCgl.setLanguageId(l.getLanguageId());
            rdCgl.setCombinedConditionDescription(l.getCombinedConditionDescription());
            return rdCgl;
        }).collect(Collectors.toList());
    }

    private List<RdPpConditionGroupDTO> convertRdPpConditionGroup(List<TrfPpConditionGroupDTO> ppConditionGroupList) {
        if (Func.isEmpty(ppConditionGroupList)) {
            return null;
        }
        return ppConditionGroupList.parallelStream().map(pcg -> {
            RdPpConditionGroupDTO rdPpConditionGroupDTO = new RdPpConditionGroupDTO();
            rdPpConditionGroupDTO.setId(pcg.getId());
            rdPpConditionGroupDTO.setConditionGroupId(pcg.getConditionGroupId());
            rdPpConditionGroupDTO.setPpTestLineRelId(pcg.getPpTestLineRelId());
            rdPpConditionGroupDTO.setLanguageType(pcg.getLanguageId());
            rdPpConditionGroupDTO.setGroupFootNotes(pcg.getGroupFootNotes());
            rdPpConditionGroupDTO.setPpNo(pcg.getPpNo());
            rdPpConditionGroupDTO.setPpName(pcg.getPpName());
            rdPpConditionGroupDTO.setSampleNos(pcg.getSampleNos());
            return rdPpConditionGroupDTO;
        }).collect(Collectors.toList());
    }

    private List<RdSubReportDTO> convertRdSubReportList(List<TrfSubReportDTO> subReportList) {
        if (Func.isEmpty(subReportList)) {
            return null;
        }
        return subReportList.parallelStream().map(sr -> {
            RdSubReportDTO rdSubReportDTO = new RdSubReportDTO();
            rdSubReportDTO.setSubReportNo(sr.getSubReportNo());
            rdSubReportDTO.setSubReportFileList(convertRdAttachmentList(sr.getSubReportFileList()));
            return rdSubReportDTO;
        }).collect(Collectors.toList());
    }

    private List<RdAttachmentDTO> convertRdAttachmentList(List<TrfFileDTO> trfFileList) {
        if (Func.isEmpty(trfFileList)) {
            return null;
        }
        return trfFileList.parallelStream().map(tf -> {
            RdAttachmentDTO sr = new RdAttachmentDTO();
            sr.setFileName(tf.getFileName());
            sr.setFileType(tf.getFileType());
//            sr.setObjectType();
//            sr.setObjectId();
            sr.setCloudId(tf.getCloudId());
            sr.setFilePath(tf.getFilePath());
            sr.setToCustomerFlag(tf.getToCustomerFlag());
            return sr;
        }).collect(Collectors.toList());
    }

    private List<RdReportConclusionDTO> convertRdTrfReportConclusionList(List<TrfReportConclusionDTO> reportConclusionList, String orderNo, Integer systemId) {
        if (Func.isEmpty(reportConclusionList)) {
            return null;
        }
        return reportConclusionList.parallelStream().map(rc -> {
            RdReportConclusionDTO rdReportConclusionDTO = new RdReportConclusionDTO();
            rdReportConclusionDTO.setConclusionInstanceId(rc.getConclusionInstanceId());
            rdReportConclusionDTO.setConclusionLevelId(rc.getConclusionLevelId());
            rdReportConclusionDTO.setObjectId(rc.getObjectId());
            rdReportConclusionDTO.setConclusionId(rc.getConclusionId());
            rdReportConclusionDTO.setConclusionCode(rc.getConclusionCode());
            rdReportConclusionDTO.setCustomerConclusionId(rc.getCustomerConclusionId());
            rdReportConclusionDTO.setCustomerConclusion(rc.getCustomerConclusion());
            rdReportConclusionDTO.setConclusionRemark(rc.getConclusionRemark());
            rdReportConclusionDTO.setTestLineInstanceId(rc.getTestLineInstanceId());
            rdReportConclusionDTO.setSampleInstanceId(rc.getSampleInstanceId());
            rdReportConclusionDTO.setSectionId(rc.getSectionId());
            rdReportConclusionDTO.setPpArtifactRelId(rc.getPpArtifactRelId());
            rdReportConclusionDTO.setPpSampleRelId(rc.getPpSampleRelId());
            rdReportConclusionDTO.setOrderNo(orderNo);
            rdReportConclusionDTO.setSystemId(systemId);
            return rdReportConclusionDTO;
        }).collect(Collectors.toList());
    }

    private void assembleReportFileList(TrfReportDTO trfReportDTO, RdReportDTO reportDTO) {
        if (Func.isEmpty(trfReportDTO.getReportFileList())) {
            return;
        }
        List<RdAttachmentDTO> rdReportFileList = trfReportDTO.getReportFileList().parallelStream().map(trfReportFileDO -> {
            RdAttachmentDTO rdReportFileDTO = new RdAttachmentDTO();
            rdReportFileDTO.setFileType(trfReportFileDO.getFileType());
            rdReportFileDTO.setFileName(trfReportFileDO.getFileName());
            rdReportFileDTO.setFilePath(trfReportFileDO.getFilePath());
            rdReportFileDTO.setCloudId(trfReportFileDO.getCloudId());
            rdReportFileDTO.setLanguageId(trfReportFileDO.getLanguageId());
            rdReportFileDTO.setToCustomerFlag(trfReportFileDO.getToCustomerFlag());
            rdReportFileDTO.setIssuedDate(trfReportFileDO.getIssuedDate());
            return rdReportFileDTO;
        }).collect(Collectors.toList());
        reportDTO.setReportFileList(rdReportFileList);
    }

    private void assembleTestResultList(TrfFullDTO trfFullDto, List<RdTestResultDTO> rdTestResultList, String orderNo, Integer systemId) {
        List<TrfTestResultDTO> testResultList = trfFullDto.getTestResultList();
        List<RdTestResultDTO> testResultDTOList = convertRdTestResultList(testResultList, orderNo, systemId);
        if (Func.isNotEmpty(testResultDTOList)) {
            rdTestResultList.addAll(testResultDTOList);
        }
//        importReportDataReq.setTestResultList(rdTestResultList);
    }

    private List<RdTestResultDTO> convertRdTestResultList(List<TrfTestResultDTO> testResultList, String orderNo, Integer systemId) {
        if (Func.isEmpty(testResultList)) {
            return null;
        }
        return testResultList.stream().map(r -> {
            RdTestResultDTO rdTestResultDTO = new RdTestResultDTO();
            rdTestResultDTO.setTestMatrixId(r.getTestMatrixId());
            rdTestResultDTO.setSubReportNo(r.getSubReportNo());
            rdTestResultDTO.setTestResult(convertRdTestResultValue(r));
            rdTestResultDTO.setTestResultSeq(r.getTestResultSeq());
            rdTestResultDTO.setReportLimit(convertRdReportLimit(r));
            rdTestResultDTO.setOrderNo(orderNo);
            rdTestResultDTO.setReportNo(r.getReportNo());
            rdTestResultDTO.setTestResultInstanceId(r.getTestResultInstanceId());
            rdTestResultDTO.setSystemId(systemId);
            rdTestResultDTO.setLanguageList(r.getLanguageList());
            return rdTestResultDTO;
        }).collect(Collectors.toList());
    }

    private RdReportLimitDTO convertRdReportLimit(TrfTestResultDTO r) {
        if (Func.isEmpty(r)) {
            return null;
        }
        if (Func.isEmpty(r.getLimitValueFullName()) && Func.isEmpty(r.getLimitValueFullNameRel())) {
            return null;
        }
        RdReportLimitDTO rdReportLimitDTO = new RdReportLimitDTO();
        rdReportLimitDTO.setLimitValueFullName(r.getLimitValueFullName());
        if (Func.isNotEmpty(r.getLimitValueFullNameRel())) {
            TrfLimitValueFullNameRelDTO limitValueFullNameRel = r.getLimitValueFullNameRel();
            RdLimitValueFullNameRelDTO rdLimitValueFullNameRelDTO = new RdLimitValueFullNameRelDTO();
            rdLimitValueFullNameRelDTO.setTalBaseId(limitValueFullNameRel.getTalBaseId());
            rdLimitValueFullNameRelDTO.setManualRequirement(limitValueFullNameRel.getManualRequirement());
            rdLimitValueFullNameRelDTO.setLimitValue1(limitValueFullNameRel.getLimitValue1());
            rdLimitValueFullNameRelDTO.setLimitValue2(limitValueFullNameRel.getLimitValue2());
            rdLimitValueFullNameRelDTO.setOperatorName(limitValueFullNameRel.getOperatorName());
            rdLimitValueFullNameRelDTO.setReportDescription(limitValueFullNameRel.getReportDescription());
            rdReportLimitDTO.setLimitValueFullNameRel(rdLimitValueFullNameRelDTO);
        }
        return rdReportLimitDTO;
    }

    private RdTestResultResultDTO convertRdTestResultValue(TrfTestResultDTO testResult) {
        if (Func.isEmpty(testResult)) {
            return null;
        }
        RdTestResultResultDTO resultResultDTO = new RdTestResultResultDTO();
        resultResultDTO.setTestResultFullName(testResult.getTestResultFullName());

        TrfTestResultNameRelDTO testResultFullNameRel = null;
        if (Func.isNotEmpty(testResult.getTestResult())) {
            TrfTestResultValueDTO trv = testResult.getTestResult();
            resultResultDTO.setResultValue(trv.getResultValue());
            resultResultDTO.setResultValueRemark(trv.getResultValueRemark());
            resultResultDTO.setReportRemark(trv.getReportRemark());
            resultResultDTO.setResultUnit(trv.getResultUnit());
            resultResultDTO.setFailFlag(trv.getFailFlag());
            testResultFullNameRel = trv.getTestResultFullNameRel();
        }
        if (Func.isEmpty(testResultFullNameRel)) {
            testResultFullNameRel = testResult.getTestResultFullNameRel();
        }
        if (Func.isNotEmpty(testResultFullNameRel)) {
            RdTestResultNameDTO testResultNameDTO = getRdTestResultNameDTO(testResultFullNameRel);
            resultResultDTO.setTestResultFullNameRel(testResultNameDTO);
        }
        return resultResultDTO;
    }

    private static @NotNull RdTestResultNameDTO getRdTestResultNameDTO(TrfTestResultNameRelDTO testResultFullNameRel) {
        RdTestResultNameDTO testResultNameDTO = new RdTestResultNameDTO();
        testResultNameDTO.setUpSpecimenInstanceId(testResultFullNameRel.getUpSpecimenInstanceId());
        testResultNameDTO.setSpecimenInstanceId(testResultFullNameRel.getSpecimenInstanceId());
        testResultNameDTO.setProcedureConditionInstanceId(testResultFullNameRel.getProcedureConditionInstanceId());
        testResultNameDTO.setParentConditionInstanceId(testResultFullNameRel.getParentConditionInstanceId());
        testResultNameDTO.setConditionInstanceId(testResultFullNameRel.getConditionInstanceId());
        testResultNameDTO.setPositionInstanceId(testResultFullNameRel.getPositionInstanceId());
        testResultNameDTO.setAnalyteInstanceId(testResultFullNameRel.getAnalyteInstanceId());
        return testResultNameDTO;
    }


    private void assembleReportMatrix(TrfReportDTO trfReportDTO, RdReportDTO reportDTO) {
        List<TrfReportMatrixDTO> reportMatrixList = trfReportDTO.getReportMatrixList();
        List<RdReportMatrixDTO> rdReportMatrixList = convertRdReportMatrixList(reportMatrixList);
        reportDTO.setReportMatrixList(rdReportMatrixList);
    }

    private List<RdReportMatrixDTO> convertRdReportMatrixList(List<TrfReportMatrixDTO> reportMatrixList) {
        if (Func.isEmpty(reportMatrixList)) {
            return null;
        }
        return reportMatrixList.parallelStream().map(rm -> {
            RdReportMatrixDTO reportMatrixDTO = new RdReportMatrixDTO();
            reportMatrixDTO.setTestMatrixId(rm.getTestMatrixId());
            reportMatrixDTO.setSubReportNo(rm.getSubReportNo());
            reportMatrixDTO.setTestMatrixGroupId(rm.getTestMatrixGroupId());
            reportMatrixDTO.setTestConditionGroupId(rm.getTestConditionGroupId());
            reportMatrixDTO.setConditionList(convertCondition(rm.getConditionList()));
            reportMatrixDTO.setTestSampleInstanceId(rm.getTestSampleInstanceId());
            reportMatrixDTO.setSpecimenList(convertRdSpeciment(rm.getSpecimenList()));
            reportMatrixDTO.setTestLineInstanceId(rm.getTestLineInstanceId());
            reportMatrixDTO.setPositionList(convertPosition(rm.getPositionList()));
            reportMatrixDTO.setRemark(rm.getRemark());
            if (Func.isEmpty(rm.getConclusion()) && Func.isNotEmpty(rm.getConclusionCode())) {
                TrfConclusionDTO trfConclusionDTO = new TrfConclusionDTO();
                trfConclusionDTO.setConclusionCode(rm.getConclusionCode());
                rm.setConclusion(trfConclusionDTO);
            }
            reportMatrixDTO.setConclusion(convertRdConclusion(rm.getConclusion()));
            return reportMatrixDTO;
        }).collect(Collectors.toList());
    }

    private List<RdConditionDTO> convertCondition(List<TrfConditionDTO> conditionList) {
        if (Func.isEmpty(conditionList)) {
            return null;
        }
        return conditionList.parallelStream().map(c -> {
            RdConditionDTO rdConditionDTO = new RdConditionDTO();
            rdConditionDTO.setConditionInstanceId(c.getConditionInstanceId());
            rdConditionDTO.setConditionId(c.getConditionId());
            rdConditionDTO.setConditionType(c.getConditionType());
            rdConditionDTO.setConditionTypeId(c.getConditionTypeId());
            rdConditionDTO.setConditionTypeName(c.getConditionTypeName());
            rdConditionDTO.setConditionName(c.getConditionName());
            rdConditionDTO.setConditionDesc(c.getConditionDesc());
            rdConditionDTO.setConditionSeq(c.getConditionSeq());
            rdConditionDTO.setLanguageList(convertRdConditionLang(c.getLanguageList()));
            return rdConditionDTO;
        }).collect(Collectors.toList());
    }

    private List<RdConditionLanguageDTO> convertRdConditionLang(List<TrfConditionLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(c -> {
            RdConditionLanguageDTO rdConditionLanguageDTO = new RdConditionLanguageDTO();
            rdConditionLanguageDTO.setLanguageId(c.getLanguageId());
            rdConditionLanguageDTO.setConditionTypeName(c.getConditionTypeName());
            rdConditionLanguageDTO.setConditionName(c.getConditionName());
            rdConditionLanguageDTO.setConditionDesc(c.getConditionDesc());
            return rdConditionLanguageDTO;
        }).collect(Collectors.toList());
    }

    private List<RdPositionDTO> convertPosition(List<TrfPositionDTO> positionList) {
        if (Func.isEmpty(positionList)) {
            return null;
        }

        return positionList.parallelStream().map(trfPositionDTO -> {
            RdPositionDTO rdPositionDTO = new RdPositionDTO();
            rdPositionDTO.setPositionInstanceId(trfPositionDTO.getPositionInstanceId());
            rdPositionDTO.setUsageTypeId(trfPositionDTO.getUsageTypeId());
            rdPositionDTO.setUsageTypeName(trfPositionDTO.getUsageTypeName());
            rdPositionDTO.setPositionName(trfPositionDTO.getPositionName());
            rdPositionDTO.setPositionDescription(trfPositionDTO.getPositionDescription());
            rdPositionDTO.setPositionSeq(trfPositionDTO.getPositionSeq());
            rdPositionDTO.setLanguageList(convertRdPositionLang(trfPositionDTO.getLanguageList()));
            return rdPositionDTO;
        }).collect(Collectors.toList());
    }

    private List<RdPositionLanguageDTO> convertRdPositionLang(List<TrfPositionLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(pl -> {
            RdPositionLanguageDTO rdPositionLanguageDTO = new RdPositionLanguageDTO();
            rdPositionLanguageDTO.setLanguageId(pl.getLanguageId());
            rdPositionLanguageDTO.setPositionName(pl.getPositionName());
            rdPositionLanguageDTO.setPositionDescription(pl.getPositionDescription());
            return rdPositionLanguageDTO;
        }).collect(Collectors.toList());
    }

    private List<RdSpecimenDTO> convertRdSpeciment(List<TrfSpecimenDTO> specimenList) {
        if (Func.isEmpty(specimenList)) {
            return null;
        }
        return specimenList.parallelStream().map(s -> {
            RdSpecimenDTO rdSpecimenDTO = new RdSpecimenDTO();
            rdSpecimenDTO.setSpecimenType(s.getSpecimenType());
            rdSpecimenDTO.setSpecimenInstanceId(s.getSpecimenInstanceId());
            rdSpecimenDTO.setSpecimenNo(s.getSpecimenNo());
            rdSpecimenDTO.setSpecimenDescription(s.getSpecimenDescription());
            rdSpecimenDTO.setLanguageList(convertRdSpecimentLang(s.getLanguageList()));
            return rdSpecimenDTO;
        }).collect(Collectors.toList());
    }

    private List<RdSpecimenLanguageDTO> convertRdSpecimentLang(List<TrfSpecimenLangDTO> languageList) {
        return languageList.parallelStream().map(sl -> {
            RdSpecimenLanguageDTO rdSl = new RdSpecimenLanguageDTO();
            rdSl.setLanguageId(sl.getLanguageId());
            rdSl.setSpecimenDescription(sl.getSpecimenDescription());
            return rdSl;
        }).collect(Collectors.toList());
    }

    private void assembleTestSample(TrfFullDTO trfFullDto, List<RdTestSampleDTO> testSampleDTOList, String orderNo, Integer systemId) {
        if (Func.isEmpty(trfFullDto.getTestSampleList())) {
            return;
        }
        List<TrfTestSampleDTO> testSampleList = trfFullDto.getTestSampleList();
        List<RdTestSampleDTO> rdTestSampleList = convertRdTestSampleList(testSampleList, orderNo, systemId);
        if (Func.isNotEmpty(rdTestSampleList)) {
            testSampleDTOList.addAll(rdTestSampleList);
        }
    }

    private void assembleReportTestline(TrfFullDTO trfFullDto, List<RdTestLineDTO> rdTestLineList, String orderNo, Integer systemId) {
        List<TrfTestLineDTO> testLineList = trfFullDto.getTestLineList();
        if (Func.isEmpty(testLineList)) {
            return;
        }
        List<RdTestLineDTO> testLineDTOList = convertRdTestlineList(testLineList, orderNo, systemId);
        if (Func.isNotEmpty(testLineDTOList)) {
            rdTestLineList.addAll(testLineDTOList);
        }
//        importReportDataReq.setTestLineList(rdTestLineList);
    }

    private List<RdTestLineDTO> convertRdTestlineList(List<TrfTestLineDTO> testLineList, String orderNo, Integer systemId) {
        if (Func.isEmpty(testLineList)) {
            return null;
        }
        return testLineList.stream().map(t -> {
            RdTestLineDTO rdTestLineDTO = new RdTestLineDTO();
            rdTestLineDTO.setTestLineInstanceId(t.getTestLineInstanceId());
            rdTestLineDTO.setTestItemNo(t.getTestItemNo());
            rdTestLineDTO.setTestLineType(t.getTestLineType());
            rdTestLineDTO.setTestLineBaseId(Func.toLongObject(t.getTestLineBaseId(), null));
            rdTestLineDTO.setTestLineId(t.getTestLineId());
            rdTestLineDTO.setTestLineVersionId(t.getTestLineVersionId());
            rdTestLineDTO.setEvaluationAlias(t.getEvaluationAlias());
            rdTestLineDTO.setEvaluationName(t.getEvaluationName());
            rdTestLineDTO.setTestLineStatus(t.getTestLineStatus());
            rdTestLineDTO.setTestLineSeq(t.getTestLineSeq());
            rdTestLineDTO.setLabSectionBaseId(Func.toLongObject(t.getLabSectionBaseId(), null));
            rdTestLineDTO.setLabTeam(t.getLabTeam());
            rdTestLineDTO.setProductLineAbbr(t.getProductLineAbbr());
            rdTestLineDTO.setTestLineRemark(t.getTestLineRemark());
            rdTestLineDTO.setCitation(convertRdCitation(t.getCitation()));
            rdTestLineDTO.setWi(convertRdTestlineWi(t.getWi()));
            rdTestLineDTO.setAnalyteList(convertRdTestlineAnalyte(t.getAnalyteList()));
            rdTestLineDTO.setPpTestLineRelList(convertRdPpTestlineList(t.getPpTestLineRelList()));
            rdTestLineDTO.setConclusion(convertRdConclusion(t.getConclusion()));
            rdTestLineDTO.setLanguageList(convertRdTestlineLang(t.getLanguageList()));
            rdTestLineDTO.setOrderNo(orderNo);
            rdTestLineDTO.setReportNo(t.getReportNo());
            rdTestLineDTO.setSystemId(systemId);
            return rdTestLineDTO;
        }).collect(Collectors.toList());
    }

    private List<RdTestLineLanguageDTO> convertRdTestlineLang(List<TrfTestLineLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(tll -> {
            RdTestLineLanguageDTO rdTestLineLanguageDTO = new RdTestLineLanguageDTO();
            rdTestLineLanguageDTO.setLanguageId(tll.getLanguageId());
            rdTestLineLanguageDTO.setEvaluationAlias(tll.getEvaluationAlias());
            rdTestLineLanguageDTO.setEvaluationName(tll.getEvaluationName());
            return rdTestLineLanguageDTO;
        }).collect(Collectors.toList());
    }

    private RdConclusionDTO convertRdConclusion(TrfConclusionDTO conclusion) {
        if (Func.isEmpty(conclusion)) {
            return null;
        }
        RdConclusionDTO rdConclusionDTO = new RdConclusionDTO();
        rdConclusionDTO.setConclusionCode(conclusion.getConclusionCode());
        rdConclusionDTO.setCustomerConclusion(conclusion.getCustomerConclusion());
        rdConclusionDTO.setReviewConclusion(conclusion.getReviewConclusion());
        rdConclusionDTO.setConclusionRemark(conclusion.getConclusionRemark());
        return rdConclusionDTO;
    }

    private List<RdPpTestLineRelDTO> convertRdPpTestlineList(List<TrfPpTestLineDTO> ppTestLineRelList) {
        if (Func.isEmpty(ppTestLineRelList)) {
            return null;
        }
        return ppTestLineRelList.parallelStream().map(ppt -> {
            RdPpTestLineRelDTO ppTestLineRelDTO = new RdPpTestLineRelDTO();
            ppTestLineRelDTO.setPpTlRelId(ppt.getPpTlRelId());
            ppTestLineRelDTO.setPpArtifactRelId(ppt.getPpArtifactRelId());
            ppTestLineRelDTO.setPpBaseId(ppt.getPpBaseId());
            ppTestLineRelDTO.setRootPPBaseId(ppt.getRootPPBaseId());
            ppTestLineRelDTO.setPpNo(ppt.getPpNo());
            ppTestLineRelDTO.setPpVersionId(ppt.getPpVersionId());
            ppTestLineRelDTO.setPpName(ppt.getPpName());
            ppTestLineRelDTO.setPpNotes(ppt.getPpNotes());
            ppTestLineRelDTO.setSectionId(ppt.getSectionId());
            ppTestLineRelDTO.setSectionLevel(ppt.getSectionLevel());
            ppTestLineRelDTO.setSectionName(ppt.getSectionName());
            ppTestLineRelDTO.setAid(Func.toLongObject(ppt.getAid(), null));
            ppTestLineRelDTO.setTestCategoryCode(ppt.getTestCategoryCode());
            ppTestLineRelDTO.setCitation(convertRdCitation(ppt.getCitation()));
            ppTestLineRelDTO.setLanguageList(convertRdPpTestlineLang(ppt.getLanguageList()));
            return ppTestLineRelDTO;
        }).collect(Collectors.toList());
    }

    private List<RdPpTestLineRelLangDTO> convertRdPpTestlineLang(List<TrfPpTestLineLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(ppTl -> {
            RdPpTestLineRelLangDTO rdPpTl = new RdPpTestLineRelLangDTO();
            rdPpTl.setLanguageId(ppTl.getLanguageId());
            rdPpTl.setPpName(ppTl.getPpName());
            rdPpTl.setPpNotes(ppTl.getPpNotes());
            return rdPpTl;
        }).collect(Collectors.toList());
    }

    private RdCitationDTO convertRdCitation(TrfCitationDTO citation) {
        if (Func.isEmpty(citation)) {
            return null;
        }
        RdCitationDTO rdCitationDTO = new RdCitationDTO();
        rdCitationDTO.setCitationId(citation.getCitationId());
        rdCitationDTO.setCitationType(citation.getCitationType());
        rdCitationDTO.setCitationVersionId(citation.getCitationVersionId());
        rdCitationDTO.setCitationSectionId(citation.getCitationSectionId());
        rdCitationDTO.setCitationSectionName(citation.getCitationSectionName());
        rdCitationDTO.setCitationName(citation.getCitationName());
        rdCitationDTO.setCitationFullName(citation.getCitationFullName());
        rdCitationDTO.setLanguageList(convertRdCitationLang(citation.getLanguageList()));

        return rdCitationDTO;
    }

    private List<RdCitationLanguageDTO> convertRdCitationLang(List<TrfCitationLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(c -> {
            RdCitationLanguageDTO citationLangDTO = new RdCitationLanguageDTO();
            citationLangDTO.setLanguageId(c.getLanguageId());
            citationLangDTO.setCitationSectionName(c.getCitationSectionName());
            citationLangDTO.setCitationName(c.getCitationName());
            citationLangDTO.setCitationFullName(c.getCitationFullName());
            return citationLangDTO;
        }).collect(Collectors.toList());
    }

    private List<RdAnalyteDTO> convertRdTestlineAnalyte(List<TrfAnalyteDTO> analyteList) {
        if (Func.isEmpty(analyteList)) {
            return null;
        }
        return analyteList.parallelStream().map(a -> {
            RdAnalyteDTO rdAnalyteDTO = new RdAnalyteDTO();
            rdAnalyteDTO.setAnalyteInstanceId(a.getAnalyteInstanceId());
            rdAnalyteDTO.setAnalyteBaseId(Func.toLongObject(a.getAnalyteBaseId(), null));
            rdAnalyteDTO.setAnalyteId(a.getAnalyteId());
            rdAnalyteDTO.setAnalyteName(a.getAnalyteName());
            rdAnalyteDTO.setAnalyteSeq(a.getAnalyteSeq());
            rdAnalyteDTO.setAnalyteLimitVersionId(a.getAnalyteLimitVersionId());
            rdAnalyteDTO.setUnitBaseId(a.getUnitBaseId());
            rdAnalyteDTO.setReportUnit(a.getReportUnit());
            rdAnalyteDTO.setCasNo(a.getCasNo());
            rdAnalyteDTO.setLanguageList(convertRdAnalyteLang(a.getLanguageList()));
            return rdAnalyteDTO;
        }).collect(Collectors.toList());
    }

    private List<RdAnalyteLanguageDTO> convertRdAnalyteLang(List<TrfAnalyteLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(a -> {
            RdAnalyteLanguageDTO al = new RdAnalyteLanguageDTO();
            al.setLanguageId(a.getLanguageId());
            al.setAnalyteName(a.getAnalyteName());
            al.setReportUnit(a.getReportUnit());
            return al;
        }).collect(Collectors.toList());
    }

    private RdWiDTO convertRdTestlineWi(TrfWiDTO wi) {
        if (Func.isEmpty(wi)) {
            return null;
        }
        RdWiDTO rdWiDTO = new RdWiDTO();
        rdWiDTO.setWiForCS(wi.getWiForCs());
        rdWiDTO.setWiForSample(wi.getWiForSample());
        rdWiDTO.setWiForTest(wi.getWiForTest());
        return rdWiDTO;
    }


    private List<RdTestSampleDTO> convertRdTestSampleList(List<TrfTestSampleDTO> testSampleList, String orderNo, Integer systemId) {
        if (Func.isEmpty(testSampleList)) {
            return null;
        }
        return testSampleList.stream().map(ts -> {
            RdTestSampleDTO rdTestSampleDTO = new RdTestSampleDTO();
            rdTestSampleDTO.setTestSampleInstanceId(ts.getTestSampleInstanceId());
            rdTestSampleDTO.setParentTestSampleId(ts.getParentTestSampleId());
            rdTestSampleDTO.setTestSampleGroupList(convertRdTestSampleGroupList(ts.getTestSampleGroupList()));
            rdTestSampleDTO.setTestSampleNo(ts.getTestSampleNo());
            rdTestSampleDTO.setTestSampleName(ts.getTestSampleName());
            rdTestSampleDTO.setExternalSampleNo(ts.getExternalSampleNo());
            rdTestSampleDTO.setTestSampleType(ts.getTestSampleType());
            rdTestSampleDTO.setTestSampleSeq(ts.getTestSampleSeq());
            rdTestSampleDTO.setMaterialAttr(convertToRdMaterialAttrList(ts.getMaterialAttrList()));
            rdTestSampleDTO.setConclusion(convertRdConclusion(ts.getConclusion()));
            rdTestSampleDTO.setTestSamplePhoto(convertRdAttachmentList(ts.getTestSamplePhotoList()));
            rdTestSampleDTO.setOrderNo(orderNo);
            rdTestSampleDTO.setSystemId(systemId);
            rdTestSampleDTO.setReportNo(ts.getReportNo());
            return rdTestSampleDTO;
        }).collect(Collectors.toList());
    }

    private List<RdTestSampleGroupDTO> convertRdTestSampleGroupList(List<TrfTestSampleGroupDTO> testSampleGroupList) {
        if (Func.isEmpty(testSampleGroupList)) {
            return null;
        }
        return testSampleGroupList.stream().map(sg -> {
            RdTestSampleGroupDTO rsg = new RdTestSampleGroupDTO();
            rsg.setTestSampleInstanceId(sg.getTestSampleInstanceId());
            rsg.setMainSampleFlag(sg.getMainSampleFlag());
            rsg.setSampleGroupId(sg.getSampleGroupId());
            return rsg;
        }).collect(Collectors.toList());
    }


    private RdMaterialAttrDTO convertToRdMaterialAttrList(List<TrfMaterialAttrDTO> materialAttrList) {
        if (Func.isEmpty(materialAttrList)) {
            return null;
        }
        TrfMaterialAttrDTO trfMaterialAttrDTO = CollUtil.get(materialAttrList, 0);
        RdMaterialAttrDTO rdMaterialAttrDTO = new RdMaterialAttrDTO();
        rdMaterialAttrDTO.setMaterialDescription(trfMaterialAttrDTO.getMaterialDescription());
        rdMaterialAttrDTO.setMaterialOtherSampleInfo(trfMaterialAttrDTO.getMaterialOtherSampleInfo());
        rdMaterialAttrDTO.setMaterialEndUse(trfMaterialAttrDTO.getMaterialEndUse());
        rdMaterialAttrDTO.setApplicableFlag(trfMaterialAttrDTO.getMaterialApplicableFlag());
        rdMaterialAttrDTO.setMaterialRemark(trfMaterialAttrDTO.getMaterialSampleRemark());
        rdMaterialAttrDTO.setMaterialName(trfMaterialAttrDTO.getMaterialName());
        rdMaterialAttrDTO.setMaterialColor(trfMaterialAttrDTO.getMaterialColor());
        rdMaterialAttrDTO.setMaterialTexture(trfMaterialAttrDTO.getMaterialTexture());
        rdMaterialAttrDTO.setExtFields(trfMaterialAttrDTO.getExtFields());
        return rdMaterialAttrDTO;
    }

    private void assembleRdInvoice(TrfFullDTO trfFullDto, ImportReportInvoiceReq importReportInvoiceReq) {
        List<TrfInvoiceDTO> invoiceList = trfFullDto.getInvoiceList();
        List<RdInvoiceDTO> rdInvoiceDTOList = convertRdInvoice(invoiceList);
        importReportInvoiceReq.setInvoiceList(rdInvoiceDTOList);
    }

    private List<RdInvoiceDTO> convertRdInvoice(List<TrfInvoiceDTO> invoiceList) {
        if (Func.isEmpty(invoiceList)) {
            return null;
        }
        return invoiceList.parallelStream().map(i -> {
            RdInvoiceDTO rdInvoiceDTO = new RdInvoiceDTO();
            rdInvoiceDTO.setInvoiceNo(i.getInvoiceNo());
            rdInvoiceDTO.setQuotationNos(i.getQuotationNoList());
            rdInvoiceDTO.setCurrency(i.getCurrency());
            rdInvoiceDTO.setNetAmount(Func.isEmpty(i.getNetAmount()) ? null : new BigDecimal(i.getNetAmount()));
            rdInvoiceDTO.setVatAmount(Func.isEmpty(i.getVatAmount()) ? null : new BigDecimal(i.getVatAmount()));
            rdInvoiceDTO.setTotalAmount(Func.isEmpty(i.getTotalAmount()) ? null : new BigDecimal(i.getTotalAmount()));
            rdInvoiceDTO.setInvoiceStatus(i.getInvoiceStatus());
            rdInvoiceDTO.setInvoiceFileList(convertRdAttachmentList(i.getInvoiceFileList()));
            return rdInvoiceDTO;
        }).collect(Collectors.toList());
    }


    private void assembleQuotationList(TrfFullDTO trfFullDto, ImportQuotationReq importReportDataReq) {
        List<TrfQuotationDTO> quotationList = trfFullDto.getQuotationList();
        List<RdQuotationDTO> rdQuotationDTOList = convertRdQuotationList(quotationList, trfFullDto.getOrder().getOrderNo(), trfFullDto.getHeader().getSystemId());
        importReportDataReq.setQuotationList(rdQuotationDTOList);
    }

    private List<RdQuotationDTO> convertRdQuotationList(List<TrfQuotationDTO> quotationList, String orderNo, Integer systemId) {
        if (Func.isEmpty(quotationList)) {
            return null;
        }
        return quotationList.parallelStream().map(q -> {
            RdQuotationDTO rdQuotationDTO = new RdQuotationDTO();
            rdQuotationDTO.setQuotationNo(q.getQuotationNo());
            RdCustomerDTO payer = convertRdCustomer(q.getPayer());
            rdQuotationDTO.setPayer(payer);
            rdQuotationDTO.setCurrency(q.getCurrency());
            rdQuotationDTO.setNetAmount(Func.isEmpty(q.getNetAmount()) ? null : new BigDecimal(q.getNetAmount()));
            rdQuotationDTO.setVatAmount(Func.isEmpty(q.getVatAmount()) ? null : new BigDecimal(q.getVatAmount()));
            rdQuotationDTO.setTotalAmount(Func.isEmpty(q.getTotalAmount()) ? null : new BigDecimal(q.getTotalAmount()));
            rdQuotationDTO.setDiscount(Func.isEmpty(q.getDiscount()) ? null : new BigDecimal(q.getDiscount()));
            rdQuotationDTO.setAdjustmentAmount(Func.isEmpty(q.getAdjustmentAmount()) ? null : new BigDecimal(q.getAdjustmentAmount()));
            rdQuotationDTO.setFinalAmount(Func.isEmpty(q.getFinalAmount()) ? null : new BigDecimal(q.getFinalAmount()));
            rdQuotationDTO.setQuotationStatus(q.getQuotationStatus());
            rdQuotationDTO.setServiceItemList(convertRdServiceItemList(q.getServiceItemList()));
            rdQuotationDTO.setQuotationFileList(convertRdAttachmentList(q.getQuotationFileList()));
            rdQuotationDTO.setOrderNo(orderNo);
            rdQuotationDTO.setSystemId(systemId);
            return rdQuotationDTO;
        }).collect(Collectors.toList());
    }


    private List<RdServiceItemDTO> convertRdServiceItemList(List<TrfQuotationServiceItemDTO> serviceItemList) {
        if (Func.isEmpty(serviceItemList)) {
            return null;
        }
        return serviceItemList.parallelStream().map(s -> {
            RdServiceItemDTO rdServiceItemDTO = new RdServiceItemDTO();
            rdServiceItemDTO.setServiceItemName(s.getServiceItemName());
            rdServiceItemDTO.setPpNo(s.getPpNo());
            rdServiceItemDTO.setTestLineId(s.getTestLineId());
            rdServiceItemDTO.setCitationId(s.getCitationId());
            rdServiceItemDTO.setCitationType(s.getCitationType());
            rdServiceItemDTO.setCitationName(s.getCitationName());
            rdServiceItemDTO.setEvaluationAlias(s.getEvaluationAlias());
            rdServiceItemDTO.setServiceItemListUnitPrice(Func.isEmpty(s.getServiceItemListUnitPrice()) ? null : new BigDecimal(s.getServiceItemListUnitPrice()));
            rdServiceItemDTO.setServiceItemSalesUnitPrice(Func.isEmpty(s.getServiceItemSalesUnitPrice()) ? null : new BigDecimal(s.getServiceItemSalesUnitPrice()));
            rdServiceItemDTO.setServiceItemDiscount(Func.isEmpty(s.getServiceItemDiscount()) ? null : new BigDecimal(s.getServiceItemDiscount()));
            rdServiceItemDTO.setQuantity(s.getQuantity());
            rdServiceItemDTO.setServiceItemNetAmount(Func.isEmpty(s.getServiceItemNetAmount()) ? null : new BigDecimal(s.getServiceItemNetAmount()));
            rdServiceItemDTO.setServiceItemVATAmount(Func.isEmpty(s.getServiceItemVatAmount()) ? null : new BigDecimal(s.getServiceItemVatAmount()));
            rdServiceItemDTO.setServiceItemTotalAmount(Func.isEmpty(s.getServiceItemTotalAmount()) ? null : new BigDecimal(s.getServiceItemTotalAmount()));
            rdServiceItemDTO.setLanguageList(convertRdServiceItemLang(s.getLanguageList()));
            if (Func.isNotEmpty(s.getExternalInfo())) {
                TrfQuotationServiceItemExternalDTO externalInfo = s.getExternalInfo();
                RdServiceItemExternalDTO rdExternalDTO = new RdServiceItemExternalDTO();
                rdExternalDTO.setTestItemId(externalInfo.getTestItemId());
                rdExternalDTO.setTestItemName(externalInfo.getTestItemId());
                rdExternalDTO.setTestCitationId(Func.toInteger(externalInfo.getTestCitationId(), null));
                rdExternalDTO.setTestCitationName(externalInfo.getTestItemName());
                rdExternalDTO.setLanguageList(convertRdServiceItemExternalLang(externalInfo.getLanguageList()));
                rdServiceItemDTO.setExternalInfo(rdExternalDTO);
            }
            return rdServiceItemDTO;
        }).collect(Collectors.toList());
    }

    private List<RdServiceItemLanguageDTO> convertRdServiceItemLang(List<TrfQuotationServiceItemLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(si -> {
            RdServiceItemLanguageDTO rdServiceItemLanguageDTO = new RdServiceItemLanguageDTO();
            rdServiceItemLanguageDTO.setLanguageId(si.getLanguageId());
            rdServiceItemLanguageDTO.setServiceItemName(si.getServiceItemName());
            return rdServiceItemLanguageDTO;
        }).collect(Collectors.toList());
    }

    private List<RdLanguageDTO> convertRdServiceItemExternalLang(List<TrfQuotationServiceItemExternalLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(e -> {
            RdLanguageDTO rdLanguageDTO = new RdLanguageDTO();
            rdLanguageDTO.setLanguageId(e.getLanguageId());
            rdLanguageDTO.setTestItemName(e.getTestItemName());
            rdLanguageDTO.setTestCitationName(e.getTestCitationName());
            return rdLanguageDTO;
        }).collect(Collectors.toList());
    }


    private void assembleReportOrder(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        assembleOrderBase(rdOrderDTO, trfOrderDTO);

        assembleOrderPayment(rdOrderDTO, trfOrderDTO);

        assembleOrderContactList(rdOrderDTO, trfOrderDTO);

        assembleOrderFlags(rdOrderDTO, trfOrderDTO);

        assembleOrderOthers(rdOrderDTO, trfOrderDTO);

        assembleOrderCustomerList(rdOrderDTO, trfOrderDTO);

        assembleOrderProduct(rdOrderDTO, trfOrderDTO);

        assembleOrderSample(rdOrderDTO, trfOrderDTO);

        assembleOrderRequirement(rdOrderDTO, trfOrderDTO);

        assembleOrderAttachment(rdOrderDTO, trfOrderDTO);
    }

    private void assembleOrderAttachment(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        List<RdAttachmentDTO> attachmentList = convertRdOrderAttachment(trfOrderDTO.getAttachmentList());
        rdOrderDTO.setAttachmentList(attachmentList);
    }

    private List<RdAttachmentDTO> convertRdOrderAttachment(List<TrfFileDTO> attachmentList) {
        if (Func.isEmpty(attachmentList)) {
            return null;
        }
        return attachmentList.parallelStream().map(trfFileDTO -> {
            RdAttachmentDTO rdAttachmentDTO = new RdAttachmentDTO();
            rdAttachmentDTO.setFileName(trfFileDTO.getFileName());
            rdAttachmentDTO.setFileType(trfFileDTO.getFileType());
//            rdAttachmentDTO.setObjectType(trfFileDTO);
//            rdAttachmentDTO.setObjectId();
            rdAttachmentDTO.setCloudId(trfFileDTO.getCloudId());
            rdAttachmentDTO.setFilePath(trfFileDTO.getFilePath());
            rdAttachmentDTO.setToCustomerFlag(trfFileDTO.getToCustomerFlag());
            return rdAttachmentDTO;
        }).collect(Collectors.toList());
    }

    private void assembleOrderSample(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getSampleList())) {
            return;
        }
        List<TrfProductSampleDTO> sampleList = trfOrderDTO.getSampleList();
        List<RdSampleDTO> rdSampleDTOList = sampleList.stream().map(tp -> {
            RdSampleDTO rdSampleDTO = new RdSampleDTO();
            rdSampleDTO.setReportNo(tp.getReportNo());
            rdSampleDTO.setTestSampleInstanceId(tp.getSampleInstanceId());
            rdSampleDTO.setTemplateId(tp.getTemplateId());
            rdSampleDTO.setProductItemNo(tp.getProductItemNo());
            rdSampleDTO.setSampleNo(tp.getSampleNo());
            rdSampleDTO.setExternalSampleNo(tp.getExternalSampleNo());
            rdSampleDTO.setSampleAttrList(convertRdProductAttrList(tp.getSampleAttrList()));
            return rdSampleDTO;
        }).collect(Collectors.toList());
        rdOrderDTO.setSampleList(rdSampleDTOList);
    }

    private void assembleOrderRequirement(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getServiceRequirement())) {
            return;
        }
        TrfServiceRequirementDTO serviceRequirement = trfOrderDTO.getServiceRequirement();
        RdServiceRequirementDTO rdServiceRequirementDTO = new RdServiceRequirementDTO();
        if (Func.isNotEmpty(serviceRequirement.getReport())) {
            TrfServiceRequirementReportDTO report = serviceRequirement.getReport();
            RdServiceRequirementReportDTO rdServiceRequirementReportDTO = new RdServiceRequirementReportDTO();
            rdServiceRequirementReportDTO.setReportLanguage(report.getReportLanguage());
            rdServiceRequirementReportDTO.setReportHeader(report.getReportHeader());
            rdServiceRequirementReportDTO.setReportAddress(report.getReportAddress());
            rdServiceRequirementReportDTO.setAccreditation(report.getReportLanguage());
            rdServiceRequirementReportDTO.setNeedConclusion(report.getNeedConclusion());
            rdServiceRequirementReportDTO.setNeedDraft(report.getNeedDraft());
            rdServiceRequirementReportDTO.setNeedPhoto(report.getNeedPhoto());
            rdServiceRequirementReportDTO.setCertificateRequired(report.getCertificateRequired());
            List<RdReportLanguageDTO> rdReportLanguageDTOList = convertToRdServiceRequirementReportLang(report.getLanguageList());
            rdServiceRequirementReportDTO.setLanguageList(rdReportLanguageDTOList);
            rdServiceRequirementDTO.setReport(rdServiceRequirementReportDTO);
        }
        rdServiceRequirementDTO.setOtherRequestRemark(serviceRequirement.getOtherRequestRemark());

        rdOrderDTO.setServiceRequirement(rdServiceRequirementDTO);
    }

    private List<RdReportLanguageDTO> convertToRdServiceRequirementReportLang(List<TrfServiceRequirementLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(tr -> {
            RdReportLanguageDTO rdReportLanguageDTO = new RdReportLanguageDTO();
            rdReportLanguageDTO.setLanguageId(tr.getLanguageId());
            rdReportLanguageDTO.setReportHeader(tr.getReportHeader());
            rdReportLanguageDTO.setReportAddress(tr.getReportAddress());
            return rdReportLanguageDTO;
        }).collect(Collectors.toList());
    }

    private List<RdProductSampleAttrDTO> convertRdProductAttrList(List<TrfProductAttrDTO> productAttrList) {
        if (Func.isEmpty(productAttrList)) {
            return null;
        }
        return productAttrList.parallelStream().map(sa -> {
            RdProductSampleAttrDTO rdSampleAttrDTO = new RdProductSampleAttrDTO();
            rdSampleAttrDTO.setSeq(sa.getAttrSeq());
            rdSampleAttrDTO.setLabelName(sa.getLabelName());
            rdSampleAttrDTO.setCustomerLabel(sa.getCustomerLabel());
            rdSampleAttrDTO.setDataType(sa.getDataType());
            rdSampleAttrDTO.setLabelCode(sa.getLabelCode());
            rdSampleAttrDTO.setValue(sa.getLabelValue());
//            rdSampleAttrDTO.setDisplayInReport(sa.getDataType());
            rdSampleAttrDTO.setLanguageList(convertRdProductSampleLang(sa.getLanguageList()));
            return rdSampleAttrDTO;
        }).collect(Collectors.toList());

    }

    private List<RdAttrLanguageDTO> convertRdProductSampleLang(List<TrfProductAttrLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(p -> {
            RdAttrLanguageDTO rdAttr = new RdAttrLanguageDTO();
            rdAttr.setLanguageId(p.getLanguageId());
            rdAttr.setCustomerLabel(p.getCustomerLabel());
            rdAttr.setLabelName(p.getLabelName());
            rdAttr.setValue(p.getLabelValue());
            return rdAttr;
        }).collect(Collectors.toList());
    }

    private void assembleOrderBase(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        rdOrderDTO.setSystemId(trfOrderDTO.getSystemId());
        rdOrderDTO.setOrderId(trfOrderDTO.getOrderId());
        rdOrderDTO.setOrderNo(trfOrderDTO.getOrderNo());
        rdOrderDTO.setOriginalOrderNo(trfOrderDTO.getOriginalOrderNo());
        rdOrderDTO.setOrderStatus(trfOrderDTO.getOrderStatus());
        rdOrderDTO.setServiceType(trfOrderDTO.getServiceType());
        rdOrderDTO.setOrderType(trfOrderDTO.getOrderType());
        rdOrderDTO.setOperationType(trfOrderDTO.getOperationType());
        rdOrderDTO.setOperationMode(trfOrderDTO.getOperationMode());
        rdOrderDTO.setProductCategory(trfOrderDTO.getProductCategory());
        rdOrderDTO.setProductSubCategory(trfOrderDTO.getProductSubCategory());
        rdOrderDTO.setGroupId(trfOrderDTO.getGroupId());
        rdOrderDTO.setIdbLab(trfOrderDTO.getIdbLab());
        rdOrderDTO.setTat(trfOrderDTO.getTat());
        rdOrderDTO.setServiceStartDate(trfOrderDTO.getServiceStartDate());
        rdOrderDTO.setTestingStartDate(trfOrderDTO.getTestingStartDate());
        rdOrderDTO.setTestingEndDate(trfOrderDTO.getTestingEndDate());
        rdOrderDTO.setServiceConfirmDate(trfOrderDTO.getServiceConfirmDate());
        rdOrderDTO.setCuttingExpectDueDate(trfOrderDTO.getCuttingExpectDueDate());
        rdOrderDTO.setOrderExpectDueDate(trfOrderDTO.getOrderExpectDueDate());
        rdOrderDTO.setJobExpectDueDate(trfOrderDTO.getJobExpectDueDate());
        rdOrderDTO.setSubcontractExpectDueDate(trfOrderDTO.getSubcontractExpectDueDate());
        rdOrderDTO.setReportExpectDueDate(trfOrderDTO.getReportExpectDueDate());
        rdOrderDTO.setSoftCopyDeliveryDate(trfOrderDTO.getSoftCopyDeliveryDate());
        rdOrderDTO.setCreateBy(trfOrderDTO.getCreateBy());
        rdOrderDTO.setCreateDate(trfOrderDTO.getCreateDate());
    }

    private void assembleOrderFlags(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getFlags())) {
            return;
        }
        TrfOrderFlagsDTO trfOrderFlags = trfOrderDTO.getFlags();
        RdFlagsDTO rdFlagsDTO = new RdFlagsDTO();
        rdFlagsDTO.setSelfTestFlag(trfOrderFlags.getSelfTestFlag());
        rdFlagsDTO.setToDMFlag(trfOrderFlags.getToDMFlag());
        rdOrderDTO.setFlags(rdFlagsDTO);
    }

    private void assembleOrderOthers(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getOthers())) {
            return;
        }
        TrfOrderOthersDTO others = trfOrderDTO.getOthers();
        RdOrderOthersDTO rdOrderOthersDTO = new RdOrderOthersDTO();
        BeanUtil.copyProperties(others, rdOrderOthersDTO, false);
        rdOrderDTO.setOthers(rdOrderOthersDTO);
    }

    private void assembleOrderProduct(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getProduct()) && Func.isEmpty(trfOrderDTO.getProductList())) {
            return;
        }
        List<TrfProductDTO> productDTOS = new ArrayList<>();
        if(Func.isEmpty(trfOrderDTO.getProduct()) && Func.isNotEmpty(trfOrderDTO.getProductList())) {
            productDTOS = trfOrderDTO.getProductList();
        } else {
            productDTOS.add(trfOrderDTO.getProduct());
        }
        List<RdProductDTO> rdProductDTOS = productDTOS.stream()
                .map(product -> {
                    RdProductDTO rdProductDTO = new RdProductDTO();
                    rdProductDTO.setReportNo(product.getReportNo());
                    TrfProductAttrDTO productAttrDTO = CollUtil.get(product.getProductAttrList(), 0);
                    if (Func.isNotEmpty(productAttrDTO)) {
                        rdProductDTO.setProductInstanceId(productAttrDTO.getProductInstanceId());
                    }
                    rdProductDTO.setTemplateId(product.getTemplateId());

                    if (Func.isNotEmpty(product.getProductAttrList())) {
                        List<RdProductSampleAttrDTO> rdProductAttrDTOList = product.getProductAttrList().stream()
                                .map(p -> {
                                    RdProductSampleAttrDTO rdProductAttrDTO = new RdProductSampleAttrDTO();
                                    rdProductAttrDTO.setSeq(p.getAttrSeq());
                                    rdProductAttrDTO.setLabelName(p.getLabelName());
                                    rdProductAttrDTO.setCustomerLabel(p.getCustomerLabel());
                                    rdProductAttrDTO.setDataType(p.getDataType());
                                    rdProductAttrDTO.setLabelCode(p.getLabelCode());
                                    rdProductAttrDTO.setValue(p.getLabelValue());
                                    rdProductAttrDTO.setLanguageList(convertRdProductSampleLang(p.getLanguageList()));
                                    return rdProductAttrDTO;
                                })
                                .collect(Collectors.toList());
                        rdProductDTO.setProductAttrList(rdProductAttrDTOList);
                    }
                    return rdProductDTO;
                })
                .collect(Collectors.toList());
        rdOrderDTO.setProductList(rdProductDTOS);
    }

    private void assembleOrderCustomerList(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getCustomerList())) {
            return;
        }
        List<TrfCustomerDTO> trfCustomerDTOList = trfOrderDTO.getCustomerList();
        List<RdCustomerDTO> rdCustomerList = trfCustomerDTOList.parallelStream().map(tc -> convertRdCustomer(tc)).collect(Collectors.toList());
        rdOrderDTO.setCustomerList(rdCustomerList);
    }

    private RdCustomerDTO convertRdCustomer(TrfCustomerDTO tc) {
        if (Func.isEmpty(tc)) {
            return null;
        }
        RdCustomerDTO rdCustomerDTO = new RdCustomerDTO();
        rdCustomerDTO.setMarketSegmentCode(tc.getMarketSegmentCode());
        rdCustomerDTO.setMarketSegmentName(tc.getMarketSegmentName());
        rdCustomerDTO.setCustomerRefId(tc.getCustomerRefId());
        rdCustomerDTO.setCustomerUsage(tc.getCustomerUsage());
        rdCustomerDTO.setBossNo(tc.getBossNo());
        rdCustomerDTO.setCustomerGroupCode(tc.getCustomerGroupCode());
        rdCustomerDTO.setCustomerName(tc.getCustomerName());
        rdCustomerDTO.setCustomerAddress(tc.getCustomerAddress());
        rdCustomerDTO.setLanguageList(convertRdCustomerLang(tc.getLanguageList()));
        List<RdCustomerContactDTO> rdCustomerContactDTOS = convertRdCustomerContact(tc.getCustomerContactList());
        rdCustomerDTO.setCustomerContactList(rdCustomerContactDTOS);
        return rdCustomerDTO;
    }

    private List<RdCustomerLanguageDTO> convertRdCustomerLang(List<TrfCustomerLangDTO> languageList) {
        if (Func.isEmpty(languageList)) {
            return null;
        }
        return languageList.parallelStream().map(l -> {
            RdCustomerLanguageDTO rdCustomerLanguageDTO = new RdCustomerLanguageDTO();
            rdCustomerLanguageDTO.setLanguageId(l.getLanguageId());
            rdCustomerLanguageDTO.setCustomerName(l.getCustomerName());
            rdCustomerLanguageDTO.setCustomerAddress(l.getCustomerAddress());
            return rdCustomerLanguageDTO;
        }).collect(Collectors.toList());
    }

    private List<RdCustomerContactDTO> convertRdCustomerContact(List<TrfCustomerContactDTO> trfCustomerContactList) {
        if (Func.isEmpty(trfCustomerContactList)) {
            return null;
        }
        return trfCustomerContactList.parallelStream().map(c -> {
            RdCustomerContactDTO rdCustomerContactDTO = new RdCustomerContactDTO();
            rdCustomerContactDTO.setCustomerContactId(c.getCustomerContactId());
            rdCustomerContactDTO.setCustomerContactAddressId(c.getContactAddressId());
            rdCustomerContactDTO.setBossContactId(c.getBossContactId());
            rdCustomerContactDTO.setBossSiteUseId(c.getBossSiteUseId());
            rdCustomerContactDTO.setContactName(c.getContactName());
            rdCustomerContactDTO.setContactTelephone(c.getContactTelephone());
            rdCustomerContactDTO.setContactMobile(c.getContactMobile());
            rdCustomerContactDTO.setContactFAX(c.getContactFax());
            rdCustomerContactDTO.setContactEmail(c.getContactEmail());
            rdCustomerContactDTO.setSgsUserId(c.getSgsUserId());
            rdCustomerContactDTO.setSgsAccountCode(c.getSgsAccountCode());
            return rdCustomerContactDTO;
        }).collect(Collectors.toList());
    }

    private void assembleOrderContactList(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getContactPersonList())) {
            return;
        }
        List<RdContactPersonDTO> rdContactPersonDTOList = trfOrderDTO.getContactPersonList().parallelStream().map(c -> {
            RdContactPersonDTO contactPersonDTO = new RdContactPersonDTO();
            contactPersonDTO.setContactUsage(Func.toStr(c.getContactUsage(), null));
            contactPersonDTO.setContactName(c.getContactName());
            contactPersonDTO.setContactEmail(c.getContactEmail());
            contactPersonDTO.setContactPhone(c.getContactPhone());
            contactPersonDTO.setContactTelephone(c.getContactTelephone());
            contactPersonDTO.setContactMobile(c.getContactMobile());
            contactPersonDTO.setContactFAX(c.getContactFAX());
            contactPersonDTO.setRegionAccount(c.getRegionAccount());
            contactPersonDTO.setResponsibleTeamCode(c.getResponsibleTeamCode());
            return contactPersonDTO;
        }).collect(Collectors.toList());
        rdOrderDTO.setContactPersonList(rdContactPersonDTOList);
    }

    private void assembleOrderPayment(RdOrderDTO rdOrderDTO, TrfOrderDTO trfOrderDTO) {
        if (Func.isEmpty(trfOrderDTO.getPayment())) {
            return;
        }
        TrfPaymentDTO payment = trfOrderDTO.getPayment();
        RdPaymentDTO rdPaymentDTO = new RdPaymentDTO();
        rdPaymentDTO.setPaymentStatus(payment.getPaymentStatus());
        rdPaymentDTO.setCurrency(payment.getCurrency());
        rdPaymentDTO.setTotalAmount(Func.isEmpty(payment.getTotalAmount()) ? null : new BigDecimal(payment.getTotalAmount()));
        rdPaymentDTO.setMainCurrencyTotalAmount(Func.isEmpty(payment.getMainCurrencyTotalAmount()) ? null : new BigDecimal(payment.getMainCurrencyTotalAmount()));
        rdOrderDTO.setPayment(rdPaymentDTO);
    }

    private void assembleRequestBaseModel(BaseModel baseModel, LabInfo labInfo, Integer systemId, String labCodeOfRequest) {
        if (Func.isEmpty(labInfo)) {
            return;
        }
        baseModel.setLabId(NumberUtil.toLongObject(labInfo.getLaboratoryID()));
        baseModel.setLabCode(Func.isEmpty(labInfo.getLaboratoryCode()) ? labCodeOfRequest :labInfo.getLaboratoryCode());
        baseModel.setSystemId(Func.toLongObject(systemId));
        baseModel.setToken(null);
        baseModel.setRequestId(IdUtil.fastSimpleUUID());
//        baseModel.setSourceProductLineCode();
        baseModel.setProductLineCode(labInfo.getProductLineAbbr());
    }

    private void assembleReportHeaderConclusion(RdReportDTO rdHeaderDTO, TrfConclusionDTO reportConclusion) {
        if (Func.isEmpty(reportConclusion)) {
            return;
        }
        RdConclusionDTO rdConclusionDTO = new RdConclusionDTO();
        rdConclusionDTO.setConclusionCode(reportConclusion.getConclusionCode());
        rdConclusionDTO.setCustomerConclusion(reportConclusion.getCustomerConclusion());
        rdConclusionDTO.setReviewConclusion(reportConclusion.getReviewConclusion());
        rdConclusionDTO.setConclusionRemark(reportConclusion.getConclusionRemark());
        rdHeaderDTO.setConclusion(rdConclusionDTO);
    }

    private void assembleReportHeaderLab(RdLabDTO rdLabDTO, LabInfo labInfo, TrfLabDTO labOfRequest) {
        if (Func.isEmpty(labInfo)) {
            return;
        }
        rdLabDTO.setLabId(NumberUtil.toInteger(labInfo.getLaboratoryID()));
        rdLabDTO.setLabCode(labInfo.getLaboratoryCode());
        rdLabDTO.setLocationId(Func.toInteger(labInfo.getLocationID()));
        rdLabDTO.setLocationCode(labInfo.getLocationCode());
        rdLabDTO.setBuId(Func.toInteger(labInfo.getProductLineID()));
        rdLabDTO.setBuCode(labInfo.getProductLineAbbr());

        //SCI-1392
        Optional.ofNullable(labOfRequest).ifPresent(lor -> {
            rdLabDTO.setLabTelephoneNumber(lor.getLabTelephoneNumber());
            rdLabDTO.setLabAddress(lor.getLabAddress());
            rdLabDTO.setLabEmail(lor.getLabEmail());
            rdLabDTO.setLabCountry(lor.getLabCountry());
            rdLabDTO.setLabCity(lor.getLabCity());
            rdLabDTO.setLabName(lor.getLabName());
        });

    }

//    private void assembleReportRequirement(RdReportInfoDTO reportInfoDTO, Long trfId) {
//        TrfServiceRequirementDO trfServiceRequirementDO = serviceRequirementService.getByTrfId(trfId);
//        if (Func.isEmpty(trfServiceRequirementDO)) {
//            log.warn("call RD import：trfId={} serviceRequirement not found", trfId);
//            return;
//        }
//        TrfServiceRequirementReportDO report = trfServiceRequirementDO.getReport();
//        reportInfoDTO.setReportLanguage(Func.toStr(report.getReportLanguage(), null));
//        reportInfoDTO.setReportHeader(report.getReportHeader());
//        reportInfoDTO.setReportAddress(report.getReportAddress());
//
//        List<TrfServiceRequirementLanguageDO> languageList = report.getLanguageList();
//
//        List<RdReportLanguageDTO> reportLanguageList = languageList.parallelStream().map(l -> {
//            RdReportLanguageDTO rdReportLanguageDTO = new RdReportLanguageDTO();
//            rdReportLanguageDTO.setReportHeader(l.getReportHeader());
//            rdReportLanguageDTO.setReportAddress(l.getReportAddress());
//            rdReportLanguageDTO.setLanguageId(Func.toStr(l.getLanguageId(), null));
//            return rdReportLanguageDTO;
//        }).collect(Collectors.toList());
//
//        reportInfoDTO.setLanguageList(reportLanguageList);
//
//
//    }
}
